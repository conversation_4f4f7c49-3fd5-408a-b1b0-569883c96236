import { config } from 'config';
import { useEffect } from 'react';

const Clock = () => {
  const countToDate = new Date(config.publishDate).getTime();
  let previousTimeBetweenDates;

  function flipAllCards(time: any) {
    const seconds = 1000;
    const minutes = seconds * 60;
    const hours = minutes * 60;
    const days = hours * 24;

    const timeDays = Math.floor(time / days);
    const timeHours = Math.floor((time % days) / hours);
    const timeMinutes = Math.floor((time % hours) / minutes);
    const timeSeconds = Math.floor((time % minutes) / seconds);
    // const seconds = time % 60;
    // const minutes = Math.floor(time / 60) % 60;
    // const hours = Math.floor(time / 3600);

    flip(document.querySelector('[data-days-tens]'), Math.floor(timeDays));
    //flip(document.querySelector('[data-days-ones]'), timeDays % 10);

    flip(document.querySelector('[data-hours-tens]'), Math.floor(timeHours));
    // flip(document.querySelector('[data-hours-ones]'), timeHours % 10);
    flip(
      document.querySelector('[data-minutes-tens]'),
      Math.floor(timeMinutes)
    );
    //flip(document.querySelector('[data-minutes-ones]'), timeMinutes % 10);
    flip(
      document.querySelector('[data-seconds-tens]'),
      Math.floor(timeSeconds)
    );
    //flip(document.querySelector('[data-seconds-ones]'), timeSeconds % 10);
  }

  function flip(flipCard: any, newNumber: any) {
    const topHalf = flipCard?.querySelector('.top');
    if (topHalf) {
      const startNumber = parseInt(topHalf?.textContent);
      if (newNumber === startNumber) return;

      const bottomHalf = flipCard?.querySelector('.bottom');
      const topFlip = document.createElement('div');
      topFlip.classList.add('top-flip');
      const bottomFlip = document.createElement('div');
      bottomFlip.classList.add('bottom-flip');

      topHalf.textContent = startNumber;
      bottomHalf.textContent = startNumber;
      topFlip.textContent = startNumber + '';
      bottomFlip.textContent = newNumber;

      topFlip.addEventListener('animationstart', (e) => {
        topHalf.textContent = newNumber;
      });
      topFlip.addEventListener('animationend', (e) => {
        topFlip.remove();
      });
      bottomFlip.addEventListener('animationend', (e) => {
        bottomHalf.textContent = newNumber;
        bottomFlip.remove();
      });
      flipCard.append(topFlip, bottomFlip);
    }
  }

  useEffect(() => {
    setInterval(() => {
      const currentDate = new Date().getTime();
      const timeBetweenDates = countToDate - currentDate;
      //const timeBetweenDates = Math.ceil((countToDate - currentDate) / 1000);
      flipAllCards(timeBetweenDates);

      previousTimeBetweenDates = timeBetweenDates;
    }, 250);
  }, []);

  return (
    <>
      {' '}
      <div className="timer-container mt-5">
        <div className="container-segment">
          <div className="segment">
            <div className="flip-card" data-days-tens>
              <div className="top">1</div>
              <div className="bottom">1</div>
            </div>
            {/* <div className="flip-card" data-days-ones>
              <div className="top">1</div>
              <div className="bottom">9</div>
            </div> */}
          </div>
          <div className="segment-title">Days</div>
        </div>
        <p
          className={`colon ${
            document.body.clientWidth >= 768 ? 'block' : 'hidden'
          }`}
        >
          :
        </p>
        <div className="container-segment">
          <div className="segment">
            <div className="flip-card" data-hours-tens>
              <div className="top">2</div>
              <div className="bottom">2</div>
            </div>
            {/* <div className="flip-card" data-hours-ones>
              <div className="top">4</div>
              <div className="bottom">4</div>
            </div> */}
          </div>
          <div className="segment-title">Hours</div>
        </div>
        <p
          className={`colon ${
            document.body.clientWidth >= 768 ? 'block' : 'hidden'
          }`}
        >
          :
        </p>
        <div className="container-segment">
          <div className="segment">
            <div className="flip-card" data-minutes-tens>
              <div className="top">0</div>
              <div className="bottom">0</div>
            </div>
            {/* <div className="flip-card" data-minutes-ones>
              <div className="top">0</div>
              <div className="bottom">0</div>
            </div> */}
          </div>
          <div className="segment-title">Minutes</div>
        </div>
        <p
          className={`colon ${
            document.body.clientWidth >= 768 ? 'block' : 'hidden'
          }`}
        >
          :
        </p>
        <div className="container-segment">
          <div className="segment">
            <div className="flip-card" data-seconds-tens>
              <div className="top">0</div>
              <div className="bottom">0</div>
            </div>
            {/* <div className="flip-card" data-seconds-ones>
              <div className="top">0</div>
              <div className="bottom">0</div>
            </div> */}
          </div>
          <div className="segment-title">Seconds</div>
        </div>
      </div>
    </>
  );
};

export default Clock;
