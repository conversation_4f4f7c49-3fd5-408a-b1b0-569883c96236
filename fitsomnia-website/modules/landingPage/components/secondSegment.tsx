import { NextComponentType } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import Shopping from '../assets/shopping';
import myImageLoader from 'image/loader';

const SecondSegment: NextComponentType = () => {
  return (
    <>
      {/* <div className="center md:w-100 lg:w-100 h-[100vh] bg-[url('/03_landing.png')] bg-cover bg-no-repeat"> */}
      <div className="center md:w-100 lg:w-100 h-[100vh]">
        <div className="container mx-auto flex flex-row items-center justify-center">
          <div className=" flex w-full flex-col items-center justify-center gap-10 text-center md:flex-row lg:w-[75%] xl:w-[75%]">
            <div className=" flex-1 ">
              <div className=" h-auto w-full px-4 text-left">
                <h1 className="pb-6 text-center font-[Outfit] text-2xl text-[40px] font-bold leading-[50px] text-[#1C2134] md:text-left">
                  Welcome to our activewear store{' '}
                  <span className="text-[#47B042]">“Fitmarket”</span>!
                </h1>

                <p className="pb-6 text-justify font-[Outfit] text-sm font-[400] leading-[168%] text-[#1C2134] md:text-left md:text-base">
                  We have a wide range of high-quality and stylish activewear
                  that will help you look and feel your best during your
                  workouts. From breathable tops to supportive leggings, our
                  selection has everything you need to take your fitness game to
                  the next level. Browse our collection today and find the
                  perfect activewear pieces to help you achieve your fitness
                  goals!
                </p>

                <div
                  className=" flex items-center justify-center rounded-full p-4 font-bold text-white md:w-1/2"
                  style={{
                    background:
                      'linear-gradient(180deg, #03DDE0 0.03%, #03CCE0 15.62%, #1996F2 71.53%, #1965F2 98.14%), linear-gradient(321.25deg, #DF387C -33.39%, #FF602C 103.69%), linear-gradient(90deg, #FFE259 0%, #FFA751 100%)',
                  }}
                >
                  <Link prefetch={false} href="/market" className="">
                    Shop now
                  </Link>
                </div>
              </div>
            </div>

            <div className=" flex-1">
              <Image
                loader={myImageLoader}
                // src={'/Trainingprograms.svg'}
                src={'/market.jpg'}
                alt="Trainingprograms logo"
                // className="h-4 w-10 md:h-3/4 md:w-1/4 xl:h-1/4 2xl:h-3/5 2xl:w-3/5"
                height={250}
                width={350}
                quality={100}
              />
              {/* <Shopping /> */}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SecondSegment;
