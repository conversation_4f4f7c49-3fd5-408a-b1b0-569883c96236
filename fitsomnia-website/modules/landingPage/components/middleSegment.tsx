import myImageLoader from 'image/loader';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';

interface Props {
  imagePosition: string;
  imageURL: string;
  imageHeight: number;
  imageWidth: number;
  headerText: string;
  bodyText: string;
  hasButton?: boolean;
  buttonBg?: string;
  buttonPadding?: string;
  buttonText?: string;
  hasBgColor?: boolean;
  bgColor?: string;
}

const MiddleSegment: React.FC<Props> = ({
  imagePosition,
  imageURL,
  headerText,
  bodyText,
  imageHeight,
  imageWidth,
  hasButton,
  buttonBg,
  buttonPadding,
  buttonText,
  hasBgColor,
  bgColor,
}) => {
  return (
    <>
      {imagePosition === 'left' ? (
        <div className={hasBgColor ? `${bgColor}` : ''}>
          <div
            className={`${
              hasButton ? '' : 'items-center'
            } grid grid-cols-7 gap-x-10 py-3 2xl:gap-x-0`}
          >
            <div className="col-span-3">
              <Image
                loader={myImageLoader}
                className={`my-10 ml-5 md:ml-10 lg:my-20 lg:ml-16 2xl:ml-24`}
                src={imageURL}
                alt="segment image"
                height={imageHeight}
                width={imageWidth}
                quality={100}
              />
            </div>
            <div
              className={`col-span-4 ml-4 mr-5 md:pl-5 md:pr-20 lg:ml-12 xl:ml-16
               ${
                 hasButton
                   ? 'mt-5 md:mt-16 lg:mt-32 lg:pr-14 xl:mt-36 2xl:mt-44 2xl:ml-24 2xl:pr-28'
                   : 'lg:mr-6 xl:mr-14 2xl:mr-52 2xl:ml-32'
               }`}
            >
              <p
                className={`mb-2 font-semibold text-fit_header_text ${
                  hasButton
                    ? 'tracking-tight lg:text-2xl xl:text-3xl 2xl:text-5xl'
                    : 'lg:text-xl xl:text-[25.5px] 2xl:text-4xl'
                }`}
              >
                {headerText}
              </p>
              <p
                className={`mb-3 break-normal text-[8px] text-fit_body_text md:text-xs 2xl:text-base ${
                  hasButton ? 'tracking-tight' : ''
                }`}
              >
                {bodyText}
              </p>
              {hasButton ? (
                <Link prefetch={false} href="/market" passHref>
                  <button
                    className={`${buttonBg} ${buttonPadding} text-[10px] font-medium lg:text-xs`}
                  >
                    {buttonText}
                  </button>
                </Link>
              ) : (
                <></>
              )}
            </div>
          </div>
        </div>
      ) : (
        <div>
          <div className="grid grid-cols-7 items-center gap-x-6">
            <div className="col-span-4 ml-5 md:mr-12 md:ml-24 lg:mr-20 lg:ml-32 xl:ml-36 xl:mr-28 2xl:mr-44 2xl:ml-72">
              <p className="mb-2 font-semibold text-fit_header_text lg:text-xl xl:text-[25.5px] 2xl:text-4xl">
                {headerText}
              </p>
              <p
                className={`mb-3 break-normal text-[8px] text-fit_body_text md:text-xs  2xl:text-base ${
                  hasButton ? 'tracking-tight' : ''
                }`}
              >
                {bodyText}
              </p>
              {hasButton ? (
                <button className={`${buttonBg} ${buttonPadding} text-sm`}>
                  {buttonText}
                </button>
              ) : (
                <></>
              )}
            </div>

            <div className="col-span-3">
              <Image
                loader={myImageLoader}
                className="float-right my-5 mr-5 md:mr-10 lg:my-20 lg:mr-16 xl:mr-24 2xl:mr-32"
                src={imageURL}
                alt="segment image"
                height={imageHeight}
                width={imageWidth}
                quality={100}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default MiddleSegment;
