import { userAPI } from 'APIs';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import myImageLoader from 'image/loader';
import { NextComponentType } from 'next';
import Image from 'next/image';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { subscriberSchema } from '../schema/subscriber.schema';
import Clock from './clock';
import Stripes from '../assets/stripes';

const TimeSegment: NextComponentType = () => {
  const [releasetime, setreleasetime] = useState(false);
  const [days, setDays] = useState(0);
  const [hours, setHours] = useState(0);
  const [minutes, setMinutes] = useState(0);
  const [seconds, setSeconds] = useState(0);

  const handleSubmit = async (email: string) => {
    try {
      const res = await userAPI.joinWaitList(email);
      if ('data' in res) {
        toast.success('Join request submitted successfully.', {
          containerId: 'bottom-right',
        });
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
  };

  return (
    <>
      {' '}
      <div className="relative mb-20 md:mt-20">
        {' '}
        {/* {releasetime ? (
          <h1>Fitsomnia is coming out soon</h1>
        ) : ( */}{' '}
        <div className="container mx-auto flex  flex-col justify-center">
          {' '}
          <div
            className="relative w-[100%] text-center"
            style={{
              background:
                'linear-gradient(90.01deg, rgba(0, 140, 118, 0.45) -20.11%, rgba(108, 214, 93, 0.45) 99.99%), linear-gradient(0deg, #6AB557, #6AB557), linear-gradient(90.01deg, #008C76 -20.11%, #6CD65D 99.99%)',
              border: '10px solid rgba(255, 255, 255, 0.13)',
              boxShadow: '0px 34px 90px rgba(60, 180, 109, 0.3)',
              borderRadius: '12px',
            }}
          >
            {/* <Image
              src="/stripes.svg"
              alt="bg"
              height={100}
              width={100}
              loader={myImageLoader}
              className="absolute h-full w-full object-fill opacity-100 mix-blend-lighten"
            /> */}
            <Stripes />
            <div className="p-6 md:p-16">
              <div>
                {' '}
                <h1 className="pb-2 text-3xl font-bold text-white">
                  &quot;Be Strong, Be Inspired, Be You&quot;
                </h1>{' '}
                <p className="font-bold text-white">Join Fitsomnia today </p>{' '}
              </div>{' '}
              {/* <Clock /> */}
              <div className="relative mt-4">
                {' '}
                <Formik
                  initialValues={{
                    email: '',
                  }}
                  onSubmit={(values, actions) => {
                    handleSubmit(values.email);
                    actions.setSubmitting(false);
                    actions.resetForm();
                  }}
                  validationSchema={subscriberSchema}
                >
                  {(formikprops) => {
                    return (
                      <Form onSubmit={formikprops.handleSubmit}>
                        <Field
                          id="email"
                          type="text"
                          name="email"
                          placeholder="Enter your e-mail address.."
                          className="relative w-full rounded-md border-b border-black p-3 text-left text-xs focus:outline-none md:w-2/3 md:text-base"
                        ></Field>
                        <button
                          className="absolute top-[50%] right-1 flex translate-y-[-50%] items-center justify-center rounded px-2 py-1 text-xs text-white md:right-[18%] md:px-4 md:text-base"
                          style={{
                            background:
                              'linear-gradient(180deg, #03DDE0 0.03%, #03CCE0 15.62%, #1996F2 71.53%, #1965F2 98.14%), linear-gradient(90deg, #FFE259 0%, #FFA751 100%), #FF7245',
                          }}
                        >
                          Submit
                        </button>
                        <div className="errMsg absolute left-[20%] text-red-600">
                          <ErrorMessage name="email" />
                        </div>
                      </Form>
                    );
                  }}
                </Formik>
              </div>{' '}
            </div>
          </div>
        </div>
        {/* )} */}{' '}
      </div>{' '}
    </>
  );
};

export default TimeSegment;
