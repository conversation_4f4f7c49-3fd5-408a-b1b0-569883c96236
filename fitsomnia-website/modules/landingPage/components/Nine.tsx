import Logo from '@/modules/landingPage/assets/landing/logo.png';
import { socials } from '@/modules/landingPage/constant/Constant';
import Image from 'next/image';
import Link from 'next/link';
export const Footer = () => {
  return (
    <>
      <div className="mt-8 mb-14 px-3 md:mx-24 md:mt-24 lg:container lg:mx-auto lg:mt-14 lg:w-3/4">
        {/* <div className="flex justify-between flex-row-reverse items-center "> */}
        <div>
          <div className="mb-2 flex items-center gap-2">
            <Image src={Logo} alt="logo" className="h-[40px] w-[40px]" />
            <p className="text-8 text-primary">FITSOMNIA</p>
          </div>
          <div className="sm:flex sm:items-center sm:justify-between ">
            <p>© 2024 by Fitsomnia LLC. All Rights Reserved.</p>
            <div className="mt-2 gap-6 sm:mt-0 sm:flex ">
              <p>Home</p>
              <p>About</p>
              <p>Features</p>
              <p>Contact</p>
            </div>
          </div>

          <div className="mt-4 flex flex-col-reverse gap-4 sm:flex-row sm:items-center sm:justify-between   ">
            <div className="font-6 flex gap-6 text-base  font-normal">
              <p
                onClick={() => window.open('/privacy-policy?prev=landing')}
                className="cursor-pointer hover:text-primary"
              >
                Privacy & Policy
              </p>
              <p
                onClick={() => window.open('/terms-of-service?prev=landing')}
                className="cursor-pointer hover:text-primary"
              >
                Terms & Conditions
              </p>
            </div>
            <div className="flex items-start  gap-4 sm:justify-between">
              {socials.map((social) => (
                <Link href={social.link} key={social.title}>
                  <Image
                    src={social.logo}
                    className="h-[40px] w-[40px]"
                    alt={social.title}
                  />
                </Link>
              ))}
            </div>
          </div>
        </div>

        {/* <div className="flex flex-col sm:gap-0 gap-4 mt-4 sm:flex-row-reverse sm:items-center sm:justify-between bg-indigo-700 "> */}
      </div>
    </>
  );
};
