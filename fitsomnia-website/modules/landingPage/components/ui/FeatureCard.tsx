import Image, { StaticImageData } from 'next/image';
type FeatureCardProps = {
  logo: string | StaticImageData;
  title: string;
  description: string;
  image: string | StaticImageData;
  titleSize?: string;
};

const FeatureCard: React.FC<FeatureCardProps> = ({
  logo,
  title,
  description,
  image,
  titleSize = 'text-2xl xl:text-4xl',
}) => {
  return (
    <div className="rounded-lg bg-[#F6F7F9]">
      <div className="flex flex-col justify-items-center p-6  ">
        <div className="mb-2 flex items-center gap-2">
          <Image src={logo} alt="feature logo" height={36} width={36} />
          <p className={`font-bold ${titleSize} `}>{title}</p>
        </div>
        <p className="md:max-w-96 text-left text-base">{description}</p>
      </div>
      <div>
        <Image
          src={image}
          //   className="xl:h-[547px] xl:w-[434px]"
          alt="feature image"
        />
      </div>
    </div>
  );
};

export default FeatureCard;
