import myImageLoader from 'image/loader';
import { NextComponentType } from 'next';
import Image from 'next/image';

const FifthSegment: NextComponentType = () => {
  return (
    <>
      <div className="center  bg-emerald-50 bg-cover bg-no-repeat">
        <div className="container mx-auto flex flex-row items-center justify-center">
          <div className=" flex w-full flex-col items-center justify-center gap-10 text-center md:flex-row lg:w-[85%] xl:w-[75%]">
            <div className=" flex-1 ">
              <div className=" h-auto w-full px-4 text-left">
                <h1 className="pb-6 text-center font-[Outfit] text-2xl text-[40px] font-bold leading-[50px] text-[#1C2134] md:text-left">
                  Training <span className="text-[#47B042]">programs</span>
                </h1>
                <p className="pb-6 text-justify font-[Outfit] text-sm font-[400] leading-[168%] text-[#1C2134] md:text-left md:text-lg ">
                  With our training program feature, you can access a variety of
                  workout plans that are designed to help you achieve your
                  fitness goals, whether it is building strength, increasing
                  endurance, or losing weight. Try out the different programs,
                  find one that works best for you, and start working towards
                  your fitness goals today. With the help of Fitsomnia workout
                  programs, you can take your fitness journey to the next level{' '}
                </p>
                <p className="pb-6"> </p>
              </div>
            </div>
            <div className=" flex-1">
              <Image
                loader={myImageLoader}
                // src={'/Trainingprograms.svg'}
                src={'/trainer.jpg'}
                alt="Trainingprograms logo"
                // className="h-4 w-10 md:h-10 md:w-24 xl:h-12 2xl:h-14 2xl:w-32"
                height={250}
                width={350}
                quality={100}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default FifthSegment;
