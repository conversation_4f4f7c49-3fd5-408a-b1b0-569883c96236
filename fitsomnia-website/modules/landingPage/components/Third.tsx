import { news } from '@/modules/landingPage/constant/Constant';
import Image from "next/image";
import Link from "next/link";

export const NewsPage = () => {
	return (
		<>
			<div className=" grid justify-items-center mx-auto mt-10 md:w-full">
				<p className="text-3xl font-bold sm:text-[40px] ">
					{" "}
					<span className="text-primary">Fitsomnia</span> Featured in
				</p>
				<p className="text-base sm:text-2xl">
					our app is the talk of the town now
				</p>
				<div className="flex flex-wrap justify-center gap-10 mt-6 max-w-xl md:max-w-6xl">
					{news.map((item) => {
						return (
							<Link href={item.link} key={item.label}>
								<Image
									src={item.image}
									alt={item.label}
									className="w-28 sm:w-36 lg:w-64 h-10 lg:h-16"
								/>
							</Link>
						);
					})}
				</div>
			</div>
		</>
	);
};
