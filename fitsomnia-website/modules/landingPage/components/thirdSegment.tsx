import myImageLoader from 'image/loader';
import { NextComponentType } from 'next';
import Image from 'next/image';
import SpotMe from '../assets/soptMe';

const ThirdSegment: NextComponentType = () => {
  return (
    <>
      <div className="center lg:w-100 mt-20  ">
        <div className="container mx-auto flex flex-row items-center justify-center">
          <div className=" flex w-full flex-col items-center justify-center gap-10 text-center md:flex-row lg:w-[75%] xl:w-[75%]">
            <div className=" flex-1 ">
              <div className=" h-auto w-full px-4 text-left">
                <h1 className="pb-6 text-center font-[Outfit] text-3xl text-[50px] font-bold leading-[50px] text-[#1C2134] md:text-left">
                  Spot <span className="text-[#47B042]">me</span>
                </h1>

                <p className="pb-6 text-justify font-[Outfit] text-sm font-[400] leading-[168%] text-[#1C2134] md:text-left md:text-lg">
                  Giving someone a spot at the gym can be a great way to support
                  and motivate them in their fitness journey. It shows that you
                  are willing to help them push themselves to new limits and
                  achieve their goals. Remember to communicate clearly and
                  provide guidance on proper form and technique to help prevent
                  injuries. With your support, they may just surprise themselves
                  with what they are capable of achieving!{' '}
                </p>
              </div>
            </div>
            <div className=" flex-1">
              {/* <Image
                loader={myImageLoader}
                src={'/spot-me.svg'}
                alt="spot-me"
                //className="h-4 w-10 md:h-10 md:w-24 xl:h-12 2xl:h-14 2xl:w-32"
                height={750}
                width={700}
                quality={100}
              /> */}
              {/* <SpotMe /> */}
              <Image
                loader={myImageLoader}
                // src={'/Trainingprograms.svg'}
                src={'/spot.jpg'}
                alt="Spot logo"
                //  className="h-4 w-10 md:h-3/4 md:w-1/4 xl:h-1/4 2xl:h-3/5 2xl:w-3/5"
                height={250}
                width={350}
                quality={100}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ThirdSegment;
