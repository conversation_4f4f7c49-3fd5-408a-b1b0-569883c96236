import { NextComponentType } from 'next';
// import EighthSegment from '@/modules/landingPage/components/eighthSegment';
// import FifthSegment from '@/modules/landingPage/components/fifthSegment';
// import Footer from '@/modules/landingPage/components/footer';
// import FourthSegment from '@/modules/landingPage/components/fourthSegment';
// import SecondSegment from '@/modules/landingPage/components/secondSegment';
// import SeventhSegment from '@/modules/landingPage/components/seventhSegment';
// import SixthSegment from '@/modules/landingPage/components/sixthSegment';
// import ThirdSegment from '@/modules/landingPage/components/thirdSegment';
// //import TimeSegment from '@/modules/landingPage/components/timeSegment';
// import TimeSegment from '@/modules/landingPage/components/timeSegment';
// import TopSegment1 from '@/modules/landingPage/components/topSegment1';
// import LazyLoad from 'react-lazyload';
import { AppOverviewPage } from './components/Eight';
import { HeaderPage } from './components/First';
import { FeaturePage } from './components/Five';
// import Footer from './components/footer';
import { MiddlePage } from './components/Fourth';
import { Footer } from './components/Nine';
import { HeroPage } from './components/Second';
import { CommunityPage } from './components/Seven';
import { NewsPage } from './components/Third';

const LandingPageComponent: NextComponentType = () => {
  return (
    <>
      {/* <div className="overflow-y-auto">
        <TopSegment1 />
        <LazyLoad offset={100}>
          <TimeSegment />

          <ThirdSegment />
          <FourthSegment />
          <br />
          <br />
          <FifthSegment />
          
          <br />
          <SixthSegment />
          <br/>
          <SecondSegment />
          <SeventhSegment />
          <EighthSegment />
          <br />
          <Footer />
        </LazyLoad>
      </div> */}
      <div>
        <HeaderPage />
        <HeroPage />
        <NewsPage />
        <MiddlePage />
        <FeaturePage />
        {/* <ReviewPage /> */}
        <CommunityPage />
        <AppOverviewPage />
        <Footer />
      </div>
    </>
  );
};
export default LandingPageComponent;
