const Landing01 = () => {
  return (
    <>
      <svg
        width="1920"
        height="920"
        viewBox="0 0 1920 920"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <mask
          id="mask0_42_66"
          style={{ maskType: 'alpha' }}
          maskUnits="userSpaceOnUse"
          x="-1"
          y="-456"
          width="1921"
          height="1328"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M-0.455252 -455.309L1919.54 -455.309L1919.54 413.141C1868.71 461.297 1809.35 513.959 1748.29 558.812C1632.16 644.128 1513.67 700.628 1384.82 700.335C1336.37 700.225 1286.99 692.006 1237.67 683.8C1155.83 670.18 1074.18 656.591 997.305 680.151C941.256 697.328 888.522 734.135 835.82 770.919C772.681 814.989 709.588 859.026 640.897 869.235C561.497 881.036 476.111 847.419 390.513 813.718C340.245 793.927 289.905 774.108 240.662 763.442C149.823 743.765 64.4691 754.975 -0.455257 770.122L-0.455252 -455.309Z"
            fill="url(#paint0_linear_42_66)"
          />
        </mask>
        <g mask="url(#mask0_42_66)">
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M-0.455252 -455.309L1919.54 -455.309L1919.54 413.141C1868.71 461.297 1809.35 513.959 1748.29 558.812C1632.16 644.128 1513.67 700.628 1384.82 700.335C1336.37 700.225 1286.99 692.006 1237.67 683.8C1155.83 670.18 1074.18 656.591 997.305 680.151C941.256 697.328 888.522 734.135 835.82 770.919C772.681 814.989 709.588 859.026 640.897 869.235C561.497 881.036 476.111 847.419 390.513 813.718C340.245 793.927 289.905 774.108 240.662 763.442C149.823 743.765 64.4691 754.975 -0.455257 770.122L-0.455252 -455.309Z"
            fill="#6AB557"
          />
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M-0.455252 -455.309L1919.54 -455.309L1919.54 413.141C1868.71 461.297 1809.35 513.959 1748.29 558.812C1632.16 644.128 1513.67 700.628 1384.82 700.335C1336.37 700.225 1286.99 692.006 1237.67 683.8C1155.83 670.18 1074.18 656.591 997.305 680.151C941.256 697.328 888.522 734.135 835.82 770.919C772.681 814.989 709.588 859.026 640.897 869.235C561.497 881.036 476.111 847.419 390.513 813.718C340.245 793.927 289.905 774.108 240.662 763.442C149.823 743.765 64.4691 754.975 -0.455257 770.122L-0.455252 -455.309Z"
            fill="url(#paint1_linear_42_66)"
            fill-opacity="0.45"
          />
          <path
            opacity="0.8"
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M-118.856 822.756L-59.7324 799.624C1.24672 776.076 119.494 729.813 254.345 748.815C389.195 767.817 536.938 852.916 661.254 825.003C785.571 797.089 882.75 656.996 1002.85 610.316C1122.96 563.637 1262.27 611.204 1390.8 602.057C1519.33 592.91 1633.36 527.881 1742.93 434.286C1852.5 340.691 1953.89 219.364 2006.45 158.283L2057.14 97.6197L2183.55 660.602L2122.32 674.351C2059.24 688.516 1936.78 716.013 1812.46 743.926C1688.14 771.84 1565.68 799.337 1441.36 827.25C1317.05 855.164 1194.59 882.66 1070.27 910.574C945.955 938.487 823.494 965.984 699.177 993.898C574.861 1021.81 452.4 1049.31 328.084 1077.22C203.767 1105.13 81.3062 1132.63 18.2203 1146.8L-43.0102 1160.55L-118.856 822.756Z"
            fill="white"
          />
        </g>
        <defs>
          <linearGradient
            id="paint0_linear_42_66"
            x1="1919.55"
            y1="314.692"
            x2="-20.9195"
            y2="-222.135"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#7B5AB6" />
            <stop offset="1" stop-color="#33148F" />
          </linearGradient>
          <linearGradient
            id="paint1_linear_42_66"
            x1="-445.645"
            y1="267.3"
            x2="1961.32"
            y2="79.038"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#008C76" />
            <stop offset="1" stop-color="#6CD65D" />
          </linearGradient>
        </defs>
      </svg>
    </>
  );
};

export default Landing01;
