import React, { useState } from 'react';

import radio from 'styles/radioButton.module.css';

const RatingTypeOptions: React.FC = () => {
  const availableOptions = [
    { id: 1212, meta: { name: 'above 4.0' } },
    { id: 2323, meta: { name: '5.0' } },
  ];
  const [availibityColorOptionVal, setavailibityColorOptionVal] = useState('');
  return (
    <>
      {/* <div className="py-4"><CounterElement /></div> */}
      <div className={radio.custom}>
        {availableOptions.map((option) => {
          return (
            <div key={option.id}>
              <div className="flex justify-between py-1">
                <input
                  id={option.id + ''}
                  type="radio"
                  name={option.meta.name}
                  value={option.meta.name}
                  onChange={(e) => setavailibityColorOptionVal(e.target.value)}
                />
                <label
                  htmlFor={option.id + ''}
                  className="flex cursor-pointer items-center"
                >
                  {option.meta.name}
                </label>
              </div>
            </div>
          );
        })}
      </div>
    </>
  );
};

export default RatingTypeOptions;
