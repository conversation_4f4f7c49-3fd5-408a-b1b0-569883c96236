import React, { useState } from 'react';

import radio from 'styles/radioButton.module.css';

const SizeTypeOptions: React.FC = () => {
  const availableOptions = [
    { id: 111, meta: { name: 'XS' } },
    { id: 222, meta: { name: 'S' } },
    { id: 333, meta: { name: 'M' } },
    { id: 444, meta: { name: 'L' } },
    { id: 555, meta: { name: 'XL' } },
    { id: 666, meta: { name: 'XXL' } },
    { id: 777, meta: { name: 'XXXL' } },
  ];
  const [availibityColorOptionVal, setavailibityColorOptionVal] = useState('');
  return (
    <>
      {/* <div className="py-4"><CounterElement /></div> */}
      <div className={radio.custom}>
        {availableOptions.map((option) => {
          return (
            <div key={option.id}>
              <div className="flex justify-between py-1">
                <input
                  id={option.id + ''}
                  type="radio"
                  name={option.meta.name}
                  value={option.meta.name}
                  onChange={(e) => setavailibityColorOptionVal(e.target.value)}
                />
                <label
                  htmlFor={option.id + ''}
                  className="flex cursor-pointer items-center"
                >
                  {option.meta.name}
                </label>
              </div>
            </div>
          );
        })}
      </div>
    </>
  );
};

export default SizeTypeOptions;
