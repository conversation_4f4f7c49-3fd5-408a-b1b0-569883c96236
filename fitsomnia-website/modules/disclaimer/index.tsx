import { accordionBody } from 'APIs/utils/types';
import Breadcrumb from '../common/breadcrumbs/breadcrumb';
import DisclaimerContentArea from './components/contentArea';

const DisclaimerComponent = () => {
  const accordionList: accordionBody[] = [
    {
      id: '1',
      title: 'NOT PROFESSIONAL ADVICE',
      body: 'This website does not contain professional advice, nor is any professional-client relationship established with you through your use of this website.  Any information found on or derived from this website should not be a substitute for and cannot be relied on as any legal, tax, real estate, medical, financial, risk management, marital or other professional advice.  If you require any such advice, please consult with a licensed or knowledgeable professional in that area before taking any action.',
    },
    {
      id: '2',
      title: 'YOUR RESPONSIBILITY',
      body: 'It is your responsibility to take all necessary steps to independently verify and ascertain that any information you choose to rely on from, access through or take action based upon this website or our Products is accurate, as we are not responsible for your use of the information obtained from or accessed through this website or our Products.  Any views expressed on this website are solely the personal views of the author and do not necessarily reflect the views of the Company.',
    },
    {
      id: '3',
      title: 'EARNINGS DISCLAIMER',
      body: 'While we may, on this website, through any of our Products or in our communications, reference certain results or outcomes, you agree and acknowledge that information about these results or outcomes are received from third parties and we have no control over the accuracy of such statements, nor is there any guarantee that you can achieve the same results or outcomes.  You agree and acknowledge that these results are not guaranteed or typical in any way and that individual outcomes may vary.  Please do not rely on these results or outcomes in using the website or purchasing any Products.',
    },
    {
      id: '4',
      title: 'THIRD PARTY LINKS',
      body: 'Our website may contain links to third party websites, for example, through hyperlinks we provide or through banners or advertisements, solely as a convenience to you. However, we are not responsible for any content found on or accessed through any links to third party websites.  Any links to third party websites we provide do not serve as endorsements by us for the third-party website or any of the products or services you may find on such website.  We have no control over third parties and assume no responsibility for any third-party websites or any of the products or services you may find on such websites, even if you access a third-party website through a link on this website.  If you choose to access a third-party website, it is solely at your own risk.',
    },
    {
      id: '5',
      title: 'TESTIMONIALS DISCLAIMER',
      body: 'This website may contain testimonials by users of our Products.  Each testimonial reflects solely the personal view, opinion or experience of the individual providing the testimonial and does not reflect our views or opinions.  You should not rely on any testimonial as indicative of a certain result or outcome.  We do not claim, nor should you assume that your use of our Products will lead to the same result or outcome.  We also do not independently verify, nor can we guarantee the accuracy of any information provided in such testimonials. Except for correcting spelling and grammatical errors, each testimonial appears verbatim as we have received it.  We do not pay or provide any form of compensation to individuals for providing testimonials. ',
    },
    {
      id: '6',
      title: 'AFFILIATE DISCLAIMER',
      body: 'This website may contain links to affiliate websites.  When you click on and/or make a purchase through an affiliate link placed on our website, we may receive a small commission or other form of compensation at no additional cost to you.  Please assume that any links contained on our website are affiliate links.  Our use of affiliate links does not influence the products, services and websites that we share with you.  This Disclaimer applies to all of the various means we use to communicate with you, including via this website, email, phone, social media, our Products or otherwise.',
    },
    {
      id: '7',
      title: 'SPONSORED POSTS',
      body: `This website may contain sponsored posts or reviews, where we receive a form of compensation in exchange for publishing a post or a review of a product or service.  We may also receive a small commission or other form of compensation at no additional cost to you if you click on and/or make a purchase through an affiliate link in a sponsored post or review.`,
    },
    {
      id: '8',
      title: 'FAIR USE DISCLAIMER',
      body: 'We may use copyrighted material on our website without specific authorization.  In these instances, we do so because we believe such use constitutes fair use of any such copyrighted material under Section 107 of the United States copyright law.',
    },
    {
      id: '9',
      title: 'CHANGES TO THE DISCLAIMER',
      body: 'We reserve the right to amend this Disclaimer at any time without notice to you.  We will alert you to any changes by posting the effective date of the latest version at the top of this page, at which point any changes will become immediately effective.  It is your responsibility to check for updates, as your continued use of the website and our Products after this Disclaimer is amended will constitute your acceptance and agreement to continue to be bound by this Disclaimer, as amended.',
    },
  ];

  const sendEmail = () => {
    window.open(`mailto:<EMAIL>`);
  };

  return (
    <>
      <Breadcrumb
        title="Disclaimer"
        pathArray={['Home', 'Disclaimer']}
        linkArray={['/', '/disclaimer']}
      />
      <div className="container mx-auto p-4">
        <div className="mx-0 flex flex-col items-center justify-center lg:mx-52">
          <h1 className="mt-10 text-xl font-bold lg:text-5xl">Disclaimer</h1>
          <p>Last Updated on 04/14/2023</p>
          <p className="mx-3 mt-5 text-justify">
            <p className="italic">
              NOTICE: Please read the Disclaimer set forth below, which is
              legally binding. By visiting, viewing or using this App and
              website and and/or by using any program, product, course or
              service from us, you agree to be bound by this Disclaimer and our
              Privacy Policy and Terms and Conditions.
            </p>
            <br />
            <p>
              Fitsomnia [Website URL (i.e.,{' '}
              <span
                className="cursor-pointer text-primary"
                onClick={() => window.open('https://fitsomnia.com')}
              >
                https://Fitsomnia.com
              </span>
              )], which is operated by [Fitsomnia LLC (“Company”, “we”, “us”, or
              “our”) provides visitors information on the website as a public
              service, subject to the following terms and conditions
              (“Disclaimer”). The term “you” refers to any visitor, viewer or
              user of the website and/or any user of any free or paid program,
              product, course or service of the Company (each, a “Product”).
            </p>
            <br />
            <p className="font-semibold">GENERAL DISCLAIMER</p>
            <p>
              The content on this website is provided to you “as is” and is
              intended to serve as general information. While we strive to
              provide you with quality content, we give no representation or
              warranty that the content is accurate, complete, updated, timely,
              relevant or free from typographical, technical, informational or
              pricing errors and omissions, whether negligent or otherwise. By
              using this website or any Products, you agree and acknowledge that
              your use of this website and use of any Products is solely at your
              own risk. This Disclaimer was created with the help of{' '}
              <span
                onClick={() =>
                  window.open('https://plugandlaw.com/legal-bundle/')
                }
                className="cursor-pointer text-primary"
              >
                Plug and Law
              </span>{' '}
              and{' '}
              <span
                onClick={() =>
                  window.open('https://plugandlaw.com/marketpage/')
                }
                className="cursor-pointer text-primary"
              >
                Privacy Policy Solutions
              </span>
              .
            </p>
          </p>
          <DisclaimerContentArea accordionList={accordionList} />
        </div>
        <p className="mx-3 mt-5 text-justify lg:mx-52">
          <p className="font-semibold">HOW TO CONTACT US</p>
          <p>
            If you have any questions, please contact us using the information
            below.
          </p>
          <ul className="list-disc text-start">
            <li className="ml-10">
              By email:{' '}
              <span className="cursor-pointer text-primary" onClick={sendEmail}>
                <EMAIL>
              </span>
            </li>
          </ul>
        </p>
      </div>
    </>
  );
};

export default DisclaimerComponent;
