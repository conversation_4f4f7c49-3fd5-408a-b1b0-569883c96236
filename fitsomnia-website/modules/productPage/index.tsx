import React, { useState } from 'react';

import { userAPI } from 'APIs';
import {
  ICompareItems,
  Product,
  ProductPhoto,
  ResponseItem,
  WishlistItem,
} from 'models';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import { useAppDispatch, useAppSelector } from 'store/hooks/index';
import { addToCart, storeAllCartItems } from 'store/slices/cartSlice';
import {
  storeCompare,
  storeProductsToComparePublic,
} from 'store/slices/compareSlice';
import { setLoginModalState, setModalState } from 'store/slices/modalSlice';
import {
  deleteItemFromWishlist,
  storeWishlist,
} from 'store/slices/productsSlice';

import Breadcrumb from '@/modules/common/breadcrumbs/breadcrumb';
import TextButton from '@/modules/common/buttons/textButton';
import CartToast from '@/modules/common/toast/cartToast';
import BuyProductQuantity from '@/modules/productPage/components/buyProductQuatity';
import ProductDescription from '@/modules/productPage/components/productDescription';
import ProductImagesSlider from '@/modules/productPage/components/productImageSlider';
import RatingStars from '@/modules/productPage/components/ratingStars';
import SimilarProducts from '@/modules/productPage/components/similarProducts';
import CircleIcon from '@/modules/common/icons/circleIcon';
interface SingleProduct {
  product: Product;
}

const ProductDetailsComponent: React.FC<SingleProduct> = ({
  product,
}: SingleProduct) => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const { t } = useTranslation();
  const currency = useAppSelector((state) => state.persistedReducer.currency);

  const [modalOn, setModalOn] = useState(false);

  const cartData = useAppSelector(
    (state) => state.persistedReducer.cart.allCartItems
  );

  const wishlistData = useAppSelector(
    (state) => state.persistedReducer.product.wishlist
  );
  const findWishlistProduct = wishlistData?.items?.find(
    (item: WishlistItem) => item.productId === product.id
  );

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  const compareProducts = useAppSelector(
    (state) => state?.persistedReducer?.compare?.compareList?.items
  );

  const inCompareList = compareProducts?.find(
    (compareProduct: ICompareItems) => compareProduct.productId === product.id
  );

  var isAvailable = true;
  var disableDecrement = false;
  var disableIncrement = false;
  let i = 0;
  let clicked = false;
  const [amount, setAmount] = useState(1);
  const [modalCmp, setModalCmp] = useState(false);
  const [alreadyInCart, setAlreadyInCart] = useState<boolean>(false);
  const [showQuestionBox, setShowQuestionBox] = useState<boolean>(false);
  const [question, setQuestion] = useState<string>('');
  const [selectedSize, setSelectedSize] = useState<string>('');
  const [selectedColor, setSelectedColor] = useState<string>('');

  if (findWishlistProduct) {
    clicked = true;
  }

  const handleAddToCompare = async () => {
    if (token) {
      if (inCompareList) {
        dispatch(setModalState(!modalCmp));
      } else {
        try {
          const res = await userAPI.addToCompare(product?.id!);
          if ('data' in res!) {
            dispatch(setModalState(!modalCmp));
            dispatch(storeCompare(res.data));
          }
        } catch (error) {
          toast.error('Error happend.', {
            containerId: 'bottom-right',
          });
        }
      }
    } else {
      const productPhotos = product?.photos!.map(
        (photo: ProductPhoto) => photo?.url!
      );
      const productDetails = {
        info: {
          name: product?.info?.name!,
          price: product?.info?.price!,
          shortDescription: product?.info?.shortDescription!,
          fullDescription: product?.info?.shortDescription!,
          oldPrice: product?.info?.oldPrice!,
        },
        meta: {
          friendlyPageName: product?.meta?.friendlyPageName!,
        },
        photos: productPhotos!,
      };
      dispatch(
        storeProductsToComparePublic({
          productId: product?.id!,
          productDetails: productDetails!,
        })
      );
      dispatch(setModalState(!modalCmp));
    }
  };

  const toCart = async () => {
    if (token) {
      if (selectedSize.length > 0 && selectedColor.length > 0) {
        const cartProduct = {
          id: product.id!,
          info: product.info!,
          meta: { friendlyPageName: product.meta?.friendlyPageName! },
          photos: product.photos!,
        };
        const cartItem = {
          product: cartProduct!,
          productId: product.id!,
          quantity: amount,
          size: selectedSize,
          color: selectedColor,
        };
        setAmount(amount);
        if (alreadyInCart) {
          const cart = await userAPI.updateCartItem({
            productId: cartItem.productId,
            quantity: amount,
            size: selectedSize,
            color: selectedColor,
          });
          if ('data' in cart) dispatch(storeAllCartItems(cart?.data?.items!));
          else {
            toast.error(cart?.error.message, {
              containerId: 'bottom-right',
            });
          }
        } else {
          const cart = await userAPI.addToCart({
            productId: cartItem.productId,
            quantity: amount,
            size: selectedSize,
            color: selectedColor,
          });
          if ('data' in cart) {
            dispatch(storeAllCartItems(cart?.data?.items!));
            setAlreadyInCart(true);
          } else {
            toast.error(cart?.error.message, {
              containerId: 'bottom-right',
            });
          }
        }
        toast(<CartToast product={product} />, {
          containerId: 'bottom-left',
        });
        dispatch(addToCart(cartItem));
      } else {
        if (selectedColor.length === 0 && selectedSize.length === 0) {
          toast.warning('Please select a color & a size', {
            containerId: 'bottom-right',
          });
        } else if (selectedColor.length === 0) {
          toast.warning('Please select a color', {
            containerId: 'bottom-right',
          });
        } else {
          toast.warning('Please select a size', {
            containerId: 'bottom-right',
          });
        }
      }
    } else {
      dispatch(setLoginModalState(!modalOn));
    }
  };

  const toWishlist = async (id: string, quantity: number) => {
    if (token) {
      const data = {
        productId: id,
        quantity,
      };
      const reduxData = {
        product: {
          id: id,
          info: product.info,
          photos: product.photos,
        },
        productId: id,
        quantity: quantity,
      };
      try {
        await userAPI.addToWishList(data);
        const newList = await userAPI.getCustomerWishlist(token);
        dispatch(storeWishlist(newList!));
        clicked = true;
        toast.success(`${t('common:item_added_to_wishlist')}`, {
          containerId: 'bottom-right',
        });
      } catch (error) {
        // console.log(error);
        toast.error('Failed to add item to wishlist', {
          containerId: 'bottom-right',
        });
      }
    } else {
      // router.push('/account/sign-in');
      dispatch(setLoginModalState(!modalOn));
    }
  };

  const deleteFromWishlist = async (productId: string) => {
    if (token) {
      try {
        await userAPI.deleteWishlistItem(productId);
        toast.error(`${t('common:item_removed_from_wishlist')}`, {
          containerId: 'bottom-right',
        });
        dispatch(deleteItemFromWishlist(productId));
      } catch (error) {
        toast.error('Failed to remove item from wishlist', {
          containerId: 'bottom-right',
        });
      }
    } else {
      dispatch(setLoginModalState(!modalOn));
    }
  };

  const handleClickToWishlist = () => {
    if (token) {
      router.push('/wishlist');
    } else {
      dispatch(setLoginModalState(!modalOn));
    }
  };

  const handleSubmitQuestion = async () => {
    try {
      const res = await userAPI.askQuestionAboutProduct(product?.id!, question);
      if ('data' in res) {
        toast.success('Question Submitted Successfully', {
          containerId: 'bottom-right',
        });
        setShowQuestionBox(false);
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    dispatch(setModalState(false));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router.asPath]);

  useEffect(() => {
    let itemAmountInCart: ResponseItem | undefined = cartData?.find(
      (item: ResponseItem) => {
        if (item.productId === product.id) {
          return item;
        }
      }
    );

    if (!itemAmountInCart) {
      const cartProduct = {
        id: product.id!,
        info: product.info!,
        meta: { friendlyPageName: product.meta?.friendlyPageName! },
        photos: product.photos!,
      };
      itemAmountInCart = {
        product: cartProduct!,
        productId: product.id!,
        quantity: 1,
        size: selectedSize,
        color: selectedColor,
      };
      setAlreadyInCart(false);
    } else {
      setAlreadyInCart(true);
      setSelectedColor(itemAmountInCart?.color!);
      setSelectedSize(itemAmountInCart?.size!);
    }
    setAmount(itemAmountInCart?.quantity!);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <Breadcrumb
        title={product?.info?.name}
        pathArray={[`${t('common:home')}`, product.info?.name]}
        linkArray={['/', '/product' + product.id]}
      />

      <section className="overflow-hidden bg-white dark:bg-dark_bg">
        <div className="container mx-auto px-5 pt-24 pb-16 text-dark_bg dark:text-dark_text">
          <div>
            <div className="mx-auto flex flex-wrap">
              <div className="w-full md:w-1/2">
                <ProductImagesSlider product={product}></ProductImagesSlider>
              </div>
              <div className="mt-10 w-full md:mt-0 md:w-1/2 md:pl-5 ">
                <h2 className="mb-1 text-xl font-normal text-gray-900 dark:text-dark_text">
                  {product.info.name}
                </h2>
                <div className="flex items-center justify-start gap-2">
                  <RatingStars avgRating={product.avgRating} />{' '}
                  {product.avgRating <= 0 && (
                    <p className="text-xs">No Rating Yet</p>
                  )}
                </div>
                <div className="my-2 ml-1 text-sm text-gray-900 dark:text-dark_text">
                  {t('product-details:manufacturer')}:{' '}
                  {product?.manufacturer?.name
                    ? product?.manufacturer?.name
                    : '---'}{' '}
                  | {t('product-details:sku')}: {product?.info?.sku}
                </div>
                <div className="m-2 ml-1 text-2xl font-medium text-primary">
                  {Intl.NumberFormat(
                    `${currency.currencyLanguage}-${currency.currencyStyle}`,
                    {
                      style: 'currency',
                      currency: `${currency.currencyName}`,
                    }
                  ).format(product?.info?.price)}
                  {product?.info.oldPrice > product?.info.price ? (
                    <span className="ml-2 text-xs font-semibold text-gray-500 dark:text-dark_text">
                      <s>
                        {Intl.NumberFormat(
                          `${currency.currencyLanguage}-${currency.currencyStyle}`,
                          {
                            style: 'currency',
                            currency: `${currency.currencyName}`,
                          }
                        ).format(product?.info?.oldPrice)}
                      </s>
                    </span>
                  ) : null}
                </div>
                <div className="my-2 flex text-sm ">
                  <span className="ml-1 text-gray-900 dark:text-dark_text">
                    {t('product-details:availability')}:
                  </span>
                  <span
                    className={`ml-2 ${
                      product?.info?.stock! > 0
                        ? `text-primary`
                        : `text-red-500`
                    } dark:text-dark_primary`}
                  >
                    {product?.info?.stock! > 0
                      ? product?.info?.stock
                      : 'Out of stock'}
                  </span>
                </div>
                <p className="m-2 ml-1 text-sm text-gray-900 dark:text-dark_text">
                  {product?.info?.shortDescription}
                </p>

                <div className="mt-5 mb-3 flex flex-wrap items-center justify-start gap-3">
                  <p>Available sizes: </p>
                  {product?.info?.size?.length! > 0 ? (
                    <>
                      {' '}
                      {product?.info?.size?.map((size) => {
                        return (
                          <>
                            <button
                              disabled={product?.info?.stock! === 0}
                              className={`cursor-pointer rounded border px-4 py-2 text-sm  ${
                                selectedSize === size
                                  ? 'bg-primary text-white transition-all duration-500 ease-in-out'
                                  : 'text-primary'
                              }`}
                              onClick={() => setSelectedSize(size)}
                            >
                              {size}
                            </button>
                          </>
                        );
                      })}
                    </>
                  ) : (
                    '--'
                  )}
                </div>
                <div className="mt-5 mb-3 flex flex-wrap items-center justify-start gap-3">
                  <p>Available Colors: </p>
                  {product?.info?.color?.length! > 0 ? (
                    <>
                      {' '}
                      {product?.info?.color?.map((color) => {
                        return (
                          <>
                            <button
                              disabled={product?.info?.stock! === 0}
                              className={`cursor-pointer rounded-full p-1 text-sm  ${
                                selectedColor === color
                                  ? 'border border-primary text-white transition-all duration-500 ease-in-out'
                                  : ''
                              }`}
                              onClick={() => setSelectedColor(color)}
                            >
                              <div className="flex flex-wrap items-center gap-2">
                                <CircleIcon fill={color} size={20} />
                              </div>
                            </button>
                          </>
                        );
                      })}
                    </>
                  ) : (
                    '--'
                  )}
                </div>
                <BuyProductQuantity
                  amount={amount}
                  disableDecrement={disableDecrement}
                  setAmount={setAmount}
                  disableIncrement={disableIncrement}
                  isAvailable={product?.info?.stock! > 0 ? true : false}
                  toCart={toCart}
                  stock={product?.info?.stock!}
                />
                {/* <ButtonType1
                  className="mt-2"
                  disabled={!isAvailable}
                  onClickFunction={toCart}
                  text={t('product-details:buy_now')}
                /> */}
                <div className="text-grey-700 ml-1 dark:text-dark_text">
                  <div className="flex gap-x-4">
                    <TextButton
                      onClickFunction={() =>
                        clicked
                          ? deleteFromWishlist(product?.id!)
                          : toWishlist(product?.id!, 1)
                      }
                      text={
                        clicked
                          ? `${t('product-details:remove_from_wishlist')}`
                          : `${t('product-details:add_to_wishlist')}`
                      }
                    />
                    <div hidden={!clicked}>
                      <TextButton
                        onClickFunction={handleClickToWishlist}
                        text={`Go to wishlist`}
                      />
                    </div>
                  </div>
                  {/* <TextButton
                    onClickFunction={handleAddToCompare}
                    text={
                      inCompareList
                        ? `${t('product-details:show_in_compare')}`
                        : `${t('product-details:compare')}`
                    }
                  /> */}
                  {/* <br /> */}
                  {!showQuestionBox && token && (
                    <TextButton
                      onClickFunction={() =>
                        setShowQuestionBox(!showQuestionBox)
                      }
                      text={'+ Ask Question'}
                    />
                  )}
                  <br />
                  {showQuestionBox && token && (
                    <div className="order-1 mb-4 flex w-full flex-col items-start gap-y-4 md:order-2">
                      <span className="font-semibold">Question</span>
                      <textarea
                        className="w-full rounded border border-gray-600/20 p-1 focus:outline-primary/20"
                        rows={4}
                        value={question}
                        onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                          setQuestion(e.target.value)
                        }
                      />
                      <div className="flex flex-wrap gap-x-4">
                        <button
                          className="rounded bg-primary py-1 px-2 text-white hover:bg-slate-800 disabled:bg-primary dark:bg-dark_primary dark:disabled:bg-dark_primary"
                          disabled={question === ''}
                          onClick={handleSubmitQuestion}
                          type="submit"
                        >
                          Submit
                        </button>
                        <button
                          className="rounded bg-gray-400 py-1 px-2 text-white transition-all duration-500 ease-in-out hover:bg-slate-800 dark:bg-dark_primary dark:disabled:bg-dark_primary"
                          onClick={() => setShowQuestionBox(false)}
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          <ProductDescription product={product} />
        </div>
        <div>
          <SimilarProducts />
        </div>
      </section>
    </>
  );
};

export default ProductDetailsComponent;
