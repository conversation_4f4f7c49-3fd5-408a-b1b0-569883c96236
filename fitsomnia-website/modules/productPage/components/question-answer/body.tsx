import AnswerIcon from '@/modules/common/icons/answerIcon';
import QuestionIcon from '@/modules/common/icons/questionIcon';
import { ProductQuestionsWithAnswerForUser } from 'models';
import moment from 'moment';
import { FC } from 'react';

interface Props {
  singleQA: ProductQuestionsWithAnswerForUser;
}

const Body: FC<Props> = ({ singleQA }) => {
  return (
    <>
      <>
        <div className="flex flex-wrap items-center gap-x-3">
          <QuestionIcon fill="#40a944" size={6} />
          <div className="flex-col flex-wrap">
            <p className="text-sm text-black dark:text-dark_text">
              {singleQA.question}
            </p>
            <p className="text-xs text-gray-500">
              {singleQA.userInfo.name} -{' '}
              {moment(singleQA.createdAt).format('ll')}
            </p>
          </div>
        </div>
        <div className="mt-4 flex flex-wrap items-center gap-x-3">
          <AnswerIcon fill="#40a944" size={5} />
          <span className="flex-col flex-wrap">
            <p className="text-sm text-black dark:text-dark_text">
              {singleQA.answer ? singleQA.answer : 'Not Answered Yet...'}
            </p>
            <p className="text-xs text-gray-500">Seller</p>
          </span>
        </div>
        <br />
        <hr />
        <br />
      </>
    </>
  );
};

export default Body;
