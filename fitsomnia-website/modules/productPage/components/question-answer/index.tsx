import Body from '@/modules/productPage/components/question-answer/body';
import { userAPI } from 'APIs';
import { ProductQuestionsWithAnswerForUser } from 'models';
import { FC, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

interface Props {
  productId: string;
}

const QuestionAnswer: FC<Props> = ({ productId }) => {
  const [questionAsked, setQuestionAsked] = useState<
    ProductQuestionsWithAnswerForUser[]
  >([]);
  const [skip, setSkip] = useState(0);
  const [limit, setLimit] = useState(3);
  const [showSeeMore, setShowSeeMore] = useState(true);

  const getQuestions = async () => {
    try {
      const res = await userAPI.getQuestionsAboutProduct(
        productId,
        skip,
        limit
      );
      if ('data' in res) {
        const newArray = questionAsked?.concat(res.data);
        setQuestionAsked(newArray);
        if (res.data.length < limit) {
          setShowSeeMore(false);
        }
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleSeeMore = () => {
    const nextSkip = skip + limit;
    setSkip(nextSkip);
  };

  useEffect(() => {
    getQuestions();
  }, [skip]);

  return (
    <>
      {questionAsked?.length! > 0 ? (
        <>
          {questionAsked?.map((single) => {
            return <Body singleQA={single} key={single.id} />;
          })}
        </>
      ) : (
        <p>There is no question yet.</p>
      )}

      {showSeeMore && (
        <button
          className="float-right text-sm text-primary dark:text-dark_primary"
          onClick={handleSeeMore}
        >
          See More...
        </button>
      )}
    </>
  );
};

export default QuestionAnswer;
