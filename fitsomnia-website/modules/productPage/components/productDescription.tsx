import useTranslation from 'next-translate/useTranslation';
import Image from 'next/legacy/image';
import { useState } from 'react';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/thumbs';

import QuestionAnswer from '@/modules/productPage/components/question-answer';
import ProductReview from '@/modules/productPage/components/review/ProductReview';
import myImageLoader from 'image/loader';
import { Product } from 'models';
import Link from 'next/link';
import { useAppSelector } from 'store/hooks';
interface SingleProduct {
  product: Product;
}

const ProductDescription: React.FC<SingleProduct> = ({
  product,
}: SingleProduct) => {
  const { t } = useTranslation();

  const [description, setDescription] = useState('block');
  const [review, setReview] = useState('hidden');
  const [shipping, setShipping] = useState('hidden');
  const [size_chart, setSize_chart] = useState('hidden');
  const [QA, setQA] = useState('hidden');

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  // const [descriptionChartFocused, setDescriptionChartFocused] = useState(true);
  // const [sizeChartFocused, setSizeChartFocused] = useState(false);
  const shipping_policy =
    'Lorem ipsum dolor sit On the other hand, we denounce with righteous indignation and dislike men who are so beguiled and demoralized by the charms of pleasure of the moment, so blinded by desire, that they cannot foresee the pain and trouble that are bound to ensue; and equal blame belongs to those who fail in their duty through weakness of will amet consectetuer adipiscing elit hdjkdsdf fksdbfg sdfff ksfhf gmnsgbksf eafksbdfg ,saefkbs fs fsdkgbjsgf sdf m,sdf skdgjn sgksugb wrgwsg lf ogs sg slkjg solg sgs gosg sg sl gsgj jsgalflanfpwig sl j so se vs dgjsdgjlb gslg esl efjgsjg ls g ls dgj sl jgs gsl gskg skg s gksg,n sfobe fpeasgff bpa gerogal vag eg';
  const size =
    'https://cdn.shopify.com/s/files/1/0492/7212/7650/files/12_720x720.png?v=1633361194';

  const handleDescription = () => {
    setDescription('block');
    setReview('hidden');
    setShipping('hidden');
    setSize_chart('hidden');
    setQA('hidden');
    // setDescriptionChartFocused(true);
    // setSizeChartFocused(false);
  };

  const handleReview = () => {
    setDescription('hidden');
    setReview('block');
    setShipping('hidden');
    setSize_chart('hidden');
    setQA('hidden');
  };

  // const handleShipping = () => {
  //   setDescription('hidden');
  //   setReview('hidden');
  //   setShipping('block');
  //   setSize_chart('hidden');
  // };

  const handleSize = () => {
    setDescription('hidden');
    setReview('hidden');
    setShipping('hidden');
    setSize_chart('block');
    setQA('hidden');
    // setDescriptionChartFocused(false);
    // setSizeChartFocused(true);
  };

  const handleQA = () => {
    setDescription('hidden');
    setReview('hidden');
    setShipping('hidden');
    setSize_chart('hidden');
    setQA('block');
  };

  return (
    <>
      <div className="border-g-300 mx-auto mt-16 border-2 text-slate-600 dark:text-dark_text lg:w-full">
        <div className="flex flex-wrap border-b-2">
          <ul className="my-3">
            <button
              onClick={handleDescription}
              className={
                description !== 'hidden'
                  ? 'mx-5 font-semibold text-primary dark:text-dark_primary'
                  : 'mx-5 font-semibold '
              }
            >
              {t('product-details:description')}
            </button>
            <button
              onClick={handleReview}
              className={
                review !== 'hidden'
                  ? 'mx-5 font-semibold text-primary dark:text-dark_primary'
                  : 'mx-5 font-semibold '
              }
            >
              Review
            </button>
            {/* <button onClick={handleShipping} className="mx-5 font-semibold">
              Shipping Policy
            </button> */}
            {/* <button
              onClick={handleSize}
              className={
                size_chart !== 'hidden'
                  ? 'mx-5 font-semibold text-primary dark:text-dark_primary'
                  : 'mx-5 font-semibold'
              }
            >
              {t('product-details:size_chart')}
            </button> */}
            <button
              onClick={handleQA}
              className={
                QA !== 'hidden'
                  ? 'mx-5 font-semibold text-primary dark:text-dark_primary'
                  : 'mx-5 font-semibold'
              }
            >
              Q&A
            </button>
          </ul>
        </div>

        <div className="m-5 flex ">
          <div className={description}>
            {product.info.fullDescription
              ? product.info.fullDescription
              : 'No description available'}
          </div>
          <div className={`${review} w-full`}>
            <ProductReview productId={product.id!} />
          </div>
          {/* <p className={shipping}>{shipping_policy}</p> */}
          <div className={`${size_chart} w-full`}>
            <h4 className="font-semibold ">
              {t('product-details:size_chart')}
            </h4>
            <div className="flex justify-center">
              <Image
                loader={myImageLoader}
                src={size}
                alt="size"
                width={400}
                height={400}
              />
            </div>
          </div>
          <div className={`${QA} w-full`}>
            {token === '' && (
              <p className="text-sm">
                <span>
                  <Link
                    prefetch={false}
                    href="/account/sign-in"
                    className="text-primary dark:text-dark_primary"
                  >
                    Login
                  </Link>
                </span>{' '}
                or{' '}
                <span>
                  <Link
                    prefetch={false}
                    href="/account/sign-up"
                    className="text-primary dark:text-dark_primary"
                  >
                    Register
                  </Link>
                </span>{' '}
                to ask questions about the product.
              </p>
            )}
            <h4 className="font-semibold ">Questions Asked</h4>
            <br />
            <div>
              <QuestionAnswer productId={product.id!} />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProductDescription;
