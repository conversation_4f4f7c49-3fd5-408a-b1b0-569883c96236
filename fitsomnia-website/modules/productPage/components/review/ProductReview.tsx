import { userAPI } from 'APIs';
import { handleMediaUpload } from 'helper/handleMediaUpload';
import myImageLoader from 'image/loader';
import { Review, ReviewListWithUserInfo } from 'models';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { useAppSelector } from 'store/hooks/index';
import SingleReview from './SingleReview';

interface IReview {
  name: string;
  email: string;
  message: string;
  rating: number;
}

interface Props {
  productId: string;
}

const ProductReview: React.FC<Props> = ({ productId }) => {
  const router = useRouter();
  const orderId = router.query.orderID as string;

  const [reviews, setReviews] = useState<ReviewListWithUserInfo[]>([]);
  const [message, setMessage] = useState<string>('');
  const [rating, setRating] = useState<number>(0);
  const [file, setFile] = useState<any>();
  const [reviewSkip, setReviewSkip] = useState(0);
  const [reviewLimit, setReviewLimit] = useState(3);
  const [showReviewForm, setShowReviewForm] = useState(false);
  const [currentUserReview, setCurrentUserReview] =
    useState<ReviewListWithUserInfo>();

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  const [reviewSeeMore, setReviewSeeMore] = useState(token ? true : false);

  const userDetails = useAppSelector(
    (state) => state.persistedReducer.user.customerDetails
  );

  const handleRating = (rate: number) => {
    setRating(rate);
  };

  const handleReviewSubmit = async (edit: boolean) => {
    try {
      let res;
      if (file) {
        const fileData = {
          filenames: file.name,
          featureName: 'review',
        };
        res = (await handleMediaUpload(
          fileData,
          file.src,
          token,
          true
        ));
      }
      let data;
      if (file) {
        data = {
          productId,
          orderId: orderId ? orderId : currentUserReview?.orderId!,
          text: message,
          image: [
            {
              url: res ? res : '',
            },
          ],
          rating,
        };
      } else {
        data = {
          productId,
          orderId: orderId ? orderId : currentUserReview?.orderId!,
          text: message,
          rating,
        };
      }
      let reviewRes;
      if (edit) {
        // reviewRes = await userAPI.updateReview(data);
      } else {
        // reviewRes = await userAPI.createReview(data);
      }
      // if ('data' in reviewRes) {
      //   toast.success('Review added successfully', {
      //     containerId: 'bottom-right',
      //   });
      // } else {
      //   toast.error('Failed to add review', {
      //     containerId: 'bottom-right',
      //   });
      // }
      setMessage('');
      setRating(0);
      setFile(null);
      setReviews([]);
      setReviewSeeMore(false);
      setReviewSkip(0);
      getReviews();
      setShowReviewForm(false);
    } catch (error) {}
  };

  const handleSeeMore = () => {
    const nextSkip = reviewSkip + reviewLimit;
    setReviewSkip(nextSkip);
  };

  const getReviews = async () => {
    try {
      const res = await userAPI.getReview(productId, reviewSkip, reviewLimit);
      if ('data' in res) {
        let newArray;
        if (reviewSkip === 0) {
          newArray = res.data;
        } else {
          newArray = reviews?.concat(res.data);
        }
        setReviews(newArray);
        if (res.data.length < reviewLimit) {
          setReviewSeeMore(false);
        }
        const currentUserReviewDetails = newArray.find(
          (review: Review) => review.userId === userDetails.id
        );
        if (currentUserReviewDetails) {
          setCurrentUserReview(currentUserReviewDetails);
        }
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
  };

  const handleReviewUpdate = async () => {
    setShowReviewForm(true);
  };

  const stars = Array(5).fill(0);

  useEffect(() => {
    if (token) getReviews();
  }, [reviewSkip]);

  return (
    <div className="flex w-full flex-col justify-between md:flex-row">
      <div className="flex w-full flex-col gap-y-4 md:w-1/2 md:pr-10">
        {token ? (
          <>
            {' '}
            {reviews.length > 0 ? (
              <>
                {reviews.map((review, index) => (
                  <React.Fragment key={index}>
                    <SingleReview
                      singleReview={review}
                      setReviews={setReviews}
                      setReviewSkip={setReviewSkip}
                      setReviewSeeMore={setReviewSeeMore}
                      getReviews={getReviews}
                      handleReviewUpdate={handleReviewUpdate}
                    />
                    {index < reviews.length - 1 && <hr />}
                  </React.Fragment>
                ))}
              </>
            ) : (
              'No review yet'
            )}
          </>
        ) : (
          <p>
            <Link
              prefetch={false}
              href="/account/sign-in"
              passHref
              className="cursor-pointer text-primary"
            >
              Login
            </Link>{' '}
            to see reviews.
          </p>
        )}
        {reviewSeeMore && (
          <div>
            <button
              className="float-right text-sm text-primary dark:text-dark_primary"
              onClick={handleSeeMore}
            >
              See More...
            </button>
          </div>
        )}
      </div>
      <br />
      {token && (orderId || showReviewForm) ? (
        <div className="order-1 mb-4 flex w-full flex-col items-start gap-y-4 md:order-2 md:w-2/5">
          <div className="text-2xl font-semibold">Add your review</div>
          <div className="flex">
            <div className="mr-2 font-semibold">Your rating:</div>
            <div className="flex">
              {stars.map((_, index) => (
                <span
                  key={index}
                  onClick={() => handleRating(index + 1)}
                  className="cursor-pointer"
                >
                  {index + 1 <= rating ? (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      className="h-6 w-6"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z"
                        clipRule="evenodd"
                      />
                    </svg>
                  ) : (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="h-6 w-6"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M11.48 3.499a.562.562 0 011.04 0l2.125 5.111a.563.563 0 00.475.345l5.518.442c.499.04.701.663.321.988l-4.204 3.602a.563.563 0 00-.182.557l1.285 5.385a.562.562 0 01-.84.61l-4.725-2.885a.563.563 0 00-.586 0L6.982 20.54a.562.562 0 01-.84-.61l1.285-5.386a.562.562 0 00-.182-.557l-4.204-3.602a.563.563 0 01.321-.988l5.518-.442a.563.563 0 00.475-.345L11.48 3.5z"
                      />
                    </svg>
                  )}
                </span>
              ))}
            </div>
          </div>
          <div className="flex w-full flex-col">
            <span className="font-semibold">Message</span>
            <textarea
              className="w-full rounded border border-gray-600/20 p-1 focus:outline-primary/20"
              rows={4}
              value={message}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                setMessage(e.target.value)
              }
            />
          </div>

          <div>
            <input
              id="file"
              name="file"
              type="file"
              onChange={(event: React.ChangeEvent<HTMLInputElement>) => {
                setFile({
                  src: event?.target?.files![0],
                  type: event?.target?.files![0].type,
                });
              }}
            />
            {file && (
              <>
                <Image
                  loader={myImageLoader}
                  className="mt-3"
                  src={URL.createObjectURL(file.src)}
                  height={100}
                  width={100}
                  alt="animation"
                />
              </>
            )}
          </div>

          <button
            className="cursor-pointer rounded bg-primary py-1 px-2 text-white transition-all duration-500 ease-in-out hover:bg-slate-800 disabled:cursor-text disabled:bg-gray-400 dark:bg-dark_primary dark:disabled:bg-dark_primary"
            disabled={message.length <= 1}
            onClick={() => {
              if (showReviewForm) {
                handleReviewSubmit(true);
              } else {
                handleReviewSubmit(false);
              }
            }}
            type="submit"
          >
            Submit
          </button>
        </div>
      ) : (
        <></>
      )}
    </div>
  );
};

export default ProductReview;
