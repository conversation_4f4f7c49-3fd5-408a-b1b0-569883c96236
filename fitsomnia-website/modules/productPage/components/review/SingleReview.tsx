import StarIcon from '@/modules/common/icons/starIcon';
import { userAPI } from 'APIs';
import myImageLoader from 'image/loader';
import { ReviewListWithUserInfo } from 'models';
import Image from 'next/image';
import React from 'react';
import { toast } from 'react-toastify';
import { useAppSelector } from 'store/hooks';

interface IReview {
  singleReview: ReviewListWithUserInfo;
  setReviews: Function;
  setReviewSkip: Function;
  setReviewSeeMore: Function;
  handleReviewUpdate: Function;
  getReviews: Function;
}

const SingleReview: React.FC<IReview> = ({
  singleReview,
  setReviews,
  setReviewSeeMore,
  setReviewSkip,
  handleReviewUpdate,
  getReviews,
}: IReview) => {
  const stars = Array(5).fill(0);
  const userDetails = useAppSelector(
    (state) => state.persistedReducer.user.customerDetails
  );

  const handleReviewDelete = async () => {
    try {
      const res = await userAPI.deleteReview(singleReview.id);
      if ('data' in res) {
        toast.success('Review deleted successfully', {
          containerId: 'bottom-right',
        });
        setReviews([]);
        setReviewSkip(0);
        //setReviewSeeMore(true);
        getReviews();
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="flex flex-row">
      <div className="mr-3">
        {singleReview?.userInfo?.image?.profile! ? (
          <>
            <Image
              loader={myImageLoader}
              className="h-10 w-10 rounded-full border-none align-middle shadow"
              src={singleReview?.userInfo?.image?.profile!}
              height={70}
              width={70}
              alt="user's profile image"
            />
          </>
        ) : (
          <div className="rounded-full border border-gray-600/30 p-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              strokeWidth={1.5}
              stroke="currentColor"
              className="h-10 w-10"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z"
              />
            </svg>
          </div>
        )}
      </div>
      <div className="flex flex-col">
        <span className="text-lg font-semibold">
          {singleReview?.userInfo?.name!}
        </span>
        <div className="flex">
          {stars.map((_, index) => (
            <span key={index}>
              {index + 1 <= singleReview?.rating! ? (
                <StarIcon height={3} width={3} fill="currentColor" />
              ) : (
                <StarIcon height={3} width={3} stroke="currentColor" />
              )}
            </span>
          ))}
        </div>
        {singleReview?.image![0] && (
          <Image
            loader={myImageLoader}
            className="mt-3"
            src={singleReview?.image![0].url!}
            height={100}
            width={100}
            alt="review image"
          />
        )}
        <p>{singleReview?.text}</p>
        {userDetails.id === singleReview.userId && (
          <>
            <div className="mr-4 flex flex-wrap items-center justify-between">
              <button
                className=" text-xs text-primary hover:text-black"
                onClick={() => handleReviewUpdate()}
              >
                Update
              </button>
              <button
                className="text-xs text-red-500 hover:text-black"
                onClick={handleReviewDelete}
              >
                Delete
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default SingleReview;
