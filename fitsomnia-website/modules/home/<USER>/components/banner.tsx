import { NextComponentType } from 'next';
import useTranslation from 'next-translate/useTranslation';

import PlayIcon from '@/modules/common/icons/playIcon';
import FillPrimaryDarkPrimary from '@/modules/common/icons/handlers/fllPrimaryDarkPrimary';

const Banner: React.FC = () => {
  const { t } = useTranslation();
  const BannerTitle = 'Best Selling Products';
  return (
    <>
      <div className="row-span-full m-auto mb-4 flex h-96 flex-col-reverse bg-[url('https://www.vmcdn.ca/f/files/glaciermedia/images/endorsed/undefined-undefined-getty-images.png;w=960')] bg-cover bg-center bg-no-repeat md:m-0 md:mb-0 md:h-full">
        <div className="ml-5 mb-6 lg:mb-7">
          <p className="mb-2 w-1 text-4xl font-thin">{BannerTitle}</p>
          <p className="row text-extrabold text-1xl flex flex uppercase">
            {t('home:shop_now')}
            <FillPrimaryDarkPrimary icon={<PlayIcon />} />
          </p>
        </div>
      </div>
    </>
  );
};

export default Banner;
