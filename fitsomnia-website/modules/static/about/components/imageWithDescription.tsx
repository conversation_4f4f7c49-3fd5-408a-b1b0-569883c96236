import useTranslation from 'next-translate/useTranslation';
import Image from 'next/legacy/image';

import myImageLoader from 'image/loader';

const ImageWithDescription: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="container mx-auto flex flex-col items-center justify-center gap-10 px-4">
      <Image
        loader={myImageLoader}
        src="/illustration.svg"
        width={800}
        height={500}
        alt="about"
      />
      <div>
        <p className="mx-auto max-w-4xl text-justify text-sm">
          At Fitsomnia, we believe that fitness is a journey, not a destination.
          We understand that getting in shape and staying healthy requires
          discipline, motivation, and support from a community of like-minded
          individuals. That&apos;s why we created the Fitsomnia app - to provide
          a platform for fitness enthusiasts to connect, share their
          experiences, and achieve their goals together.
        </p>
        <br />
        <p className="mx-auto max-w-4xl text-justify text-sm">
          Everyone on our team shares a common goal to make fitness accessible
          and enjoyable for everyone. We believe that technology can be a
          powerful tool for bringing people together and helping them live
          healthier, more fulfilling lives.
        </p>
        <p className="mx-auto max-w-4xl text-justify text-sm">
          With that in mind, we&apos;ve built an app that not only connects
          people but also provides them with the tools they need to succeed.
          From our innovative Spot/Not feature that helps users connect with
          fellow gym members, to our Hall of Weights that adds a competitive
          element to workouts, and our robust calorie counter that makes it easy
          to track nutrition, we&apos;ve designed Fitsomnia to be a
          comprehensive one-stop-shop for all your fitness needs.
        </p>
        <br />
        <p className="mx-auto max-w-4xl text-justify text-sm">
          But we&apos;re not just about technology - we&apos;re also about
          community. We believe that a supportive network of like-minded
          individuals can make all the difference when it comes to achieving
          your fitness goals. That&apos;s why we&apos;ve made it easy for users
          to connect with each other and share their experiences, advice, and
          encouragement.
        </p>
        <br />
        <p className="mx-auto max-w-4xl text-justify text-sm">
          Whether you&apos;re just starting out on your fitness journey or
          you&apos;re a seasoned pro, we believe that Fitsomnia can help you
          achieve your goals and live your best life. Join us today and become a
          part of our supportive fitness community.
        </p>
      </div>
      {/* <Image
        loader={myImageLoader}
        src="https://cdn.shopify.com/s/files/1/0359/6350/2651/files/about-us-signature_medium.png?v=1588134272"
        width={228}
        height={129}
        alt="about"
      /> */}
    </div>
  );
};

export default ImageWithDescription;
