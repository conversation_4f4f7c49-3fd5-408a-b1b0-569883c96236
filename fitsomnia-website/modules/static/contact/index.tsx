import Breadcrumb from '@/modules/common/breadcrumbs/breadcrumb';
import ContactArea from '@/modules/static/contact/components/contactArea';
import useTranslation from 'next-translate/useTranslation';
import { useRouter } from 'next/router';

const ContactComponent: React.FC = () => {
  const { t } = useTranslation();
  const router = useRouter();
  const prev = router.query.prev;

  return (
    <>
      <Breadcrumb
        title={t('contact:contact')}
        pathArray={[`${t('common:home')}`, `${t('contact:contact')} `]}
        linkArray={[prev ? '/' : '/', '/']}
      />
      {/* <div className="mt-2 mb-10">
        <MapArea />
      </div> */}
      <div className="mt-5 mb-10">
        <ContactArea />
      </div>
    </>
  );
};

export default ContactComponent;
