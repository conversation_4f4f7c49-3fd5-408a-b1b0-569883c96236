import { useRouter } from 'next/router';
import { FC, useEffect, useState } from 'react';

interface Props {
  cur: number;
  setCur: Function;
  total: number;
  paginate?: Function;
  limit?: number;
}

const Pagination: FC<Props> = ({ cur, setCur, total, paginate, limit }) => {
  const router = useRouter();
  let [num, setNum] = useState(1);
  const [pages, setPages] = useState<{ page: number }[]>([]);

  // const pages = [
  //   { page: num },
  //   { page: num + 1 },
  //   { page: num + 2 },
  //   { page: num + 3 },
  //   { page: num + 4 },
  // ];

  useEffect(() => {
    const arr = [];
    for (let i = 0; i < 5; i++) {
      if (i + num <= total) arr.push({ page: i + num });
    }
    setPages(arr);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [num]);

  function Next() {
    const x = num + 1;
    if (x + 4 <= total) setNum(x);
  }
  function back() {
    num > 1 && setNum(--num);
  }

  useEffect(() => {
    setNum(1);
  }, [router?.query]);

  return (
    <div className="flex gap-2 rounded-lg bg-white">
      <button
        onClick={back}
        className="rounded border 
                bg-[#f1f1f1] px-2 font-medium transition-all duration-500 ease-in-out hover:bg-primary hover:text-white"
      >
        <svg className="h-4 w-4 fill-current" viewBox="0 0 20 20">
          <path
            d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
            clipRule="evenodd"
            fillRule="evenodd"
          ></path>
        </svg>
      </button>
      {pages.map((pg, i) => (
        <button
          key={i}
          onClick={() => {
            setCur(pg.page);
            paginate && paginate!(limit! * (pg.page - 1));
          }}
          className={`rounded border px-3 py-2 text-sm font-medium hover:bg-primary hover:text-white
                ${cur === pg.page ? 'bg-primary text-white' : 'bg-[#f1f1f1]'}`}
        >
          {pg.page}
        </button>
      ))}
      <button
        onClick={Next}
        className="rounded border bg-[#f1f1f1] px-2 font-medium hover:bg-primary hover:text-white"
      >
        <svg className="h-4 w-4 fill-current" viewBox="0 0 20 20">
          <path
            d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
            clipRule="evenodd"
            fillRule="evenodd"
          ></path>
        </svg>
      </button>
    </div>
  );
};

export default Pagination;
