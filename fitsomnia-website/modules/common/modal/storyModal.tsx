import { useRouter } from 'next/router';
import React from 'react';
import { useAppDispatch } from 'store/hooks/index';

import Image from 'next/image';

import { useState } from 'react';
import 'react-toastify/dist/ReactToastify.css';
import { useAppSelector } from 'store/hooks/index';

interface Props {
  setModalOn: Function;
  setChoice: Function;
  trigger?: Function;
  modalTitle?: string;
  bodyText?: string;
}

export interface Media {
  url: string;
  name: string;
  file: File;
}

const StoryModal: React.FC<Props> = ({
  setModalOn,
  setChoice,
  trigger,
  modalTitle,
  bodyText,
}: Props) => {
  const router = useRouter();
  const dispatch = useAppDispatch();

  const handleCancelClick = () => {
    setChoice(false);

    setModalOn(false);
  };

  // const router = useRouter();
  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );
  const [media, setMedia] = useState<Media[]>([]);
  const [file, setFile] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [addMediaFile, setAddMediaFile] = useState(true);
  const [showStory, setShowStory] = useState(false);
  const addMedia = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const file = files[0]; // Only take the first file

      const fileUrl = URL.createObjectURL(file);

      // Update the file state with a single file
      setFile({
        [fileUrl]: {
          src: fileUrl,
          type: file.type,
          file: file,
        },
      });

      // Create the media object
      const mediaInfo = {
        url: fileUrl,
        name: file.name,
        file: file,
      };

      // Set media state to only contain the new file
      setMedia([mediaInfo]);
    }
    setAddMediaFile(false);
    setShowStory(true);
  };

  console.log('media', media);

  return (
    <>
      <div
        className="fixed inset-0 z-50 bg-neutral-900/40"
        onClick={handleCancelClick}
      >
        {addMediaFile && (
          <div className="flex h-screen items-center justify-around ">
            <div
              className="w-2/3 px-5 pt-5 dark:bg-zinc-700 sm:w-auto"
              onClick={(e: React.MouseEvent<HTMLDivElement, MouseEvent>) =>
                e.stopPropagation()
              }
            >
              <div className="bg-[##EEF2FF] flex h-[141px] w-[350px] flex-col items-center gap-4 rounded-lg border border-indigo-500 bg-indigo-100 p-10">
                <input
                  className="hidden"
                  type="file"
                  id="media"
                  name="media"
                  onChange={addMedia}
                  accept="image/*,video/*"
                  multiple
                />
                <label htmlFor="media" className=" cursor-pointer ">
                  <div className="flex items-center justify-center rounded-full">
                    <Image
                      src="/camera.png"
                      alt="camera"
                      width={30}
                      height={30}
                    />
                  </div>
                  <p className="text-lg font-medium capitalize text-[#1E1B4B]">
                    Add Media
                  </p>
                </label>
              </div>
            </div>
          </div>
        )}
        {showStory && (
          <div className="absolute left-1/2 top-1/2 flex h-[700px] w-[960px] -translate-x-1/2 -translate-y-1/2 transform flex-col items-start gap-4 rounded-2xl bg-white p-5">
            {/* Header */}
            <div className="flex h-[43px] w-full flex-row items-center justify-center gap-2 rounded-t-lg border border-t border-l border-r border-gray-200 p-4">
              <span className="font-poppins text-center text-lg font-semibold leading-[30px] text-[#04160A]">
                Create Story
              </span>
            </div>

            {media.map((item, index) => (
              <>
                <div
                  key={index}
                  className="mx-auto flex flex-col items-center justify-center"
                >
                  <Image
                    src={item.url}
                    alt="story"
                    width={500}
                    height={500}
                    className="bg-cover bg-center"
                  />
                </div>
                <div className="mx-auto flex h-[62px] w-[436px] flex-row items-center justify-center gap-3">
                  <Image
                    src="/story_text.png"
                    alt="story"
                    width={62}
                    height={62}
                  />

                  {[...Array(5)].map((_, index) => (
                    <div key={index}>
                      <Image
                        src={item.url}
                        alt="story"
                        width={62}
                        height={62}
                      />
                    </div>
                  ))}

                  <Image
                    src="/story_complete.png"
                    alt="story"
                    width={62}
                    height={62}
                  />
                </div>
              </>
            ))}

            {/* Content Container */}

            {/* User Avatars */}

            {/* Post Button */}
            <div className="flex h-[51px] w-full cursor-pointer flex-row items-center justify-center gap-2 rounded-full bg-[#1EA951] p-5">
              <span className="font-poppins text-lg font-medium capitalize text-white">
                Post Story
              </span>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default StoryModal;
