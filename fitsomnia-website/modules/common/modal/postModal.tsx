import { useRouter } from 'next/router';
import React from 'react';
import { useAppDispatch } from 'store/hooks/index';
import { setLoginModalState } from 'store/slices/modalSlice';

import { userAPI } from 'APIs';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import { handleMediaUpload } from 'helper/handleMediaUpload';
import { PrivacyType } from 'models/post/enums.post.interface';
import Image from 'next/image';

import { useState } from 'react';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useAppSelector } from 'store/hooks/index';

interface Props {
  setModalOn: Function;
  setChoice: Function;
  trigger?: Function;
  modalTitle?: string;
  bodyText?: string;
}

export interface Media {
  url: string;
  name: string;
  file: File;
}

const PostModal: React.FC<Props> = ({
  setModalOn,
  setChoice,
  trigger,
  modalTitle,
  bodyText,
}: Props) => {
  const router = useRouter();
  const dispatch = useAppDispatch();

  const handleCancelClick = () => {
    setChoice(false);
    dispatch(setLoginModalState(false));
    setModalOn(false);
  };

  // const router = useRouter();
  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );
  const [media, setMedia] = useState<Media[]>([]);
  const [file, setFile] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (values: any) => {
    try {
      setIsLoading(true);
      let mediaUrls: string[] = [];

      if (media.length > 0) {
        const fileData = {
          featureName: 'post',
          filenames: media.map((item) => item.name),
        };

        // console.log('file data from form', fileData);

        try {
          // Upload all media files together
          mediaUrls = await handleMediaUpload(
            fileData,
            media.map((item) => item.file),
            token,
            false
          );
        } catch (uploadError: any) {
          toast.error(uploadError.message || 'Media upload failed', {
            containerId: 'bottom-right',
          });

          setModalOn(false);

          return; // Stop execution if upload fails
        } finally {
          setIsLoading(false); // Stop loading
        }
      }

      // Separate images and videos
      const images = mediaUrls.filter((url) =>
        url.match(/\.(jpeg|jpg|png|gif)$/i)
      );
      const videos = mediaUrls.filter((url) =>
        url.match(/\.(mp4|mov|avi|mkv)$/i)
      );

      const data = {
        content: values.content,
        privacy: values.privacy,
        images: images.map((url) => ({ url })),
        videos: videos.map((url) => ({ url })),
      };

      const res = await userAPI.createPost(data);
      // console.log(res?.data);

      if (res.data) {
        toast.success('Post Created Successfully', {
          containerId: 'bottom-right',
        });
        setModalOn(false);
      } else {
        toast.error('Failed to Create Post', {
          containerId: 'bottom-right',
        });
        setModalOn(false);
      }
    } catch (error: any) {
      toast.error(error.message || 'Error creating post', {
        containerId: 'bottom-right',
      });
    }
  };

  return (
    <>
      <div
        className="fixed inset-0 z-50 bg-neutral-900/40"
        onClick={handleCancelClick}
      >
        <div className="flex h-screen items-center justify-around ">
          <div
            className="w-2/3 px-5 pt-5 dark:bg-zinc-700 sm:w-auto"
            onClick={(e: React.MouseEvent<HTMLDivElement, MouseEvent>) =>
              e.stopPropagation()
            }
          >
            <div data-testid="hygen">
              <Formik
                initialValues={{
                  content: '',
                  privacy: PrivacyType.PUBLIC,
                  files: [],
                }}
                onSubmit={(values, actions) => {
                  handleSubmit(values);
                  actions.setSubmitting(false);
                }}
                // validationSchema={}
              >
                {(formikprops) => {
                  const addMedia = (
                    event: React.ChangeEvent<HTMLInputElement>
                  ) => {
                    const files = event.target.files;
                    if (files) {
                      const newFiles = Array.from(files);

                      // console.log('Selected files:', newFiles); // Debugging step

                      newFiles.forEach((file) => {
                        const fileUrl = URL.createObjectURL(file);

                        setFile((prev: any) => ({
                          ...prev,
                          [fileUrl]: {
                            src: fileUrl,
                            type: file.type,
                            file: file,
                          },
                        }));

                        const mediaInfo = {
                          url: fileUrl,
                          name: file.name,
                          file: file,
                        };

                        setMedia((prevMedia) => {
                          const updatedMedia = [...prevMedia, mediaInfo];
                          // console.log('Updated media state:', updatedMedia); // Debugging step
                          return updatedMedia;
                        });

                        formikprops.setFieldValue(
                          'media',
                          (prevMedia: Media[]) => [...prevMedia, mediaInfo]
                        );
                      });

                      // Update the files in form values
                      formikprops.setFieldValue(
                        'files',
                        (prevFiles: File[]) => [...prevFiles, ...newFiles]
                      );
                    }
                  };

                  const removeMedia = (url: string) => {
                    const updatedMedia = media.filter(
                      (item) => item.url !== url
                    );
                    setMedia(updatedMedia);

                    formikprops.setFieldValue('media', updatedMedia);

                    // Remove the file from the files state
                    if (file[url]?.file) {
                      formikprops.setFieldValue(
                        'files',
                        (currentFiles: File[]) =>
                          currentFiles.filter((f) => f !== file[url].file)
                      );

                      // Clean up the file state
                      setFile((prev: any) => {
                        const newState = { ...prev };
                        delete newState[url];
                        return newState;
                      });
                    }
                  };
                  return (
                    <Form onSubmit={formikprops.handleSubmit}>
                      <div className="absolute left-[240px] top-1/2 flex max-h-[100vh] w-[960px] -translate-y-1/2 flex-col   overflow-y-auto rounded-2xl bg-white p-5">
                        {isLoading && (
                          <div className="absolute inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                            <div className="h-16 w-16 animate-spin rounded-full border-4 border-white border-t-transparent"></div>
                          </div>
                        )}
                        <div className="">
                          <div className="flex justify-between gap-2 rounded-t-lg border border-t border-l border-r border-b-0 border-gray-200  p-4">
                            <span className="text-lg font-semibold text-[#04160A]">
                              Create Post
                            </span>
                            <div className="flex items-center gap-2">
                              <select
                                name="privacy"
                                className="rounded-md border border-gray-300 bg-green-500 p-2 focus:outline-none focus:ring-2 focus:ring-green-500 "
                                value={formikprops.values.privacy}
                                onChange={formikprops.handleChange}
                              >
                                <option value={PrivacyType.PUBLIC}>
                                  Public
                                </option>
                                <option value={PrivacyType.PRIVATE}>
                                  Private
                                </option>
                              </select>
                              <div className="errMsg text-red-600">
                                <ErrorMessage name="privacy" />
                              </div>
                            </div>
                          </div>
                          <div className="mb-4 h-48 w-full  rounded-b-lg border border-gray-200 bg-white  ">
                            <Field
                              as="textarea"
                              className="h-full w-full   resize-none p-2 placeholder-gray-600 outline-0"
                              id="content"
                              name="content"
                              placeholder="Write here"
                            />
                            <div className="errMsg text-red-600">
                              <ErrorMessage name="content" />
                            </div>
                          </div>
                        </div>

                        {/* Media */}

                        <div className="media-preview mx-auto flex flex-wrap gap-2 overflow-y-auto mb-2">
                          {media.map((item, index) => (
                            <div
                              key={index}
                              className="relative overflow-hidden rounded-lg shadow-md"
                              style={{ width: '180px', height: '180px' }}
                            >
                              {/* Show image */}
                              {file?.[item.url]?.type?.startsWith('image/') ? (
                                <Image
                                  src={item.url}
                                  alt="Preview"
                                  width={180}
                                  height={180}
                                  className="rounded-lg object-cover"
                                />
                              ) : (
                                <video
                                  src={item.url}
                                  className="h-full w-full rounded-lg object-cover"
                                  controls
                                />
                              )}

                              {/* Remove button with better styling */}
                              <button
                                className="absolute top-1 right-1 rounded-full bg-gray-500 px-2 py-1 text-xs text-white hover:bg-gray-600"
                                onClick={() => removeMedia(item.url)}
                              >
                                ✕
                              </button>
                            </div>
                          ))}
                        </div>

                        <div className="flex h-[52px] w-full items-center justify-center gap-4 rounded-lg border border-green-400 bg-green-100 p-2">
                          <input
                            className="hidden"
                            type="file"
                            id="media"
                            name="media"
                            onChange={addMedia}
                            accept="image/*,video/*"
                            multiple
                          />
                          <label
                            htmlFor="media"
                            className="flex cursor-pointer items-center gap-4"
                          >
                            <Image
                              src="/camera.png"
                              alt="camera"
                              width={16}
                              height={16}
                            />
                            <span className="text-lg font-medium text-[#04160A]">
                              Add Media
                            </span>
                          </label>
                        </div>
                        {/* <p className="mx-auto text-gray-300">
                          you can add up up to 10 attachments{' '}
                        </p> */}

                        <button
                          type="submit"
                          // className="flex h-[51px] w-full items-center justify-center gap-2 rounded-full bg-green-600 p-5 text-lg font-medium text-white"
                          disabled={
                            !(
                              formikprops.values.content.trim() ||
                              media.length > 0
                            )
                          }
                          className={`mt-2 rounded-full px-4 py-2 text-lg ${
                            formikprops.values.content.trim() ||
                            media.length > 0
                              ? 'cursor-pointer bg-green-600  text-white hover:bg-green-600'
                              : 'cursor-not-allowed bg-[#E0E2E5] text-[#6B7280]'
                          }`}
                        >
                          Share Post
                        </button>
                      </div>
                    </Form>
                  );
                }}
              </Formik>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PostModal;
