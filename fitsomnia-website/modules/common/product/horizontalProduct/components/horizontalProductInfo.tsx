import { trimDescription } from 'helper/trim';
import { UserProduct } from 'models';
import { useAppSelector } from 'store/hooks/index';

interface SingleProduct {
  product: UserProduct;
}

const ProductInfo = (props: SingleProduct) => {
  const { product } = props;
  const currency = useAppSelector((state) => state.persistedReducer.currency);

  return (
    <div>
      <div className="pl-4">
        <div
          className="text-base text-inherit text-gray-600"
          id="searchProductName"
        >
          {trimDescription(product?.info?.name, 15)}
        </div>
        <p className="py-2 font-['arial'] text-xs text-gray-600">
          {product?.tags![0]}
        </p>
        <p className="text-base font-semibold text-primary dark:text-dark_primary">
          {Intl.NumberFormat(
            `${currency.currencyLanguage}-${currency.currencyStyle}`,
            { style: 'currency', currency: `${currency.currencyName}` }
          ).format(product?.info?.price)}
          {product?.info.oldPrice > product?.info.price ? (
            <span className="ml-2 text-xs font-semibold text-dark_bg dark:text-dark_text">
              <s>
                {Intl.NumberFormat(
                  `${currency.currencyLanguage}-${currency.currencyStyle}`,
                  { style: 'currency', currency: `${currency.currencyName}` }
                ).format(product?.info?.oldPrice)}
              </s>
            </span>
          ) : null}
        </p>
        {product?.info?.stock! <= 0 && <p>Out of Stock</p>}
      </div>
    </div>
  );
};

export default ProductInfo;
