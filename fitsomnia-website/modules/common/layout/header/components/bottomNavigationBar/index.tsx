import Link from 'next/link';

import HeartIcon from '@/modules/common/icons/heartIcon';
import HomeOutlineIcon from '@/modules/common/icons/homeIcon';
import ShoppingCartOutlineIcon from '@/modules/common/icons/shoppingCartIcon';
import UserOutlineIcon from '@/modules/common/icons/userIcon';

interface Props {
  openDrawer: () => void;
}

const BottomNavigationBar: React.FC<Props> = ({ openDrawer }: Props) => {
  return (
    <>
      <div className="fixed bottom-0 z-40 flex w-full flex-row items-center justify-center bg-primary py-3 dark:bg-dark_primary lg:hidden">
        <div className="flex w-full flex-row justify-evenly md:w-9/12">
          <Link prefetch={false} href="/market" passHref>
            <button className="flex flex-col items-center text-white">
              <HomeOutlineIcon />
              <span>Home</span>
            </button>
          </Link>
          <Link prefetch={false} href="/wishlist" passHref>
            <button className="flex flex-col items-center text-white">
              <HeartIcon bottomNav={true} />
              <span>Wishlist</span>
            </button>
          </Link>
          <Link prefetch={false} href="/cart" passHref>
            <button className="flex flex-col items-center text-white">
              <ShoppingCartOutlineIcon />
              <span>Cart</span>
            </button>
          </Link>
          <button
            className="flex flex-col items-center text-white"
            onClick={() => openDrawer()}
          >
            <UserOutlineIcon />
            <span>More</span>
          </button>
        </div>
      </div>
    </>
  );
};

export default BottomNavigationBar;
