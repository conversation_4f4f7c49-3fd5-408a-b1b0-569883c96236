import MinusSolidIcon from '@/modules/common/icons/minusIcon';
import PlusSolidIcon from '@/modules/common/icons/plusIcon';
import HeaderSubCategory from '@/modules/common/layout/header/components/headerSubCategory';
import { NestedCategoryList, SubCategoryList } from 'models';
import Link from 'next/link';
import React, { useState } from 'react';

interface Props {
  category: NestedCategoryList;
}

const HeaderCategory: React.FC<Props> = ({ category }: Props) => {
  const [showSubCategory, settt] = useState(false);
  const [expand, setExpand] = useState<boolean>(false);

  const handleExpandClick = (
    e: React.MouseEvent<HTMLSpanElement, MouseEvent>
  ) => {
    e.stopPropagation();
    setExpand(!expand);
  };

  return (
    <div
      className={`${expand && 'h-96'} categoryDropdown overflow-y-scroll`}
      /** For hover effect only in large screen */
      // onMouseEnter={() => document.body.clientWidth > 1023 && setExpand(true)}
      // onMouseLeave={() => document.body.clientWidth > 1023 && setExpand(false)}
    >
      <div className="flex cursor-pointer flex-row items-center justify-between py-2 px-1 text-sm transition-all duration-100 ease-linear hover:text-primary dark:hover:text-dark_primary">
        <Link
          prefetch={false}
          href={{
            pathname: `/collections/${category.name}`,
            query: {
              categoryId: category.id,
              name: category.name,
            },
          }}
          className=""
        >
          {category.name}
        </Link>

        <div
          className="block"
          onClick={(e: React.MouseEvent<HTMLSpanElement, MouseEvent>) =>
            handleExpandClick(e)
          }
        >
          {category.subCategories ? (
            expand ? (
              <MinusSolidIcon size={5} />
            ) : (
              <PlusSolidIcon size={5} />
            )
          ) : (
            ''
          )}
        </div>
      </div>
      {category.subCategories && expand ? (
        <div className="">
          <ul className="pl-2">
            {category.subCategories?.map((subCategory: SubCategoryList) => (
              <li key={subCategory.name}>
                {/* {subCategory.name} */}
                <HeaderSubCategory
                  category={subCategory}
                  level={1}
                  showSub={expand}
                />
              </li>
            ))}
          </ul>
        </div>
      ) : (
        ''
      )}
    </div>
  );
};

export default HeaderCategory;
