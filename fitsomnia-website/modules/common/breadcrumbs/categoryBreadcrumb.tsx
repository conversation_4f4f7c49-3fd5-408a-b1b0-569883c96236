import Link from 'next/link';
import React from 'react';

interface CategoryNameIdProp {
  name: string;
  id: string;
}

interface Props {
  title: string;
  categoryNameAndId: CategoryNameIdProp[];
}

const CategoryBreadcrumb: React.FC<Props> = (props) => {
  const { title, categoryNameAndId } = props;

  return (
    <div className=" h-48 bg-[url('/segment8bg.jpg')] bg-cover bg-center bg-no-repeat p-9 text-white sm:p-5 md:p-12 lg:p-14 xl:p-14">
      <h3 className="text-center text-2xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-4xl">
        {title}
      </h3>
      <div className="m-2 flex flex-wrap items-center justify-center gap-2 sm:m-2 md:m-3 lg:m-3 xl:m-3">
        {categoryNameAndId.map((path, index) => {
          return (
            <React.Fragment key={path.id}>
              {index === 0 && (
                <Link
                  prefetch={false}
                  href="/"
                  passHref
                  className="text-decoration-none text-sm text-white hover:text-orange-400"
                >
                  Home /
                </Link>
              )}
              {index === categoryNameAndId.length - 1 && (
                <Link
                  prefetch={false}
                  href={{
                    pathname: `/collections/${path.name}`,
                    query: {
                      categoryId: path.id,
                      name: path.name,
                    },
                  }}
                  passHref
                  className="text-decoration-none text-sm text-gray-300 hover:text-orange-400"
                  style={{ pointerEvents: 'none' }}
                >
                  {path.name}
                </Link>
              )}
              {index < categoryNameAndId.length - 1 && (
                <Link
                  prefetch={false}
                  href={{
                    pathname: `/collections/${path.name}`,
                    query: {
                      categoryId: path.id,
                      name: path.name,
                    },
                  }}
                  passHref
                  className="text-decoration-none text-sm text-white hover:text-orange-400"
                >
                  {path.name}
                </Link>
              )}
              {index !== categoryNameAndId.length - 1 && <span>/</span>}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default CategoryBreadcrumb;
