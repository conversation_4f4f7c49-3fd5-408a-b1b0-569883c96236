import React from 'react';
import SocialSigninButton from './SocialSigninButton';
import FacebookLogo from '../../icons/facebookLogo';
import { useSocialSignin } from '@/hooks/useSocialSignin';

interface FacebookSigninButtonProps {
  className?: string;
  disabled?: boolean;
  onSigninStart?: () => void;
  onSigninComplete?: () => void;
  onSigninError?: () => void;
}

const FacebookSigninButton: React.FC<FacebookSigninButtonProps> = ({
  className = '',
  disabled = false,
  onSigninStart,
  onSigninComplete,
  onSigninError,
}) => {
  const { signInWithFacebook } = useSocialSignin();

  const handleClick = async () => {
    try {
      onSigninStart?.();
      await signInWithFacebook();
      onSigninComplete?.();
    } catch (error) {
      onSigninError?.();
    }
  };

  return (
    <SocialSigninButton
      className={className}
      disabled={disabled}
      onClick={handleClick}
    >
      <FacebookLogo />
      <span>Continue with Facebook</span>
    </SocialSigninButton>
  );
};

export default FacebookSigninButton;
