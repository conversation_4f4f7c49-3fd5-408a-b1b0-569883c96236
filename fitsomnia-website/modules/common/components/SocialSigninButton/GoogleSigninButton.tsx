import React from 'react';
import SocialSigninButton from './SocialSigninButton';
import GoogleLogo from '../../icons/googleLogo';
import { useSocialSignin } from '@/hooks/useSocialSignin';

interface GoogleSigninButtonProps {
  className?: string;
  disabled?: boolean;
  onSigninStart?: () => void;
  onSigninComplete?: () => void;
  onSigninError?: () => void;
}

const GoogleSigninButton: React.FC<GoogleSigninButtonProps> = ({
  className = '',
  disabled = false,
  onSigninStart,
  onSigninComplete,
  onSigninError,
}) => {
  const { signInWithGoogle } = useSocialSignin();

  const handleClick = async () => {
    try {
      onSigninStart?.();
      await signInWithGoogle();
      onSigninComplete?.();
    } catch (error) {
      onSigninError?.();
    }
  };

  return (
    <SocialSigninButton
      className={className}
      disabled={disabled}
      onClick={handleClick}
    >
      <GoogleLogo />
      <span>Continue with Google</span>
    </SocialSigninButton>
  );
};

export default GoogleSigninButton;
