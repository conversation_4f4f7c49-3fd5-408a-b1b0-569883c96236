interface ICheckOutline {
  size?: number;
  fill?: string;
}

const QuestionIcon: React.FC<ICheckOutline> = ({
  size,
  fill,
}: ICheckOutline) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      version="1.0"
      viewBox="0 0 225 225"
      fill={fill ? fill : 'currentColor'}
      className={size ? `h-${size} w-${size}` : 'h-6 w-6'}
    >
      <path d="M21.5 37.9c-5 2.2-10.1 7.9-11.5 12.8-.8 2.6-1 18.9-.8 51.5l.3 47.6 3 4.4c4.8 6.9 10 8.9 23.7 9.1l11.3.2.3 16.9.3 17 17.2-17.1 17.2-17.1h59.1c57.6 0 59.2-.1 63.1-2.1 4.4-2.2 8.9-7 10.5-11 .6-1.6 1-20.7 1.1-50.7V51.2l-2.7-4.5c-2.8-5-6.2-7.8-11.5-9.5-2.5-.9-27.2-1.2-90-1.2-81.6 0-86.8.1-90.6 1.9zm178.9 9.3c1.4.6 3.4 2.3 4.3 3.7 1.6 2.4 1.8 6.5 2.1 47.6.2 41.4.1 45.3-1.5 48.5-3.7 7.2-1.8 7-68.3 7H77.5l-10.2 10.2L57 174.5V154.2l-15.2-.4c-16.6-.3-19.3-1.1-21.6-6.4-.9-1.8-1.2-15.9-1.2-48.5V52.8l3.4-3.4 3.4-3.4h86.1c62.9 0 86.7.3 88.5 1.2z" />
      <path d="M101.5 60.5C95 61.9 88 64.9 88 66.2c0 .5 1.1 3.1 2.5 5.7l2.4 4.9 6.8-2.4c7.4-2.6 13.2-3 16.6-1.3 3 1.6 3.7 2.8 3.7 6.7 0 3.6-1.5 5.4-10.3 12.5-5.5 4.4-7.2 7.4-8.2 14l-.7 4.7H115v-3.1c0-3.3 1.9-5.5 11.7-13.6 6.1-5.1 8.3-9.4 8.3-16.6 0-9-5.9-15.4-16.1-17.6-6.3-1.4-9.8-1.3-17.4.4zM105.3 120.7c-3.2.6-5.3 4.1-5.3 8.6 0 6.9 6.5 10.7 13.3 7.6 6.4-2.9 6-13.6-.6-15.9-3.1-1-3.5-1-7.4-.3z" />
    </svg>
  );
};

export default QuestionIcon;
