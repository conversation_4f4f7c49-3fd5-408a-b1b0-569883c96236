interface ICheckOutline {
  size?: number;
  fill?: string;
}

const AnswerIcon: React.FC<ICheckOutline> = ({ size, fill }: ICheckOutline) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      version="1.0"
      viewBox="0 0 512 512"
      fill={fill ? fill : 'currentColor'}
      className={size ? `h-${size} w-${size}` : 'h-6 w-6'}
    >
      <path d="M50.1 1.5C32.3 5.4 15.3 18.7 7.2 35.1-.4 50.5 0 39.9 0 208c0 128.6.2 151.5 1.5 157.4 3.9 18.3 17 35.2 33.6 43.4 14.9 7.4 11.4 7.2 113.7 7.2H240v41.2c0 38.1.2 41.6 1.9 45.4 3.2 7 10 10.4 17.8 8.9 3.4-.6 9.4-6.2 51.3-48.1l47.5-47.4h48c54.4 0 56.3-.2 70.4-7.2 16.6-8.2 29.7-25.1 33.6-43.4 2.2-10.4 2.2-304.4 0-314.8-5-23.9-25.2-44.1-49.1-49.1C451.3-.6 60-.6 50.1 1.5zm411 33.6c7.1 3.3 12.5 8.7 15.8 15.8l2.6 5.6v303l-2.6 5.6c-3.3 7.1-8.7 12.5-15.8 15.8l-5.6 2.6-54.5.5c-39.5.4-55.1.8-56.5 1.7-1.1.6-17.7 17-37 36.2l-35 35.1-.5-31.4c-.5-29.8-.6-31.6-2.6-34.2-1.1-1.5-3.3-3.7-4.8-4.8-2.7-2.1-3.7-2.1-105.4-2.6l-102.7-.5-5.6-2.6c-7.1-3.3-12.5-8.7-15.8-15.8l-2.6-5.6-.3-149.4C31.9 46 31.6 55.5 38 45.9c3.6-5.5 9.1-9.6 16.3-12 5-1.8 14.7-1.8 203.2-1.6l198 .2 5.6 2.6z" />
      <path d="M249.4 97.9c-2.3 1-5 3.2-6.1 4.7-1.9 2.7-96.1 206.1-97 209.4-.3 1.2 3.1 3.2 13.3 7.8 7.5 3.5 14.1 6.1 14.6 6 .5-.2 8-15.8 16.8-34.8l15.9-34.5h98.2L321 291c8.8 19 16.3 34.6 16.8 34.8.5.1 7.1-2.5 14.6-6 10.2-4.6 13.6-6.6 13.3-7.8-.9-3.3-95.1-206.7-97-209.4-2.2-3.1-8.8-6.6-12.7-6.6-1.4 0-4.3.9-6.6 1.9zm23.8 89.4 16.6 36.2-16.9.3c-9.3.1-24.5.1-33.8 0l-16.9-.3 16.6-36.2c9.2-20 16.9-36.3 17.2-36.3.3 0 8 16.3 17.2 36.3z" />
    </svg>
  );
};

export default AnswerIcon;
