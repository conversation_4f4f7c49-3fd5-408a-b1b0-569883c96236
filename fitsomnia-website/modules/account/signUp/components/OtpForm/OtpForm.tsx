import React, { useEffect, useState } from 'react';
import { Formik, Field, Form, ErrorMessage } from 'formik';
import Link from 'next/link';
import useTranslation from 'next-translate/useTranslation';
import { useAppSelector } from 'store/hooks';
import { otpSchema } from '@/modules/account/schemas/forgotPassword.schema';

interface User {
  username: string;
  name: string;
  password: string;
  otp: number;
}

interface Props {
  handleOtpFormSubmit: Function;
}

export const OtpForm: React.FC<Props> = ({ handleOtpFormSubmit }) => {
  const { t } = useTranslation();
  const otp = useAppSelector((state) => state.persistedReducer.signUp.otp);

  // console.log('otp',otp)

  return (
    <>
      <div data-testid="hygen">
        <Formik
          initialValues={{
            otp:'',
          }}
          onSubmit={(values, actions) => {
            handleOtpFormSubmit(values.otp);
            actions.setSubmitting(false);
          }}
          validationSchema={otpSchema}
        >
          {(formikprops) => {
            return (
              <Form onSubmit={formikprops.handleSubmit}>
                <>
                  <div className="mb-4">
                    <Field
                      type="number"
                      className="w-full p-2 placeholder-gray-600 outline-0"
                      id="otp"
                      name="otp"
                      placeholder={t('register:otp')}
                    />
                    <div className="errMsg text-red-600">
                      <ErrorMessage name="otp" />
                    </div>
                  </div>
                </>

                <div className="flex flex-wrap justify-end sm:justify-end md:justify-between lg:justify-between xl:justify-between">
                  <button
                    type="submit"
                    className={`my-2 w-full rounded bg-primary py-2 capitalize text-white transition-all duration-500 ease-in-out hover:bg-black dark:bg-dark_primary `}
                  >
                    {t('common:submit')}
                  </button>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
    </>
  );
};
