import { registerSchema } from '@/modules/account/schemas/registerSchema';
import { ErrorMessage, Field, Form, Formik } from 'formik';
import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import React, { useState } from 'react';

interface UserDetailsFormProps {
  handleUserDetailsFormSubmit: Function;
}

export const UserDetailsForm: React.FC<UserDetailsFormProps> = ({
  handleUserDetailsFormSubmit,
}) => {
  const { t } = useTranslation();
  const [passwordVisible, setPasswordVisible] = useState(false);
   const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);

  return (
    <>
      <div data-testid="hygen">
        <Formik
          initialValues={{
            username: '',
            name: '',
            password: '',
            confirmPassword: '',
            agreeToTerms: false,
          }}
          onSubmit={(values, actions) => {
            handleUserDetailsFormSubmit(values);
            actions.setSubmitting(false);
          }}
          validationSchema={registerSchema}
        >
          {(formikprops) => {
            return (
              <Form onSubmit={formikprops.handleSubmit}>
                <>
                  <div className="mb-4">
                    <Field
                      type="text"
                      className="w-full rounded-lg border-2 p-2 placeholder-gray-600 outline-0"
                      id="username"
                      name="username"
                      placeholder={t('register:username')}
                    />
                    <div className="errMsg text-red-600">
                      <ErrorMessage name="username" />
                    </div>
                  </div>
                </>

                <>
                  <div className="mb-4">
                    <Field
                      type="text"
                      className="w-full rounded-lg border-2 p-2 placeholder-gray-600 outline-0"
                      id="name"
                      name="name"
                      placeholder={t('register:name')}
                    />
                    <div className="errMsg text-red-600">
                      <ErrorMessage name="name" />
                    </div>
                  </div>
                </>

                <>
                  {/* <div className="mb-4">
                    <Field
                      type="password"
                      className="w-full rounded-lg border-2 p-2 placeholder-gray-600 outline-0"
                      id="password"
                      name="password"
                      placeholder={t('register:password')}
                    />
                    <div className="errMsg text-red-600">
                      <ErrorMessage name="password" />
                    </div>
                  </div> */}
                  <div className="relative mb-4">
                    <Field
                      type={passwordVisible ? 'text' : 'password'}
                      className="w-full rounded-lg border-2 p-2 pr-10 placeholder-gray-600 outline-0"
                      id="password"
                      name="password"
                      placeholder="Password"
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-3 text-primary"
                      onClick={() => setPasswordVisible(!passwordVisible)}
                    >
                      {passwordVisible ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          strokeWidth={2}
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M13.875 18.825A10.582 10.582 0 0112 19c-4.5 0-8.5-3.5-10-7 1.5-3.5 5.5-7 10-7 1.25 0 2.5.25 3.625.7M9.75 14.25l4.5-4.5M3 3l18 18"
                          />
                        </svg>
                      ) : (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          strokeWidth={2}
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M12 4c5 0 9 3.5 10 7-1 3.5-5 7-10 7-5 0-9-3.5-10-7 1-3.5 5-7 10-7zm0 3.5a3.5 3.5 0 110 7 3.5 3.5 0 010-7z"
                          />
                        </svg>
                      )}
                    </button>
                    <div className="errMsg text-red-600">
                      <ErrorMessage name="password" />
                    </div>
                  </div>
                </>
                <>
                  <div className="relative mb-4">
                    <Field
                      type={confirmPasswordVisible ? 'text' : 'password'}
                      className="w-full rounded-lg border-2 p-2 pr-10 placeholder-gray-600 outline-0"
                      id="confirmPassword"
                      name="confirmPassword"
                      placeholder="Confirm Password"
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-3 text-primary"
                      onClick={() =>
                        setConfirmPasswordVisible(!confirmPasswordVisible)
                      }
                    >
                      {confirmPasswordVisible ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          strokeWidth={2}
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M13.875 18.825A10.582 10.582 0 0112 19c-4.5 0-8.5-3.5-10-7 1.5-3.5 5.5-7 10-7 1.25 0 2.5.25 3.625.7M9.75 14.25l4.5-4.5M3 3l18 18"
                          />
                        </svg>
                      ) : (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          strokeWidth={2}
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M12 4c5 0 9 3.5 10 7-1 3.5-5 7-10 7-5 0-9-3.5-10-7 1-3.5 5-7 10-7zm0 3.5a3.5 3.5 0 110 7 3.5 3.5 0 010-7z"
                          />
                        </svg>
                      )}
                    </button>
                    <div className="errMsg text-red-600">
                      <ErrorMessage name="confirmPassword" />
                    </div>
                  </div>
                </>
                <>
                  <div className="mb-4 flex items-start">
                    <Field
                      type="checkbox"
                      id="agreeToTerms"
                      name="agreeToTerms"
                      className="mr-2 mt-2"
                    />
                    <label htmlFor="agreeToTerms" className="text-gray-600 font-medium">
                      I agree with Fitsomnia’s{' '}
                      <Link
                        href="/terms-of-service?prev=landing"
                        className="text-primary underline"
                      >
                        Terms & Conditions
                      </Link>{' '}
                      and{' '}
                      <Link
                        href="/privacy-policy?prev=landing"
                        className="text-primary underline"
                      >
                        Privacy Policies
                      </Link>
                    </label>
                  </div>
                  <div className="errMsg text-red-600">
                    <ErrorMessage name="agreeToTerms" />
                  </div>
                </>

                <div className="flex flex-wrap justify-end sm:justify-end md:justify-between lg:justify-between xl:justify-between">
                  <button
                    type="submit"
                    className={`my-2 w-full rounded-full bg-primary py-3 capitalize text-white transition-all duration-500 ease-in-out hover:bg-black dark:bg-dark_primary `}
                  >
                    {t('register:signup')}
                  </button>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
    </>
  );
};
