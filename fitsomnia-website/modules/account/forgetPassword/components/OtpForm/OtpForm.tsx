import React from 'react';
import { Formik, Field, Form, ErrorMessage } from 'formik';
import Link from 'next/link';
import useTranslation from 'next-translate/useTranslation';
import { useAppSelector } from 'store/hooks/index';
import { otpSchema } from '@/modules/account/schemas/forgotPassword.schema';

interface Props {
  setSubmitButtonState: Function;
  handleOtpFormSubmit: Function;
}

export const OtpForm: React.FC<Props> = ({
  setSubmitButtonState,
  handleOtpFormSubmit,
}) => {
  const { t } = useTranslation();
  const otpDetail = useAppSelector(
    (state) => state.persistedReducer.forgetPassword
  );

  return (
    <>
      <div data-testid="hygen">
        <Formik
          initialValues={{
            otp: otpDetail.otp,
          }}
          onSubmit={(values, actions) => {
            let regex = new RegExp('[a-z0-9]+@[a-z]+.[a-z]{2,3}');
            const isEmail = regex.test(otpDetail.username);
            let data;
            isEmail
              ? (data = {
                  email: otpDetail.username,
                  otp: values.otp,
                })
              : (data = {
                  phone: otpDetail.username,
                  otp: values.otp,
                });
            handleOtpFormSubmit(data);
            actions.setSubmitting(false);
          }}
          validationSchema={otpSchema}
        >
          {(formikprops) => {
            return (
              <Form onSubmit={formikprops.handleSubmit}>
                <>
                  <div className="mb-4">
                    <Field
                      type="number"
                      className="w-full p-2 placeholder-gray-600 outline-0"
                      id="otp"
                      name="otp"
                      placeholder={t('register:otp')}
                    />
                    <div className="errMsg text-red-600">
                      <ErrorMessage name="otp" />
                    </div>
                  </div>
                </>

                <div className="flex flex-wrap justify-end sm:justify-end md:justify-between lg:justify-between xl:justify-between">
                  <button
                    type="submit"
                    className={`my-2 w-full rounded bg-primary py-2 capitalize text-white transition-all duration-500 ease-in-out hover:bg-black dark:bg-dark_primary  sm:w-full md:w-1/4 lg:w-1/4 xl:w-1/4 `}
                  >
                    {t('common:submit')}
                  </button>

                  <div
                    id="cancelDiv"
                    className="text-decoration-none font-weight-light my-0 text-gray-600 hover:text-gray-500 sm:my-0 md:my-3 lg:my-3 xl:my-3"
                  >
                    <Link prefetch={false} href="/account/sign-in">
                      {t('common:cancel')}
                    </Link>
                  </div>
                </div>

                <div className="text-gray-500">
                  {t('forgot-password:otp_not_received')}{' '}
                  <button
                    onClick={() => {
                      setSubmitButtonState('username');
                    }}
                    className="hover:text-primary"
                  >
                    {t('forgot-password:try_again')}
                  </button>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
    </>
  );
};
