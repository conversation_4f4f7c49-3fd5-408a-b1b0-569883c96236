import { ErrorMessage, Field, Form, Formik } from 'formik';
import Link from 'next/link';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import { informationSchema } from '@/modules/checkout/components/schemas/checkout.schema';
import ChevronLeft from '@/modules/common/icons/chevronLeft';
import { userAPI } from 'APIs';
import { TagType, UserAddress } from 'models';
import useTranslation from 'next-translate/useTranslation';
import { useAppDispatch, useAppSelector } from 'store/hooks/index';
import { storeUserToken } from 'store/slices/authSlice';
import { addToShippingInfo } from 'store/slices/checkoutSlice';
import { storeAddresses } from 'store/slices/customerAddressSlice';
import FieldTemplate from '../common/formInput/fieldTemplate';
import { storeSummary } from 'store/slices/checkoutSummary';
import Loading from '@/modules/common/loader';

interface FormData {
  email: string;
  contact: string;
  sendNotificationCheckbox?: string;
  firstName: string;
  lastName: string;
  country: string;
  state: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  postCode: string;
  tag: TagType;
}

interface Props {
  setModal: Function;
}

const InformationForm: React.FC<Props> = ({ setModal }: Props) => {
  const dispatch = useAppDispatch();
  const { t } = useTranslation();

  const [dropdownText, setDropdownText] = useState('Use a new address');
  const [showLabel, setShowLabel] = useState(true);
  const [loading, setLoading] = useState(false);

  const cartData = useAppSelector(
    (state) => state.persistedReducer.cart.allCartItems
  );

  const productInfo = cartData.map((data) => {
    return {
      productId: data.productId,
      quantity: data.quantity,
      photo: data.product?.photos![0].url,
    };
  });

  const user = useAppSelector(
    (state) => state.persistedReducer.user.customerDetails
  );

  const shippingInfo = useAppSelector(
    (state) => state.persistedReducer.checkout.shippingInfo!
  );

  const addresses = useAppSelector(
    (state) => state.persistedReducer.UserAddress.addresses
  );

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  const UserAddresses = useAppSelector(
    (state) => state.persistedReducer.UserAddress.addresses
  );

  let initialValues = {
    email: user?.email,
    contact: shippingInfo?.phone
      ? shippingInfo?.phone
      : addresses?.length > 0
      ? addresses[0]?.phone
      : '',
    firstName: shippingInfo?.firstName
      ? shippingInfo?.firstName
      : addresses?.length > 0
      ? addresses[0]?.firstName
      : '',
    lastName: shippingInfo?.lastName
      ? shippingInfo?.lastName
      : addresses?.length > 0
      ? addresses[0]?.lastName
      : '',
    addressLine1: shippingInfo?.addressLine1
      ? shippingInfo?.addressLine1
      : addresses?.length > 0
      ? addresses[0]?.addressLine1
      : '',
    addressLine2: shippingInfo?.addressLine2
      ? shippingInfo?.addressLine2
      : addresses?.length > 0
      ? addresses[0]?.addressLine2
      : '',
    city: shippingInfo?.city
      ? shippingInfo?.city
      : addresses?.length > 0
      ? addresses[0]?.city
      : '',
    country: shippingInfo?.country
      ? shippingInfo?.country
      : addresses?.length > 0
      ? addresses[0]?.country
      : '',
    state: shippingInfo?.state
      ? shippingInfo?.state
      : addresses?.length > 0
      ? addresses[0]?.state
      : '',
    postCode: shippingInfo?.postCode
      ? shippingInfo?.postCode
      : addresses?.length > 0
      ? addresses[0]?.postCode
      : '',
    tag: shippingInfo?.tag
      ? shippingInfo?.tag
      : addresses?.length > 0
      ? addresses[0]?.tag
      : TagType.OTHERS,
    tag2: TagType.HOME,
  };

  const [update, setUpdate] = useState(initialValues);
  const [tags, setTags] = useState<string[]>([]);
  const [checkoutSummary, setCheckoutSummary] = useState();
  const [validated, setValidated] = useState(true);

  const setTagsOptions = () => {
    const ntags = new Set<TagType>();
    UserAddresses?.map((addressn) => {
      ntags.add(addressn?.tag);
    });
    const nArray: Array<TagType> = [];
    ntags.forEach((tag) => nArray.push(tag));
    nArray.length === tags.length ? '' : setTags(nArray);
  };

  const handlePreviousAddress = (detail: string, setFieldValue: Function) => {
    if (detail === 'Use a new address') {
      setShowLabel(true);
      setFieldValue('firstName', '');
      setFieldValue('lastName', '');
      setFieldValue('addressLine1', '');
      setFieldValue('addressLine2', '');
      setFieldValue('city', '');
      setFieldValue('postCode', '');
      setFieldValue('contact', '');
    } else {
      setShowLabel(false);
      const selectedAddress = addresses.find((address) => {
        return address.tag === detail;
      });
      setFieldValue('firstName', selectedAddress?.firstName);
      setFieldValue('lastName', selectedAddress?.lastName);
      setFieldValue('addressLine1', selectedAddress?.addressLine1);
      setFieldValue('addressLine2', selectedAddress?.addressLine2);
      setFieldValue('city', selectedAddress?.city);
      setFieldValue('postCode', selectedAddress?.postCode);
      setFieldValue('contact', selectedAddress?.phone);
    }
  };

  const handleCheckoutSubmit = async (data: FormData) => {
    try {
      setLoading(true);
      const toValidate = {
        firstName: data?.firstName!,
        lastName: data?.lastName!,
        email: user.email!,
        addressLine1: data?.addressLine1!,
        addressLine2: data?.addressLine2 ? data.addressLine2! : '',
        city: data?.city!,
        state: data?.state!,
        country: data?.country!,
        postCode: data?.postCode!,
        phone: data?.contact!,
      };
      const validateAddressRes = await userAPI.validateAddress(toValidate);
      if ('data' in validateAddressRes) {
        const shippingData = {
          firstName: data.firstName,
          lastName: data.lastName,
          email: user?.email!,
          addressLine1: data.addressLine1,
          addressLine2: data.addressLine2,
          city: data.city,
          state: data?.state,
          country: data?.country,
          postCode: data.postCode,
          phone: data.contact,
          tag: data.tag,
        };
        dispatch(addToShippingInfo(shippingData));
        submitCheckoutSummary(shippingData);
      } else {
        toast.error(JSON.parse(validateAddressRes?.error.message)[0].message, {
          containerId: 'bottom-right',
        });
        setLoading(false);
      }
    } catch (error) {}
  };

  const submitCheckoutSummary = async (shippingData: any) => {
    try {
      const data = {
        products: productInfo,
        billingAddress: shippingData!,
        shippingAddress: shippingData!,
        paymentMethod: '',
      };
      const res = await userAPI.createCheckoutSummary(data);
      if ('data' in res) {
        setCheckoutSummary(res.data);
        dispatch(storeSummary(res.data));
        const obj = {
          info: false,
          ship: true,
          pay: false,
        };
        setModal(obj);
      } else {
        toast.error(res.error.message!, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
    setLoading(false);
  };

  useEffect(() => {
    shippingInfo?.tag
      ? setDropdownText(shippingInfo.tag)
      : addresses.length > 0
      ? setDropdownText(addresses[0].tag)
      : setDropdownText('Use a new address');
    addresses?.length > 0 ? setShowLabel(false) : setShowLabel(true);
    setTagsOptions();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tags, shippingInfo]);

  useEffect(() => {
    setTagsOptions();
  });

  const setAddCustomerNewAddress = async (data: UserAddress) => {
    try {
      const toValidate = {
        firstName: data?.firstName,
        lastName: data?.lastName,
        email: user.email,
        addressLine1: data?.addressLine1,
        addressLine2: data?.addressLine2 ? data.addressLine2 : '',
        city: data?.city,
        state: data?.state,
        country: data?.country,
        postCode: data?.postCode!,
        phone: data?.phone,
      };
      const res = await userAPI.validateAddress(toValidate);
      if ('data' in res) {
        setValidated(true);
        await userAPI.addCustomerNewAddress(data);
        await userAPI.getCustomer(token).then((response) => {
          dispatch(storeAddresses(response?.data?.addresses!));
        });
        setTagsOptions();
      } else {
        toast.error(JSON.parse(res?.error.message)[0].message, {
          containerId: 'bottom-right',
        });
        setValidated(false);
      }
      setDropdownText('');
      setShowLabel(false);
    } catch (error) {}
  };

  if (loading) return <Loading />;

  return (
    <div className="">
      <Formik
        enableReinitialize={true}
        initialValues={update}
        onSubmit={(values, actions) => {
          const data = {
            email: user?.email!,
            contact: values.contact,
            firstName: values.firstName,
            lastName: values.lastName,
            addressLine1: values.addressLine1,
            addressLine2: values.addressLine2 ? values.addressLine2 : '',
            city: values.city ? values.city : '',
            country: values.country ? values.country : '',
            state: values.state ? values.state : '',
            postCode: values.postCode!,
            tag:
              dropdownText === 'Use a new address' ? values.tag2 : values.tag,
          };
          const addressData = {
            phone: values.contact,
            firstName: values.firstName,
            lastName: values.lastName,
            addressLine1: values.addressLine1,
            addressLine2: values.addressLine2,
            city: values.city,
            country: values.country,
            state: values.state,
            postCode: values.postCode,
            tag:
              dropdownText === 'Use a new address' ? values.tag2 : values.tag,
          };
          if (dropdownText === 'Use a new address') {
            setAddCustomerNewAddress(addressData);
            validated && handleCheckoutSubmit(data);
          } else {
            handleCheckoutSubmit(data);
          }
          actions.setSubmitting(false);
        }}
        validationSchema={informationSchema}
      >
        {(formikprops) => {
          return (
            <>
              <Form onSubmit={formikprops.handleSubmit}>
                <div className="mt-8">
                  <p className="text-lg">{t('checkout:shipping_address')}</p>

                  <div className="mt-5">
                    <div className="mb-3">
                      <Field
                        as="select"
                        id="tag"
                        name="tag"
                        className="input required peer block w-full rounded border border-gray-300  p-4 text-sm text-gray-500 focus:border-2 focus:border-black focus:outline-none focus:ring-0 dark:bg-dark_bg dark:text-dark_text dark:focus:border-gray-300"
                        onClick={(
                          event: React.ChangeEvent<HTMLInputElement>
                        ) => {
                          setDropdownText(event.target.value);
                          handlePreviousAddress(
                            event.target.value,
                            formikprops.setFieldValue
                          );
                        }}
                      >
                        <option>{dropdownText}</option>
                        {tags
                          ? tags?.map((tag: string) => {
                              return (
                                <React.Fragment key={tag}>
                                  {tag !== dropdownText ? (
                                    <option>{`${tag}`}</option>
                                  ) : null}
                                </React.Fragment>
                              );
                            })
                          : null}
                        {dropdownText !== 'Use a new address' ? (
                          <option>Use a new address</option>
                        ) : null}
                      </Field>
                      <div className="errMsg text-red-600">
                        <ErrorMessage name="tag" />
                      </div>
                    </div>
                    <div className="row">
                      <div className="grid grid-cols-1 gap-0 sm:grid-cols-1 sm:gap-0 md:grid-cols-2 md:gap-4 lg:grid-cols-2 lg:gap-4 xl:grid-cols-2 xl:gap-4">
                        <FieldTemplate
                          label={t('checkout:first_name')}
                          fieldID="firstName"
                          fieldType="text"
                          placeholder=" "
                        />
                        <FieldTemplate
                          label={t('checkout:last_name')}
                          fieldID="lastName"
                          fieldType="text"
                          placeholder=" "
                        />
                      </div>
                    </div>

                    <FieldTemplate
                      label={t('checkout:mobile_number')}
                      fieldID="contact"
                      fieldType="text"
                      placeholder=" "
                    />

                    <FieldTemplate
                      label={t('checkout:address_line1')}
                      fieldID="addressLine1"
                      fieldType="text"
                      placeholder=" "
                      extraClass="mb-3"
                    />

                    <FieldTemplate
                      label={t('checkout:address_line2')}
                      fieldID="addressLine2"
                      fieldType="text"
                      placeholder=" "
                      extraClass="mb-3"
                    />

                    <div className="row">
                      <div className="grid grid-cols-1 gap-0 sm:grid-cols-1 sm:gap-0 md:grid-cols-2 md:gap-4 lg:grid-cols-2 lg:gap-4 xl:grid-cols-2 xl:gap-4">
                        <FieldTemplate
                          label={t('checkout:city')}
                          fieldID="city"
                          fieldType="text"
                          placeholder=" "
                          extraClass="mb-3"
                        />

                        <FieldTemplate
                          label="State"
                          fieldID="state"
                          fieldType="text"
                          placeholder=" "
                          extraClass="mb-3"
                        />
                      </div>
                    </div>
                    <FieldTemplate
                      label="Country"
                      fieldID="country"
                      fieldType="text"
                      placeholder=" "
                      extraClass="mb-3"
                    />
                    <FieldTemplate
                      label={t('checkout:postal_code')}
                      fieldID="postCode"
                      fieldType="text"
                      placeholder=" "
                      extraClass="mb-3"
                    />
                  </div>
                </div>

                {showLabel ? (
                  <div>
                    <div className="">
                      <div className="mb-3">
                        <label htmlFor="tag2" className="pb-8 text-sm">
                          {t('checkout:tag2_label')}
                        </label>

                        <div className="mt-2">
                          <Field
                            required
                            id="tag2"
                            name="tag2"
                            as="select"
                            className="w-full border py-3 px-3 leading-tight text-gray-700 focus:border-gray-500 focus:bg-white focus:outline-none focus:grayscale dark:text-dark_text dark:focus:bg-gray-700"
                          >
                            <option value={TagType.HOME}>{TagType.HOME}</option>
                            <option value={TagType.OFFICE}>
                              {TagType.OFFICE}
                            </option>
                            <option value={TagType.OTHERS}>
                              {TagType.OTHERS}
                            </option>
                          </Field>
                          {/* <FieldTemplate
                            fieldID="tag2"
                            fieldType="text"
                            placeholder="E.g. Home, Office, Others etc."
                            isRequired={true}
                          /> */}
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <></>
                )}

                <div className="flex flex-col flex-wrap items-center gap-5 sm:flex-col md:flex-row">
                  <button
                    type="submit"
                    className="my-2 w-full rounded bg-black p-3 text-sm text-white dark:bg-dark_primary sm:w-full md:w-44"
                  >
                    {t('checkout:continue_to_shipping')}
                  </button>
                  <div className="flex flex-wrap items-center">
                    <div className="text-decoration-none block items-center sm:block sm:items-center md:hidden lg:hidden xl:hidden">
                      <Link prefetch={false} href="/cart" passHref>
                        {<ChevronLeft height="h-5" width="h-5" />}
                      </Link>
                    </div>
                    <div className="text-decoration-none">
                      <Link prefetch={false} href="/cart" passHref>
                        {t('checkout:return_to_cart')}
                      </Link>
                    </div>
                  </div>
                </div>
              </Form>
            </>
          );
        }}
      </Formik>
    </div>
  );
};

export default InformationForm;
