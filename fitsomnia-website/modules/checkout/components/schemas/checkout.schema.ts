import { object, string } from 'yup';

export const informationSchema = object().shape({
  email: string().required('Enter an email.'),
  contact: string().required(
    'Enter a phone number.'
  ),
  firstName: string().required('Enter a first name'),
  lastName: string().required('Enter a last name'),
  addressLine1: string().required('Enter an address'),
  addressLine2: string(),
  city: string().required('Enter a city name'),
  state: string().required('Enter a state name'),
  country: string().required('Enter a country name'),
  postCode: string()
    .matches(/^[0-9\+]*$/, 'This field only contains digits')
    .required('Enter a  postal code'),
});

export const paymentSchema = object().shape({
  paymentMethod: string().required('Select a payment method'),
  shippingAddressPicked: string(),
  firstName: string(),
  lastName: string(),
  phone: string(),
  addressLine1: string(),
  addressLine2: string(),
  city: string(),
  state: string(),
  country: string(),
  postCode: string(),
});
