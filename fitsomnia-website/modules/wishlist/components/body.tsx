import Image from 'next/legacy/image';
import Link from 'next/link';
import React from 'react';

import { WishlistItem } from 'models';
import { useAppSelector } from 'store/hooks/index';

import ProductHoverActions from '@/modules/common/product/common/productHoverActions';
import SingleProductInfo from '@/modules/wishlist/components/singleProductInfo';
import myImageLoader from 'image/loader';

interface Props {
  productImageHeight: number;
  productImageWidth: number;
}

const WishlistBody: React.FC<Props> = (props) => {
  const { productImageHeight, productImageWidth } = props;
  const wishlistData = useAppSelector(
    (state) => state.persistedReducer.product.wishlist
  );
  return (
    <>
      {wishlistData?.items?.map((data: WishlistItem, index: number) => {
        return (
          <React.Fragment key={data.productId}>
            <div className="flex flex-col flex-wrap items-center">
              <Link
                prefetch={false}
                href={{
                  pathname: `product/${data?.product?.meta?.friendlyPageName}`,
                }}
                passHref
                legacyBehavior
              >
                <div className="w-50 relative flex cursor-pointer flex-col items-center justify-center">
                  <Image
                    loader={myImageLoader}
                    src={data.product?.photos![0].url!}
                    alt="Wishlist Product Image"
                    width={productImageWidth}
                    height={productImageHeight}
                  />

                  <div className="absolute inset-0 z-10 flex items-center justify-center font-semibold text-black opacity-0 duration-300 hover:-translate-y-3 hover:opacity-70">
                    <ProductHoverActions product={data?.product!} />
                  </div>

                  <div className="text-center">
                    <SingleProductInfo product={data?.product!} />
                  </div>
                </div>
              </Link>
            </div>
          </React.Fragment>
        );
      })}
    </>
  );
};

export default WishlistBody;
