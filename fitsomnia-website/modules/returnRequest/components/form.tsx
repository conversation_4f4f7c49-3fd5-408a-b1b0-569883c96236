import { ErrorMessage, Field, Form, Formik } from 'formik';
import myImageLoader from 'image/loader';
import Image from 'next/image';
import { useState } from 'react';

const FormComponent = () => {
  const [file, setFile] = useState<any>();

  return (
    <div className="m-5 my-3 sm:m-5 md:mx-10">
      <Formik
        initialValues={{
          reason: '',
          file: null,
          refund: false,
          agree: false,
        }}
        onSubmit={(values, actions) => {
          actions.setSubmitting(false);
        }}
        //validationSchema={registerSchema}
      >
        {(formikprops) => {
          return (
            <Form onSubmit={formikprops.handleSubmit}>
              <>
                <div className="mb-4">
                  <div className="flex flex-col md:flex-row">
                    <label className="md:mr-14">Reason: </label>
                    <Field
                      component="textarea"
                      rows="4"
                      className="w-full p-2 placeholder-gray-600 outline-0"
                      id="reason"
                      name="reason"
                    />
                  </div>
                  <div className="errMsg text-red-600">
                    <ErrorMessage name="reason" />
                  </div>
                </div>
              </>

              <>
                <div className="mb-4">
                  <div className="flex flex-col md:flex-row">
                    <label className="md:mr-5">Attachment: </label>
                    <div>
                      <input
                        id="file"
                        name="file"
                        type="file"
                        className="text-sm"
                        onChange={(
                          event: React.ChangeEvent<HTMLInputElement>
                        ) => {
                          setFile({
                            src: event?.target?.files![0],
                            type: event?.target?.files![0].type,
                          });
                        }}
                      />
                      {file && (
                        <>
                          <Image
                            loader={myImageLoader}
                            className="mt-3"
                            src={URL.createObjectURL(file.src)}
                            height={100}
                            width={100}
                            alt="animation"
                          />
                        </>
                      )}
                    </div>
                  </div>
                  <div className="errMsg text-red-600">
                    <ErrorMessage name="file" />
                  </div>
                </div>
              </>

              <div className="mb-4">
                <div className="flex">
                  <Field type="checkbox" id="refund" name="refund" />
                  <label className="ml-5">Refund</label>
                </div>
                <div className="errMsg text-red-600">
                  <ErrorMessage name="refund" />
                </div>
              </div>

              <div className="mb-4">
                <div className="flex items-center gap-1">
                  <p>Drop-off Location: </p>
                  <p className="rounded-lg bg-yellow-50 py-1 px-3">
                    Warehouse location here
                  </p>
                </div>
              </div>

              <div className="mb-4">
                <div className="flex">
                  <Field
                    type="checkbox"
                    id="agree"
                    name="agree"
                    onClick={(e: any) => {
                      if (e.target.checked) {
                        window.open('/return-policy');
                      }
                    }}
                  />
                  <label className="ml-5">Agreed to return policy?</label>
                </div>
                <div className="errMsg text-red-600">
                  <ErrorMessage name="agree" />
                </div>
              </div>

              <div className="flex flex-wrap justify-center ">
                <button
                  type="submit"
                  className={`my-2 w-full rounded bg-primary py-2 capitalize text-white transition-all duration-500 ease-in-out hover:bg-black dark:bg-dark_primary md:w-1/2 `}
                >
                  Send Request
                </button>
              </div>
            </Form>
          );
        }}
      </Formik>
    </div>
  );
};

export default FormComponent;
