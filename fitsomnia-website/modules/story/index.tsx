import { userAPI } from 'APIs';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import Carousel from 'react-multi-carousel';
import { useAppSelector } from 'store/hooks/index';
import WithAuth from '../auth/withAuth';
import StoryModal from '../common/modal/storyModal';

interface Props {
  url?: string;
}

const StoryComponent: React.FC<Props> = ({ url }: Props) => {
  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  const [story, setStory] = useState<any>([]);
  const [myStory, setMyStory] = useState<any>([]);
  const [storyModel, setStoryModel] = useState(false);
  const [choice, setChoice] = useState(false);

  // console.log('my story', myStory);

  // console.log(story);

  const fetchMyStory = async () => {
    try {
      const followerStoryRes = await userAPI.getFollowerStories(token);

      const myStoryRes = await userAPI.getMyStory(token);

      if ('data' in myStoryRes) {
        setMyStory(myStoryRes?.data);
      }

      if ('data' in followerStoryRes) {
        setStory(followerStoryRes?.data);
      }
    } catch (error) {}
  };

  useEffect(() => {
    fetchMyStory();
  }, []);

  return (
    <>
      {storyModel && (
        <StoryModal
          setModalOn={setStoryModel}
          setChoice={setChoice}
          modalTitle="Create Post"
          bodyText="Proceed to login?"
        />
      )}

      <div className="mx-auto flex max-w-4xl items-center justify-start  py-4">
        <Carousel
          additionalTransfrom={0}
          arrows={myStory.length + story.length > 3}
          autoPlaySpeed={3000}
          centerMode={false}
          className=""
          containerClass="container"
          dotListClass=""
          draggable
          focusOnSelect={false}
          infinite={false}
          itemClass=""
          keyBoardControl
          minimumTouchDrag={80}
          pauseOnHover
          renderArrowsWhenDisabled={false}
          renderButtonGroupOutside={false}
          renderDotsOutside={false}
          responsive={{
            desktop: {
              breakpoint: {
                max: 3000,
                min: 1024,
              },
              items: 4,
              partialVisibilityGutter: 0,
            },
            mobile: {
              breakpoint: {
                max: 464,
                min: 0,
              },
              items: 1,
              partialVisibilityGutter: 0,
            },
            tablet: {
              breakpoint: {
                max: 1024,
                min: 464,
              },
              items: 2,
              partialVisibilityGutter: 0,
            },
          }}
          rewind={false}
          rewindWithAnimation={false}
          rtl={false}
          shouldResetAutoplay
          showDots={false}
          sliderClass=""
          slidesToSlide={1}
          swipeable
        >
          <div
            className="flex cursor-pointer"
            // onClick={() => setStoryModel(true)}
          >
            <div className="relative h-[240px] w-[167px]">
              <div
                className="h-[200px] w-[167px] rounded-[16px] bg-cover bg-no-repeat"
                style={{
                  backgroundImage: `url(${url || '/user.png'})`, // Dynamically set image URL
                  backgroundPosition: 'center',
                  backgroundSize: 'cover',
                }}
              ></div>

              <div className="absolute bottom-0 left-0 h-[86px] w-[167px] rounded-b-[16px] border border-t-0 border-[#1EA951] bg-[#D4F7E1]"></div>

              <p className="absolute left-1/2 bottom-[10px] -translate-x-1/2 text-center font-[Poppins] text-[16px] font-medium capitalize text-[#082B15]">
                Share your Day
              </p>

              <div className="absolute left-1/2 top-[134px] flex h-[40px] w-[40px] -translate-x-1/2 items-center justify-center rounded-full bg-[#1EA951]">
                <Image
                  src="/story-plus.png"
                  alt="story plus image"
                  height={50}
                  width={50}
                />
              </div>
            </div>
          </div>

          {myStory.length > 0 && (
            <>
              <div className="relative h-[240px] w-[167px] overflow-hidden rounded-2xl">
                {myStory.length > 0 && (
                  <>
                    <Image
                      src={myStory[0]?.url}
                      alt="Profile"
                      className="absolute h-full w-full object-cover"
                      fill
                    />
                  </>
                )}
              </div>
            </>
          )}

          {story.length > 0 && (
            <div className="relative h-[240px] w-[167px] overflow-hidden rounded-2xl">
              {story.length > 0 && story[0]?.stories?.length > 0 && (
                <>
                  <Image
                    src={story[0]?.stories[0]?.url || '/user.png'}
                    alt="Profile"
                    className="absolute h-full w-full object-cover"
                    height={50}
                    width={50}
                  />
                  <div className="absolute bottom-2 left-3 flex items-center gap-1.5">
                    <div className="h-8 w-8 overflow-hidden rounded-full border-2 border-green-500">
                      <Image
                        src={story[0]?.image[0]?.profile || '/user.png'}
                        alt="Avatar"
                        className="h-full w-full object-cover"
                        height={50}
                        width={50}
                      />
                    </div>
                    <span className="text-sm font-medium capitalize text-gray-500">
                      {story[0]?.username}
                    </span>
                  </div>
                </>
              )}
            </div>
          )}
        </Carousel>
      </div>
    </>
  );
};

export default WithAuth(StoryComponent);
