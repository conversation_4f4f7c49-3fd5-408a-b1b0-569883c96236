import myImageLoader from 'image/loader';
import _ from 'lodash';
import Image from 'next/legacy/image';
import Link from 'next/link';

import { useEffect, useRef, useState } from 'react';

import { userAPI } from 'APIs';
import { ResponseItem, UpdateCartItemRequest } from 'models';
import { useAppDispatch, useAppSelector } from 'store/hooks/index';
import {
  deleteCartItem,
  storeAllCartItems,
  updateCartItem,
} from 'store/slices/cartSlice';
import { toast } from 'react-toastify';
import { trimDescription } from 'helper/trim';
import CircleIcon from '@/modules/common/icons/circleIcon';

interface Props {
  data: ResponseItem;
}

const ShowData: React.FC<Props> = ({ data }: Props) => {
  const dispatch = useAppDispatch();

  const [itemToUpdate, setItemToUpdate] = useState({
    productId: data.productId,
    quantity: data.quantity,
  });
  const currency = useAppSelector((state) => state.persistedReducer.currency);

  const handleCartItemDelete = async () => {
    try {
      const productId = data.productId;
      const res = await userAPI.deleteSingleCartItem(productId);
      if ('data' in res) dispatch(deleteCartItem(data));
      else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
  };

  const handleUpdateCart = async (updatedCartItem: UpdateCartItemRequest) => {
    try {
      const cart = await userAPI.updateCartItem(updatedCartItem);
      if ('data' in cart) {
        dispatch(storeAllCartItems(cart?.data?.items!));
        dispatch(updateCartItem(updatedCartItem));
      } else {
        toast.error(cart?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
  };

  const debouncedUpdateCartItem = useRef(
    _.debounce(async (updatedCartItem: UpdateCartItemRequest) => {
      await handleUpdateCart(updatedCartItem);
    }, 1000)
  ).current;

  useEffect(() => {
    return () => {
      debouncedUpdateCartItem.cancel();
    };
  }, [debouncedUpdateCartItem]);

  return (
    <>
      <td className="px-5 py-4">
        {' '}
        {data?.product?.photos![0]?.url ? (
          <Image
            loader={myImageLoader}
            src={data?.product?.photos![0]?.url!}
            alt="product Image"
            width={100}
            height={90}
          />
        ) : (
          'Problem Rendering Image'
        )}
      </td>
      <td className="px-5 py-4">
        <Link
          prefetch={false}
          href={{
            pathname: `/product/${data?.product?.meta?.friendlyPageName}`,
          }}
          passHref
          legacyBehavior
        >
          <p className="hover:cursor-pointer hover:text-primary">
            {trimDescription(data?.product?.info?.name!, 25)}
          </p>
        </Link>
      </td>
      <td className="px-5 py-4">{data?.size}</td>
      <td className="px-5 py-4">
        <button className={`cursor-default rounded-full p-1 text-sm `}>
          <div className="flex flex-wrap items-center gap-2">
            <CircleIcon fill={data?.color!} size={20} />
          </div>
        </button>
      </td>
      <td className="px-5 py-4">
        <span className="flex justify-center">
          {' '}
          {Intl.NumberFormat(
            `${currency.currencyLanguage}-${currency.currencyStyle}`,
            { style: 'currency', currency: `${currency.currencyName}` }
          ).format(data?.product?.info?.price!)}
          {/* ${data?.product?.info?.price} */}
        </span>
      </td>
      <td className="px-5 py-4">
        {' '}
        <div className="flex flex-col items-center justify-center gap-2">
          <div className="flex justify-center">
            <div className="box-content w-12 border-2 p-2">
              <div className="flex justify-between">
                <button
                  disabled={itemToUpdate.quantity <= 1 ? true : false}
                  onClick={() => {
                    let _quantity =
                      itemToUpdate.quantity - 1 >= 0
                        ? itemToUpdate.quantity - 1
                        : 0;
                    setItemToUpdate({
                      productId: data?.productId,
                      quantity: _quantity,
                    });

                    debouncedUpdateCartItem({
                      productId: data?.productId,
                      quantity: _quantity,
                    });
                  }}
                >
                  -
                </button>
                <div>
                  {' '}
                  {Intl.NumberFormat(
                    `${currency.currencyLanguage}-${currency.currencyStyle}`
                  ).format(itemToUpdate.quantity)}
                </div>
                {/* {itemToUpdate.quantity} */}
                <button
                  disabled={
                    data.product?.info?.stock! === itemToUpdate.quantity
                  }
                  onClick={() => {
                    let _quantity = itemToUpdate.quantity + 1;
                    setItemToUpdate({
                      productId: data?.productId,
                      quantity: _quantity,
                    });
                    debouncedUpdateCartItem({
                      productId: data?.productId,
                      quantity: _quantity,
                    });
                  }}
                >
                  +
                </button>
              </div>
            </div>
          </div>
          {data?.product?.info?.stock! <= 10 ? (
            <p className="font-medium text-error">
              {data?.product?.info?.stock!} left!
            </p>
          ) : (
            ''
          )}
        </div>
      </td>
      <td className="px-5 py-4">
        <div className="flex justify-center">
          {Intl.NumberFormat(
            `${currency.currencyLanguage}-${currency.currencyStyle}`,
            { style: 'currency', currency: `${currency.currencyName}` }
          ).format((data?.product?.info?.price! * itemToUpdate.quantity)!)}
          {/* ${data?.product?.info?.price! * itemToUpdate.quantity} */}
        </div>
      </td>
      <td className="px-5 py-4 text-primary dark:text-dark_primary">
        <div className="flex justify-center">
          <button
            className="font-bold text-primary dark:text-dark_primary hover:dark:text-white"
            onClick={handleCartItemDelete}
          >
            X
          </button>
        </div>
      </td>
    </>
  );
};

export default ShowData;
