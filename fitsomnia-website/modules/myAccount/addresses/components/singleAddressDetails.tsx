import { UserAddress } from 'models';
import Link from 'next/link';
import { useRouter } from 'next/router';

import Breadcrumb from '@/modules/common/breadcrumbs/breadcrumb';
import ChevronLeft from '@/modules/common/icons/chevronLeft';
import ChevronRight from '@/modules/common/icons/chevronRight';
import SingleAddressDetailsTableData from '@/modules/myAccount/addresses/components/singleAddressDetailsTableData';
import useTranslation from 'next-translate/useTranslation';
import { useEffect, useState } from 'react';
import { useAppSelector } from 'store/hooks/index';

const SingleAddressDetails: React.FC = () => {
  const router = useRouter();
  const addressID = router.query.addressId;
  const [singleAddress, setSingleAddress] = useState<UserAddress>();
  const { t } = useTranslation();
  const UserAddresses = useAppSelector(
    (state) => state?.persistedReducer?.UserAddress.addresses
  );

  useEffect(() => {
    const address = UserAddresses.find(
      (UserAddress) => UserAddress.id === addressID
    );
    setSingleAddress(address!);
  }, []);

  return (
    <>
      <Breadcrumb
        title={t('myAccount:order_details')}
        pathArray={[`${t('common:home')}`, `${t('myAccount:order_details')}`]}
        linkArray={['/', '/']}
      />
      <div className="container mx-auto mt-5 px-4">
        <div className="flex gap-x-4">
          <button
            className="cursor-pointer fill-primary dark:fill-dark_primary"
            onClick={() => {
              router.push('/myAccount/addresses');
            }}
          >
            <ChevronLeft height="h-9" width="w-9" />
          </button>
          <p className="text-2xl font-semibold">
            {t('myAccount:delivery_address')}
          </p>
        </div>
        <div className="mt-5 flex items-center gap-x-2">
          <Link prefetch={false} href="/myAccount/addresses" passHref>
            <p className="cursor-pointer text-sm text-[#7c827f]">
              {t('myAccount:delivery_address')}
            </p>
          </Link>
          <div className="fill-current stroke-current">
            <ChevronRight height="h-4" width="w-4" />
          </div>
          <p className="cursor-pointer text-sm">{t('myAccount:details')}</p>
        </div>

        <div className="mt-5 rounded-lg border md:w-1/2">
          <SingleAddressDetailsTableData
            label={t('myAccount:address_line')}
            text={singleAddress?.addressLine1!}
            extraClass="mt-5"
          />

          <SingleAddressDetailsTableData
            label={t('myAccount:building_name')}
            text={
              singleAddress?.addressLine2 ? singleAddress?.addressLine2 : 'N/A'
            }
          />

          <SingleAddressDetailsTableData
            label={t('myAccount:city')}
            text={singleAddress?.state!}
          />

          <SingleAddressDetailsTableData
            label={t('myAccount:post_code')}
            text={singleAddress?.postCode!}
          />

          <SingleAddressDetailsTableData
            label={t('myAccount:phone_number')}
            text={singleAddress?.phone ? singleAddress?.phone : 'N/A'}
          />
        </div>
      </div>
    </>
  );
};

export default SingleAddressDetails;
