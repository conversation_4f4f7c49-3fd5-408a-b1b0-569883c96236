import { Field, Form, Formik } from 'formik';
import useTranslation from 'next-translate/useTranslation';
import { FC } from 'react';
import { toast } from 'react-toastify';

import { userAPI } from 'APIs';
import { TagType, UserAddress } from 'models';
import { useAppDispatch, useAppSelector } from 'store/hooks/index';
import { storeAddresses } from 'store/slices/customerAddressSlice';
import { storeCustomerDetails } from 'store/slices/userSlice';
interface props {
  user?: UserAddress;
  cancelForm: Function;
  id?: string;
}
const AddressForm: FC<props> = ({ user, cancelForm, id }: props) => {
  const dispatch = useAppDispatch();
  const { t } = useTranslation();

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  const userDetails = useAppSelector(
    (state) => state.persistedReducer.user.customerDetails
  );

  const handleAddressSubmit = async (
    data: UserAddress,
    id: string,
    resetForm: Function
  ) => {
    try {
      const toValidate = {
        firstName: data?.firstName,
        lastName: data?.lastName,
        email: userDetails.email,
        addressLine1: data?.addressLine1,
        addressLine2: data?.addressLine2 ? data.addressLine2 : '',
        city: data?.city,
        state: data?.state,
        country: data?.country,
        postCode: data?.postCode!,
        phone: data?.phone,
      };

      const res = await userAPI.validateAddress(toValidate);
      if ('data' in res) {
        if (!id) {
          await userAPI.addCustomerNewAddress(data);
          resetForm();
        } else {
          await userAPI.updateUserAddress(id, data);
        }
        cancelForm('');
        const updatedCustomer = await userAPI.getCustomerProfile(token);
        if ('data' in updatedCustomer) {
          dispatch(storeAddresses(updatedCustomer?.data?.addresses!));
          dispatch(storeCustomerDetails(updatedCustomer?.data!));
        } else {
          toast.error(updatedCustomer?.error.message, {
            containerId: 'bottom-right',
          });
        }
      } else {
        toast.error(JSON.parse(res.error.message)[0].message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {
      toast.error(`Error occurred!!`, {
        containerId: 'bottom-right',
      });
    }
  };

  return (
    <>
      <div className="flex flex-wrap items-center justify-center md:items-start md:justify-start">
        <Formik
          initialValues={{
            firstName: user?.firstName ? user.firstName : '',
            lastName: user?.lastName ? user.lastName : '',
            addressLine1: user?.addressLine1 ? user.addressLine1 : '',
            addressLine2: user?.addressLine2 ? user.addressLine2 : '',
            city: user?.city ? user.city : '',
            state: user?.state ? user.state : '',
            country: user?.country ? user.country : '',
            postCode: user?.postCode ? user.postCode : '',
            phone: user?.phone ? user.phone : '',
            tag: user?.tag ? user.tag : TagType.HOME,
          }}
          onSubmit={(values, { resetForm }) => {
            const data = {
              firstName: values.firstName,
              lastName: values.lastName,
              addressLine1: values.addressLine1,
              addressLine2: values.addressLine2,
              city: values.city,
              postCode: values.postCode,
              state: values.state,
              country: values.country,
              phone: values.phone,
              tag: values.tag,
            };
            handleAddressSubmit(data, id!, resetForm);
            // actions.setSubmitting(true);
          }}
        >
          {(formikprops) => {
            return (
              <Form onSubmit={formikprops.handleSubmit}>
                <div className="mb-3">
                  <div className="grid grid-cols-1 gap-x-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2">
                    <div>
                      <label htmlFor="firstName" className="text-sm">
                        {t('manage-address:firstname')}
                      </label>
                      <br />
                      <Field
                        type="text"
                        className="w-full appearance-none border py-3 px-3 leading-tight text-gray-700 focus:border-gray-500 focus:bg-white focus:outline-none focus:grayscale dark:text-dark_text dark:focus:bg-gray-700"
                        id="firstName"
                        name="firstName"
                      />
                    </div>
                    <div>
                      <label htmlFor="lastName" className="text-sm">
                        {t('manage-address:lastname')}
                      </label>
                      <br />
                      <Field
                        type="text"
                        className="w-full appearance-none border py-3 px-3 leading-tight text-gray-700 focus:border-gray-500 focus:bg-white focus:outline-none focus:grayscale dark:text-dark_text dark:focus:bg-gray-700"
                        id="lastName"
                        name="lastName"
                      />
                    </div>
                  </div>
                </div>

                <div className="mb-3">
                  <div className="grid-cols-1">
                    <label htmlFor="addressLine1" className="text-sm">
                      {t('manage-address:address1')}
                    </label>
                    <br />
                    <Field
                      type="text"
                      className="w-full appearance-none border py-3 px-3 leading-tight text-gray-700 focus:border-gray-500 focus:bg-white focus:outline-none focus:grayscale dark:text-dark_text dark:focus:bg-gray-700"
                      id="addressLine1"
                      name="addressLine1"
                    />
                  </div>
                </div>

                <div className="mb-3">
                  <div className="grid-cols-1">
                    <label htmlFor="addressLine2" className="text-sm">
                      {t('manage-address:address2')}
                    </label>
                    <br />
                    <Field
                      type="text"
                      className="w-full appearance-none border py-3 px-3 leading-tight text-gray-700 focus:border-gray-500 focus:bg-white focus:outline-none focus:grayscale dark:text-dark_text dark:focus:bg-gray-700"
                      id="addressLine2"
                      name="addressLine2"
                    />
                  </div>
                </div>

                <div className="mb-3">
                  <label htmlFor="city" className="text-sm">
                    {t('manage-address:city')}
                  </label>
                  <br />
                  <Field
                    type="text"
                    className="w-full appearance-none border py-3 px-3 leading-tight text-gray-700 focus:border-gray-500 focus:bg-white focus:outline-none focus:grayscale dark:text-dark_text dark:focus:bg-gray-700"
                    id="city"
                    name="city"
                  />
                </div>

                <div className="mb-3">
                  <label htmlFor="state" className="text-sm">
                    State
                  </label>
                  <br />
                  <Field
                    type="text"
                    className="w-full appearance-none border py-3 px-3 leading-tight text-gray-700 focus:border-gray-500 focus:bg-white focus:outline-none focus:grayscale dark:text-dark_text dark:focus:bg-gray-700"
                    id="state"
                    name="state"
                  />
                </div>

                <div className="mb-3">
                  <label htmlFor="country" className="text-sm">
                    Country
                  </label>
                  <br />
                  <Field
                    type="text"
                    className="w-full appearance-none border py-3 px-3 leading-tight text-gray-700 focus:border-gray-500 focus:bg-white focus:outline-none focus:grayscale dark:text-dark_text dark:focus:bg-gray-700"
                    id="country"
                    name="country"
                  />
                </div>

                <div className="grid grid-cols-1 gap-x-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2">
                  <div className="mb-3">
                    <label htmlFor="postCode" className="text-sm">
                      {t('manage-address:postcode')}
                    </label>
                    <br />
                    <Field
                      type="text"
                      className="w-full appearance-none border py-3 px-3 leading-tight text-gray-700 focus:border-gray-500 focus:bg-white focus:outline-none focus:grayscale dark:text-dark_text dark:focus:bg-gray-700"
                      id="postCode"
                      name="postCode"
                    />
                  </div>
                  <div className="mb-3">
                    <label htmlFor="phone" className="text-sm">
                      {t('manage-address:phone')}
                    </label>
                    <br />
                    <Field
                      type="text"
                      className="w-full appearance-none border py-3 px-3 leading-tight text-gray-700 focus:border-gray-500 focus:bg-white focus:outline-none focus:grayscale dark:text-dark_text dark:focus:bg-gray-700"
                      id="phone"
                      name="phone"
                    />
                  </div>
                </div>
                <div className="mb-3">
                  <label htmlFor="tag" className="text-sm">
                    {t('manage-address:tag')}
                  </label>
                  <br />
                  {/* <Field
                    type="text"
                    className="w-full appearance-none border py-3 px-3 leading-tight text-gray-700 focus:border-gray-500 focus:bg-white focus:outline-none focus:grayscale dark:text-dark_text dark:focus:bg-gray-700"
                    id="tag"
                    name="tag"
                    placeholder={t('manage-address:example')}
                  /> */}
                  <Field
                    id="tag"
                    name="tag"
                    as="select"
                    className="w-full border py-3 px-3 leading-tight text-gray-700 focus:border-gray-500 focus:bg-white focus:outline-none focus:grayscale dark:text-dark_text dark:focus:bg-gray-700"
                  >
                    <option value={TagType.HOME}>{TagType.HOME}</option>
                    <option value={TagType.OFFICE}>{TagType.OFFICE}</option>
                    <option value={TagType.OTHERS}>{TagType.OTHERS}</option>
                  </Field>
                </div>

                <button
                  type="submit"
                  className="my-2 w-full rounded bg-primary py-2 text-white hover:bg-black dark:bg-dark_primary dark:hover:border sm:w-full md:w-32 lg:w-32 xl:w-32"
                >
                  {user?.firstName
                    ? `${t('manage-address:save_address')}`
                    : `${t('manage-address:add_address')}`}
                </button>
                <br />
              </Form>
            );
          }}
        </Formik>
      </div>
      <button type="button" onClick={() => cancelForm('')}>
        {t('common:cancel')}
      </button>
    </>
  );
};

export default AddressForm;
