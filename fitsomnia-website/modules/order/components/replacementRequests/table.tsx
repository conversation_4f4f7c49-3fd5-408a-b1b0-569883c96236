import TableData from '@/modules/order/components/orders/tableData';
import { OrderByUserId } from 'models';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

const Table: React.FC = () => {
  const { t } = useTranslation();

  return (
    <>
      <div className="mt-5">
        <div className="overflow-x-auto rounded-lg border">
          <div className="inline-block min-w-full py-2 sm:px-4">
            <table className="inline-table w-full text-left text-sm">
              <thead className="">
                <tr className="border-b text-center">
                  <th scope="col" className="px-5 py-4">
                    Order IDs
                  </th>
                  <th scope="col" className="px-5 py-4">
                    Product
                  </th>
                  <th scope="col" className="px-5 py-4">
                    Previous Size
                  </th>
                  <th scope="col" className="px-5 py-4">
                    Requested Size
                  </th>
                  <th scope="col" className="px-5 py-4">
                    Previous Color
                  </th>
                  <th scope="col" className="px-5 py-4">
                    Requested Color
                  </th>
                  <th scope="col" className="px-5 py-4">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody>
                {/* {orderList?.map((singleOrder, index) => {
                  return (
                    <React.Fragment key={singleOrder?.orderId}>
                      <tr
                        className={
                          index === orderList?.length - 1
                            ? 'border-none text-center'
                            : 'border-b text-center'
                        }
                      >
                        <TableData singleOrder={singleOrder} />
                      </tr>
                    </React.Fragment>
                  );
                })} */}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </>
  );
};

export default Table;
