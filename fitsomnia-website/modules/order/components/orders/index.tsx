import WithAuth from '@/modules/auth/withAuth';
import Breadcrumb from '@/modules/common/breadcrumbs/breadcrumb';
import { userAPI } from 'APIs';
import { OrderByUserId } from 'models';
import useTranslation from 'next-translate/useTranslation';
import { useEffect, useState } from 'react';
import { useAppSelector } from 'store/hooks/index';
import OrderTable from './orderTable';
import Loading from '@/modules/common/loader';

const Orders = () => {
  const [allOrderList, setAllOrderList] = useState<OrderByUserId[]>([]);
  const { t } = useTranslation();

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  const getAllOrders = async () => {
    const orderListRes = await userAPI.getOrderProducts(token);
    setAllOrderList(orderListRes?.orderInfo!);
  };

  useEffect(() => {
    getAllOrders();
  }, []);

  if (allOrderList.length === 0) return <Loading />;

  return (
    <>
      <Breadcrumb
        title={t('order:my_order')}
        pathArray={[`${t('common:home')}`, `${t('order:my_order')}`]}
        linkArray={['/', '/']}
      />
      <div className="container mx-auto px-4">
        <p className="mt-5 text-2xl font-semibold">Your Orders</p>
        {allOrderList?.length ? (
          <OrderTable orderList={allOrderList} getAllOrders={getAllOrders} />
        ) : (
          'You have not placed any order yet'
        )}
      </div>
    </>
  );
};

export default WithAuth(Orders);
