import withAuth from '@/modules/auth/withAuth';
import Breadcrumb from '@/modules/common/breadcrumbs/breadcrumb';
import ChevronLeft from '@/modules/common/icons/chevronLeft';
import ChevronRight from '@/modules/common/icons/chevronRight';
import { userAPI } from 'APIs';
import { OrderByUserId, PaymentStatusEnum } from 'models';
import useTranslation from 'next-translate/useTranslation';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { FC, useEffect, useState } from 'react';
import { useAppSelector } from 'store/hooks/index';
import OrderStatus from './orderStatus';
import ProductTable from './productTable/table';
import OrderSummary from './summary';

const SingleOrder: FC = () => {
  const router = useRouter();

  const [singleOrder, setSingleorder] = useState<OrderByUserId>();

  const id = router.query.id;

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );
  const { t } = useTranslation();
  const getSingleOrder = async () => {
    try {
      const singleOrderDetails = await userAPI.getOrderProduct(id as string);
      if (singleOrderDetails) setSingleorder(singleOrderDetails);
    } catch (error) {}
  };

  useEffect(() => {
    getSingleOrder();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <Breadcrumb
        title={t('order:order_details')}
        pathArray={[`${t('common:home')}`, `${t('order:order_details')}`]}
        linkArray={['/', '/']}
      />
      <div className="container mx-auto mt-5 px-4 dark:text-dark_text">
        <div className="flex gap-x-4">
          <button
            className="cursor-pointer fill-primary dark:fill-dark_primary"
            onClick={() => {
              router.push('/order');
            }}
          >
            <ChevronLeft height="h-9" width="h-9" />
          </button>
          <p className="text-2xl font-semibold">{t('order:order_summary')}</p>
        </div>
        <div className="mt-5 flex items-center gap-x-2">
          <Link prefetch={false} href="/order" passHref>
            <p className="cursor-pointer text-sm text-[#7c827f]">
              {t('common:order')}
            </p>
          </Link>
          <div className="fill-dark_text stroke-dark_text">
            <ChevronRight height="h-4" width="w-4" />
          </div>
          <p className="text-sm">{t('order:order_summary')}</p>
        </div>

        <OrderSummary singleOrder={singleOrder!} />
        <OrderStatus status={singleOrder?.orderStatus!} />
        <ProductTable
          productList={singleOrder?.products!}
          order={singleOrder!}
        />

        {/* {singleOrder?.paymentStatus === PaymentStatusEnum.Paid &&
          singleOrder?.shippingStatus === 'Delivered' &&
          singleOrder?.orderStatus === 'Completed' && (
            <div className="flex justify-center pt-6">
              <Link
                prefetch={false}
                href={{
                  pathname: '/return-request',
                  query: { orderId: id },
                }}
              >
                {' '}
                <button
                  className="rounded bg-primary py-2 px-8 font-bold text-white hover:bg-black dark:bg-dark_primary dark:hover:border dark:hover:bg-dark_bg"
                  id="re-order"
                >
                  Return Request
                </button>
              </Link>
            </div>
          )} */}
        {/* <ReOrder singleOrder={singleOrder!} /> */}
      </div>
    </>
  );
};

export default withAuth(SingleOrder);
