import CircleIcon from '@/modules/common/icons/circleIcon';
import { userAPI } from 'APIs';
import { trimDescription } from 'helper/trim';
import myImageLoader from 'image/loader';
import {
  IPlaceOrderProductInfo,
  OrderByUserId,
  PaymentStatusEnum,
  Review,
} from 'models';
import Image from 'next/legacy/image';
import Link from 'next/link';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { useAppSelector } from 'store/hooks';

interface Props {
  singleProduct: IPlaceOrderProductInfo;
  order: OrderByUserId;
}
const TableData: React.FC<Props> = ({ singleProduct, order }) => {
  const [productName, setProductName] = useState();
  const [reviewed, setReviewed] = useState(false);
  const userDetails = useAppSelector(
    (state) => state.persistedReducer.user.customerDetails
  );

  const getProductById = async () => {
    try {
      const res = await userAPI.getPublicProductsById(singleProduct.productId);
      if ('data' in res) setProductName(res.data.meta.friendlyPageName);
      else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
  };

  const getReviews = async () => {
    try {
      const res = await userAPI.getReview(singleProduct.productId);
      if ('data' in res) {
        const userReview = res.data.find(
          (review: Review) => review.userId === userDetails.id
        );
        if (userReview) {
          setReviewed(true);
        }
      } else {
        toast.error(res?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    getProductById();
    getReviews();
  }, []);
  return (
    <>
      <td className="px-5 py-4 text-center">
        <Image
          loader={myImageLoader}
          src={singleProduct?.photo!}
          alt={singleProduct?.name}
          width={60}
          height={60}
        />
      </td>
      <td className="px-5 py-4 text-center">
        {trimDescription(singleProduct?.name, 15)}
      </td>
      <td className="px-5 py-4 text-center">{singleProduct?.price}</td>
      <td className="px-5 py-4 text-center">{singleProduct?.size!}</td>
      <td className="px-5 py-4 text-center">
        <button className={`cursor-default rounded-full p-1 text-sm `}>
          <div className="flex flex-wrap items-center gap-2">
            <CircleIcon fill={singleProduct?.color!} size={20} />
          </div>
        </button>
      </td>
      <td className="px-5 py-4 text-center">{singleProduct?.quantity}</td>
      <td className="px-5 py-4 text-center">
        ${singleProduct?.price * singleProduct?.quantity}
      </td>
      {order?.orderStatus === 'Completed' &&
        order?.shippingStatus === 'delivered' &&
        order?.paymentStatus === PaymentStatusEnum.Paid && (
          <>
            <td className="px-5 py-4 text-center">
              {reviewed ? (
                <button className="disabled rounded bg-gray-400 p-2 text-black  dark:bg-dark_primary">
                  Already Reviewed
                </button>
              ) : (
                <Link
                  prefetch={false}
                  href={{
                    pathname: `/product/${productName}`,
                    query: { orderID: order?.orderId! },
                  }}
                >
                  <button className="rounded bg-primary p-2 text-white transition-all duration-500 ease-in-out hover:bg-black dark:bg-dark_primary">
                    Review Now
                  </button>
                </Link>
              )}
            </td>
            <td className="px-5 py-4 text-center">
              <button className="rounded bg-dark_text p-2 text-white transition-all duration-500 ease-in-out hover:bg-black dark:bg-dark_primary">
                Request
              </button>
            </td>
          </>
        )}
    </>
  );
};
export default TableData;
