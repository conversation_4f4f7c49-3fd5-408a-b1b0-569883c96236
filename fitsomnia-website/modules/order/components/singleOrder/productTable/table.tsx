import TableData from '@/modules/order/components/singleOrder/productTable/tableData';
import { IOrderProduct, OrderByUserId, PaymentStatusEnum } from 'models';
import useTranslation from 'next-translate/useTranslation';
import React from 'react';

interface Props {
  productList: IOrderProduct[];
  order: OrderByUserId;
}

const ProductTable: React.FC<Props> = ({ productList, order }) => {
  const { t } = useTranslation();

  return (
    <>
      <div className="mt-5">
        <div className="overflow-x-auto rounded-lg border">
          <div className="inline-block min-w-full py-2 sm:px-4">
            <table className="inline-table w-full text-left text-sm">
              <thead className="">
                <tr className="border-b">
                  <th scope="col" className="px-5 py-4 text-center">
                    {t('common:image')}
                  </th>
                  <th scope="col" className="px-5 py-4 text-center">
                    {t('common:product')}
                  </th>
                  <th scope="col" className="px-5 py-4 text-center">
                    Size
                  </th>
                  <th scope="col" className="px-5 py-4 text-center">
                    Color
                  </th>
                  <th scope="col" className="px-5 py-4 text-center">
                    {t('common:price')}
                  </th>
                  <th scope="col" className="px-5 py-4 text-center">
                    {t('common:quantity')}
                  </th>
                  <th scope="col" className="px-5 py-4 text-center">
                    {t('common:total')}
                  </th>
                  {order?.orderStatus === 'Completed' &&
                    order?.shippingStatus === 'delivered' &&
                    order?.paymentStatus === PaymentStatusEnum.Paid && (
                      <>
                        <th scope="col" className="px-5 py-4 text-center">
                          Review
                        </th>
                        <th scope="col" className="px-5 py-4 text-center">
                          Replace/Exchange Request
                        </th>
                      </>
                    )}
                </tr>
              </thead>
              <tbody>
                {productList?.map((singleProduct, index) => {
                  return (
                    <React.Fragment key={singleProduct?.productId}>
                      <tr
                        className={
                          index === productList?.length - 1
                            ? 'border-none'
                            : 'border-b'
                        }
                      >
                        <TableData
                          singleProduct={singleProduct}
                          order={order}
                        />
                      </tr>
                    </React.Fragment>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </>
  );
};

export default ProductTable;
