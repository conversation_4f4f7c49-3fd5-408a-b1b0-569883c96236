import { trimDescription } from 'helper/trim';
import myImageLoader from 'image/loader';
import { Blog } from 'models';
import moment from 'moment';
import Image from 'next/image';
import Link from 'next/link';
import { FC } from 'react';
interface Props {
  recentList: any;
  currentMonthsList: any;
}

const Left: FC<Props> = ({ recentList, currentMonthsList }) => {
  return (
    <>
      {' '}
      <div className="mt-5 md:mt-0">
        <p className="mb-5 font-semibold">RECENT POST</p>
        <hr />
        {recentList?.map((blog: any) => (
          <Link prefetch={false} href={`/blogs/${blog.slug}`} key={blog.slug}>
            <div className="mt-5 flex items-center gap-3">
              <div className="flex flex-col items-start">
                <p className="text-sm">
                  {trimDescription(blog.title.rendered, 20)}
                </p>
                <p className="text-sm">
                  {moment(blog.date).utc().local().format('ll')}
                </p>
              </div>
            </div>
          </Link>
        ))}
        <p className="mt-10 mb-5 font-semibold">ARCHIVE</p>
        <hr />
        <p className="mt-2 text-sm text-gray-600">
          {moment(new Date()).utc().local().format('MMMM, YYYY')}
        </p>

        {currentMonthsList?.map((blog: any) => {
          return (
            <Link prefetch={false} href={`/blogs/${blog.slug}`} key={blog.slug}>
              <ul className="my-3 ml-5 list-disc">
                <li className="text-sm hover:text-primary">
                  {trimDescription(blog.title.rendered, 30)}
                </li>
              </ul>
            </Link>
          );
        })}
      </div>
    </>
  );
};

export default Left;
