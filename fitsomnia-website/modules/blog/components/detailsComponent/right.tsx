import myImageLoader from 'image/loader';
import { Blog } from 'models';
import moment from 'moment';
import Image from 'next/image';
import Head from 'next/head';
// import './styles/wp.module.css';

import { FC, useEffect } from 'react';
import { trimDescription } from 'helper/trim';

interface Props {
  blog: any;
}

const Right: FC<Props> = ({ blog }) => {
  // useEffect(() => {
  //   (document.getElementById('blogContent') as HTMLElement).innerHTML =
  //     blog.content.rendered;
  // }, []);

  return (
    <>
      <div className="flex flex-col items-center justify-center">
        <p className="text-3xl font-medium">{blog.title.rendered}</p>
        {/* <Image
          src={blog['_embedded']['wp:featuredmedia'][0]['source_url']}
          alt=""
          loader={myImageLoader}
          height={500}
          width={500}
        /> */}
        <div>
          <div className="mt-3 flex flex-wrap gap-5 text-sm font-thin">
            <p>Posted by: {blog._embedded.author![0]?.name}</p>
            <p>|</p>
            <p>{moment(blog.date).utc().local().format('ll')}</p>
          </div>

          <div
            className="mt-2"
            id="blogContent"
            dangerouslySetInnerHTML={{
              __html: trimDescription(blog['content']['rendered'], 1000),
            }}
          ></div>
          <p
            className="cursor-pointer text-primary underline"
            onClick={() => window.open(blog.link)}
          >
            Read More...
          </p>
        </div>
      </div>
    </>
  );
};

export default Right;
