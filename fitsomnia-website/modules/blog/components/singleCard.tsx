import RightArrow from '@/modules/common/icons/rightArrow';
import { trimDescription } from 'helper/trim';
import myImageLoader from 'image/loader';
import { Blog } from 'models';
import moment from 'moment';
import Image from 'next/image';
import Link from 'next/link';
import { FC } from 'react';

interface Props {
  blog: any;
}

const SingleCard: FC<Props> = ({ blog }) => {
  return (
    <>
      <div className="w-full border-none bg-white">
        {/* <Image
          className="w-full rounded-t-lg"
          src={blog['_embedded']['wp:featuredmedia'][0]['source_url']}
          alt=""
          loader={myImageLoader}
          height={100}
          width={100}
        /> */}
        <div className="border-none p-5">
          <div className="flex flex-wrap gap-3 text-sm text-gray-700">
            <p>{moment(blog.date).utc().local().format('ll')}</p>
            <p>|</p>
            <p>{blog._embedded!.author[0].name!}</p>
          </div>
          <Link prefetch={false} href={`/blogs/${blog.slug}`}>
            <h5 className="mb-2 text-2xl font-bold tracking-tight text-gray-900 hover:text-primary">
              {blog.title.rendered}
            </h5>
          </Link>
          {/* <p className="mb-3 font-normal text-gray-700 dark:text-gray-400">
            Here are the biggest enterprise technology acquisitions of 2021 so
            far, in reverse chronological order.
          </p> */}
          <Link
            prefetch={false}
            href={`/blogs/${blog.slug}`}
            className="inline-flex items-center rounded-lg bg-primary px-3 py-2 text-center text-sm font-medium text-white hover:bg-black focus:outline-none"
          >
            Read more
            <RightArrow />
          </Link>
        </div>
      </div>
    </>
  );
};

export default SingleCard;
