import Breadcrumb from '@/modules/common/breadcrumbs/breadcrumb';
import Left from './detailsComponent/left';
import Right from './detailsComponent/right';
import { Blog } from 'models';
import { FC } from 'react';
import { trimDescription } from 'helper/trim';
import Head from 'next/head';

interface Props {
  blog: any;
  recentList: any;
  currentMonthsList: any;
}

const BlogDetails: FC<Props> = ({ blog, recentList, currentMonthsList }) => {
  return (
    <>
      {/* <Breadcrumb
        title="Blogs"
        pathArray={[
          `Home`,
          `Blogs`,
          `${trimDescription(blog.title.rendered, 20)}`,
        ]}
        linkArray={['/market', '/blogs', '/title']}
      /> */}
      <div className="container mx-auto mt-6 px-4 py-5">
        <div className="grid grid-cols-1 md:grid-cols-12">
          <div className="order-3 col-span-3 md:order-1">
            <Left
              recentList={recentList}
              currentMonthsList={currentMonthsList!}
            />
          </div>
          <div className="order-2 col-span-1 hidden md:block"></div>
          <div className="order-1 col-span-8 md:order-3">
            <Right blog={blog} />
          </div>
        </div>
      </div>
    </>
  );
};

export default BlogDetails;
