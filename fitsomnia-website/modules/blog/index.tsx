import { NextComponentType } from 'next';
import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';
import Breadcrumb from '../common/breadcrumbs/breadcrumb';
import SingleCard from './components/singleCard';
import Pagination from '../common/pagination2';
import { Blog } from 'models';
import { userAPI } from 'APIs';
import { toast } from 'react-toastify';
import { useAppSelector } from 'store/hooks';
import ChevronLeft from '../common/icons/chevronLeft';
import ChevronRight from '../common/icons/chevronRight';

const BlogComponent: NextComponentType = () => {
  const [limit, setLimit] = useState(8);
  const [currentPage, setCurrentPage] = useState(1);
  const router = useRouter();
  const prev = router.query.prev! as string;
  const [blogs, setBlogs] = useState<Blog[]>([]);

  // const currentTableData = useMemo(() => {
  //   const firstPageIndex = (currentPage - 1) * limit;
  //   const lastPageIndex = firstPageIndex + limit;
  //   return x.slice(firstPageIndex, lastPageIndex);
  // }, [currentPage, limit, x]);

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  const getBlogs = async () => {
    try {
      // const skip = (currentPage - 1) * limit;
      // const res = await userAPI.getBlog(skip, limit, token, undefined);
      // if ('data' in res) {
      //   setBlogs(res.data);
      // } else {
      //   toast.error(res?.error.message);
      // }
      const page = currentPage;
      const res = await userAPI.getWpBlogs(page, limit);
      if ('data' in res) {
        setBlogs(res.data);
      }
    } catch (error) {}
  };

  useEffect(() => {
    getBlogs();
  }, [currentPage]);

  return (
    <>
      {/* <Breadcrumb
        title="Blogs"
        pathArray={[`Home`, `Blogs`]}
        linkArray={[prev ? '/' : '/market', '/market']}
      /> */}
      <div className="container mx-auto mt-6 px-4 py-5">
        <div className="flex flex-wrap items-center justify-center gap-10 md:mx-40">
          {blogs.length > 0 ? (
            <>
              {blogs.map((blog) => (
                <>
                  <SingleCard blog={blog} />{' '}
                </>
              ))}
            </>
          ) : (
            <div className="">No Blog Found</div>
          )}
        </div>
        <div className="flex justify-between md:mx-44">
          <button
            className="flex items-center rounded-lg p-2 hover:text-primary"
            onClick={() => setCurrentPage(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <ChevronLeft height="h-6" width="w-6" />
            Previous
          </button>
          <button
            className="flex items-center hover:text-primary"
            onClick={() => setCurrentPage(currentPage + 1)}
            disabled={blogs.length === 0}
          >
            Next <ChevronRight height="h-6" width="w-6" />
          </button>
        </div>
        {/* <div className="flex justify-center border p-2 md:mx-44">
            <Pagination
              cur={currentPage}
              setCur={setCurrentPage}
              total={40 / limit}
            />
          </div> */}
      </div>
    </>
  );
};

export default BlogComponent;
