module.exports = {
  prompt: async ({ inquirer }) => {
    const initialQuestions = [
      {
        type: "input",
        name: "folder",
        message: "Where to create?",
      },
      {
        type: "input",
        name: "component_name",
        message: "What is the name of the Component?",
      },
      {
        type: "input",
        name: "numberOfFields",
        message: "Type number of fields",
      },
      {
        type: "select",
        name: "formType",
        message: "Type Form Type",
        choices: ["Registration Form", "Login Form", "Forgot Password Form"],
      }
    ];
    const questions = [
      {
        type: "input",
        name: "name",
        message: "Type field name",
      },
      {
        type: "input",
        name: "type",
        message: "Type field input type",
      },
      {
        type: "input",
        name: "placeholder",
        message: "Type value for placeholder",
      },
    ];

    const buttonQuestion = [
      {
        type: "input",
        name: "submitButtonLabel",
        message: "Type submit button label",
      },
    ]

    const userInput = await inquirer.prompt(initialQuestions);
    const { category, folder, component_name, numberOfFields, formType } = userInput;
    const path = `${folder}/${component_name}`;
    const absPath = `modules/${path}`;

    let inputArray: any = [];
    for(let i=0;i<numberOfFields;i++) {
      const {name, label, type, placeholder} = await inquirer.prompt(questions);
      inputArray.push({name, label, type, placeholder});
    }
    const {submitButtonLabel} = await inquirer.prompt(buttonQuestion);

    return { path, absPath, category, component_name, inputArray, numberOfFields, formType, submitButtonLabel };
  },
};
