---
to: <%= absPath %>/<%= component_name %>.tsx
---
import React from 'react';
import { Formik, Field, Form } from "formik";
import Link from 'next/link';
import useTranslation from 'next-translate/useTranslation';

export const <%= component_name %>: React.FC = (props) => {
  const { t } = useTranslation();

  return <>
    <div data-testid="hygen">
      <Formik
        initialValues={{
           <% for(let i=0; i<numberOfFields; i++ ) {%>
                <%= inputArray[i].name %>: '',
            <% } %>
        }}
        onSubmit={(values, actions) => {
          console.log(values);
          actions.setSubmitting(false);
        }}
      >
      {(formikprops) => {
          return (
            <Form
              onSubmit={formikprops.handleSubmit}
            >
              <% for(let i=0; i<numberOfFields; i++ ) {%>
                    <>
                      <div className="mb-4">
                        <Field
                          type="<%= inputArray[i].type %>"
                          className="w-full p-2 placeholder-gray-600 outline-0"
                          id="<%= inputArray[i].name %>"
                          name="<%= inputArray[i].name %>"
                          placeholder={t("<%= inputArray[i].placeholder %>")}/>
                          <div className="errMsg text-red-600">
                          <ErrorMessage name="<%= inputArray[i].name %>" />
                    </div>
                      </div>
                    </>
              <% } %>
              <div className="flex flex-wrap justify-end sm:justify-end md:justify-between lg:justify-between xl:justify-between">
                <button
                  type="submit"
                  className={`my-2 w-full rounded bg-primary py-2 capitalize text-white hover:bg-black dark:bg-dark_primary <% if (formType !== 'Registration Form') { %> sm:w-full md:w-1/4 lg:w-1/4 xl:w-1/4 <% } %>` }
                >
                {t('<%= submitButtonLabel %>')}
                </button>
                <% if (formType === 'Login Form') { %>
                <div
                  id="forgotPasswordDiv"
                  className="text-decoration-none my-0 sm:my-0 md:my-3 lg:my-3 xl:my-3 font-weight-light text-gray-600 hover:text-gray-500"
                >
                  <Link prefetch={false} href="/account/forgot-password">
                      {t('login:forgot_password')}
                  </Link>
                </div>
                <% } %>
                <% if (formType === 'Forgot Password Form') { %>
                  <div
                    id="cancelDiv"
                    className="text-decoration-none my-0 sm:my-0 md:my-3 lg:my-3 xl:my-3 font-weight-light text-gray-600 hover:text-gray-500"
                  >
                    <Link prefetch={false} prefetch={false} href="/account/sign-in">
                        {t('common:cancel')}
                    </Link>
                  </div>
                <% } %>
              </div>
              <% if (formType === 'Forgot Password Form') { %>
                <div className="text-gray-500">
                  {t('forgot-password:otp_not_received')}{' '}
                  <button
                    onClick={}
                    className="hover:text-primary"
                  >
                    {t('forgot-password:try_again')}
                  </button>
                </div>
              <% } %>
            </Form>
          );
        }}
      </Formik>
    </div>
  </>
};