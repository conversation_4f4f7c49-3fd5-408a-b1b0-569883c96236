import { SuccessResponse } from '../common/index';
import { Manufacturer } from './manufacturer';

/**
 * API Path: /manufacturers/{manufacturerId}
 * method: DELETE
 * param: manufacturerId: string
 * body: null
 * response: DeleteManufacturerResponse
 */

export const enum DeleteManufacturerErrorMessages {
  MANUFACTURER_NOT_FOUND = 'Manufacturer not found',
  MANUFACTURER_NOT_DELETED = 'Manufacturer not deleted',
}

export const enum DeleteManufacturerSuccessMessages {
  MANUFACTURER_DELETED_SUCCESSFULLY = 'Manufacturer deleted successfully',
}

export interface DeleteManufacturerSuccessResponse extends SuccessResponse {
  data: {
    manufacturer: Manufacturer;
    message: DeleteManufacturerSuccessMessages;
  };
}
