import { SuccessResponse } from '../common/index';
import { User } from './user';

/**
 * API Path: /user
 * method: PATCH
 * body: UpdateUserRequestBody
 * response: UpdateUserResponse
 */

export interface UpdateUserRequestBody {
  name?: string;
  phone?: string;
  fcmToken?: string;
  title?: string;
  organization?: string;
  workoutType?: string;
  bodyType?: string;
  dateOfBirth?: Date;
  gender?: string;
  maxBench?: number;
  maxSquat?: number;
  image?: {
    profile: string;
  };
}

export interface UpdateUserSuccessResponse extends SuccessResponse {
  data: User;
}

export const enum UpdateUserErrorMessages {
  CAN_NOT_UPDATE_USER_INFORMATION = 'Can not update user information',
  PHONE_ALREADY_EXISTS = 'Phone number already exists',
  TIME_LIMIT_EXCEED_OR_UNVERIFIED_USER = 'Time limit exceed or unverified user',
}
