import { SuccessResponse } from '../common/index';
import { User } from './user';

/**
 * API Path: /user/add-address
 * method: PUT
 * body: UserAddress
 * response: AddUserNewAddressResponse
 */

export interface AddUserNewAddressSuccessResponse extends SuccessResponse {
  data: User;
}

export const enum AddUserNewAddressErrorMessages {
  CAN_NOT_ADD_USER_NEW_ADDRESS = 'Can not add user new address',
}


export interface IEasypostAddress {
  id: string
  object: string
  created_at: string
  updated_at: string
  name: string
  company: any
  street1: string
  street2: string
  city: string
  state: string
  zip: string
  country: string
  phone: string
  email: any
  mode: string
  carrier_facility: any
  residential: boolean
  federal_tax_id: any
  state_tax_id: any
  verifications: IAddressVerifications
}

export interface IAddressVerifications {
  zip4: Zip4
  delivery: Delivery
}

export interface Zip4 {
  success: boolean
  errors: any[]
  details: any
}

export interface Delivery {
  success: boolean
  errors: any[]
  details: Details
}

export interface Details {
  latitude: number
  longitude: number
  time_zone: string
}

export interface AddressValidationSuccessResponse {
  data: IEasypostAddress;
}

export interface AddressData {
  firstName: string;
  lastName: string;
  email: string;
  addressLine1: string;
  addressLine2:string;
  city: string;
  state?: string;
  country?: string;
  postCode: string;
  phone: string;
}