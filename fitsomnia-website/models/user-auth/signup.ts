import { SuccessResponse } from '../common/index';

/**
 * API Path: /user-auth/signup
 * method: POST
 * body: CreateUserRequest
 * response: CreateUserResponse
 */

export interface CreateUserRequest {
  email: string;
  otp: number;
}

export interface CreateUserSuccessResponse extends SuccessResponse {
  data: {
    token?: string;
  };
}

export const enum CreateUserErrorMessages {
  USER_ALREADY_EXITS = 'User already exists',
  TIME_LIMIT_EXCEED_OR_UNVERIFIED_USER = 'Time limit exceed or unverified user',
  CAN_NOT_CREATE_USER = 'Can not create user',
  OTP_EXPIRED_OR_INVALID_OTP = 'Otp expired or invalid otp',
}
