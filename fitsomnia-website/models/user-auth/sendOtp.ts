import { SuccessResponse } from '../common/index';

/**
 * API Path: /user-auth/forgot-password/send-otp
 * method: POST
 * body: SendOtpRequest
 * response: SendOtpResponse
 */

export interface SendOtpRequest {
  email: string;
}

export declare const enum SendOtpSuccessMessages {
  OTP_SEND_SUCCESSFUL = 'Otp sent successfully',
}

export interface SendOtpSuccessResponse extends SuccessResponse {
  data: {
    message?: SendOtpSuccessMessages;
  };
}

export declare const enum SendOtpErrorMessages {
  USER_ALREADY_EXITS = 'User already exists',
  CAN_NOT_UPDATE_OTP = 'Can not update otp',
  CAN_NOT_SEND_OTP = 'Can not send otp',
}
