import { SuccessResponse } from '../common/index';

/**
 * API Path: /wishlist/:wishlistId
 * method: DELETE
 * params: wishlistId
 * response: DeleteAllWishlistItemsResponse
 */

export interface DeleteAllWishlistItemsSuccessResponse extends SuccessResponse {
  data: {
    message?: string;
  };
}

export const enum DeleteAllWishlistItemsSuccessMessages {
  WISHLIST_ITEMS_DELETED_SUCCESSFUL = 'Wishlist items deletion successful',
}

export const enum DeleteAllWishlistItemsErrorMessage {
  CAN_NOT_DELETE_ALL_WISHLIST_ITEMS = 'Can not delete all wishlist items',
}
