import { SuccessResponse } from '../common/index';
import { Wishlist } from './wishlist';

/**
 * API Path: /wishlist/items/:productId
 * method: DELETE
 * params: productId
 * response: DeleteWishlistItemResponse
 */

export interface DeleteWishlistItemParams {
  productId: string;
}

export interface DeleteWishlistItemSuccessResponse extends SuccessResponse {
  data: Wishlist;
}

export const enum DeleteWishlistItemErrorMessage {
  CAN_NOT_DELETE_WISHLIST_ITEM = 'Can not delete wishlist item',
}
