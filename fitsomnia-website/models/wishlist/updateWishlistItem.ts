import { SuccessResponse } from '../common/index';
import { Wishlist } from './wishlist';

/**
 * API Path: /wishlist/item
 * method: PATCH
 * body: UpdateWishlistItemRequestBody
 * response: UpdateWishlistItemResponse
 */

export interface UpdateWishlistItemRequestBody {
  productId: string;
  quantity: number;
}

export interface UpdateWishlistItemSuccessResponse extends SuccessResponse {
  data: Wishlist;
}

export const enum UpdateWishlistItemErrorMessage {
  CAN_NOT_DELETE_WISHLIST_ITEM = 'Can not delete wishlist item',
  CAN_NOT_UPDATE_WISHLIST_ITEM = 'Can not update wishlist item',
}
