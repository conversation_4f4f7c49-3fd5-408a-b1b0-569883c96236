import { SuccessResponse } from '../common/index';

/**
 * API Path: /wishlist/:wishlistId
 * method: DELETE
 * params: wishlistId
 * response: DeleteWishlistResponse
 */

export interface DeleteWishlistParams {
  wishlistId: string;
}

export interface DeleteWishlistSuccessResponse extends SuccessResponse {
  data: {
    message?: string;
  };
}

export const enum DeleteWishlistSuccessMessage {
  WISHLIST_DELETED_SUCCESSFUL = 'Wishlist deleted  successfully',
}

export const enum DeleteWishlistErrorMessage {
  CAN_NOT_DELETE_WISHLIST = 'Can not delete wishlist',
}
