import { SuccessResponse } from '../common/successResponse';

export interface OrderCancelRequest {
  orderId: string;
}

export const enum OrderMessage {
  CANCELLED_ORDER_SUCCESSFULLY = 'Cancelled Order successfully',
}

export interface OrderCancelMessage {
  message: OrderMessage;
}

export interface CancelOrderSuccessResponse extends SuccessResponse {
  data: OrderCancelMessage;
}

export const enum OrderCancelErrorMessage {
  CAN_NOT_CANCEL_ORDER_AFTER_PAYMENT = 'Can not cancel order after payment',
  CAN_NOT_CANCEL_ORDER_AFTER_7days = 'Can not cancel order after 7 days',
  CAN_NOT_CANCEL_ORDER = 'Can not cancel order',
}
