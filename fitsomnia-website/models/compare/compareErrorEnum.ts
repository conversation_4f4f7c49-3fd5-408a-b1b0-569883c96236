export const enum AddProductToCompareErrorEnum {
  CAN_NOT_ADD_ITEM_FOR_COMPARING = 'Can not add item for comparing.',
}

export const enum GetCompareErrorEnum {
  COMPARISON_LIST_IS_EMPTY = 'Comparison list is empty.',
  COMPARISON_NOT_FOUND = 'Comparison not found.',
}

export const enum DeleteCompareErrorEnum {
  COMPARISON_CAN_NOT_BE_DELETED_OR_NOT_EXIST = 'Comparison can not be deleted or not exist.',
  ITEM_CAN_NOT_BE_DELETED = 'Item can not be deleted.',
  INVALID_ID = 'Invalid Product id',
}
