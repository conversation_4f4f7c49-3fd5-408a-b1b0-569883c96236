export enum BlogCategory {
  GENERAL = 'general',
  LIFESTYLE = 'lifestyle',
  TECHNOLOGY = 'technology',
  SPORTS = 'sports',
  ENTERTAINMENT = 'entertainment',
}

export interface Author {
  id: string;
  name: string;
  image: {
    profile: string;
  };
}
export interface Blog {
  id: string;
  authorId: string;
  title: string;
  content: string;
  category: BlogCategory;
  authorInfo?: Author[];
  createdAt: string;
}
