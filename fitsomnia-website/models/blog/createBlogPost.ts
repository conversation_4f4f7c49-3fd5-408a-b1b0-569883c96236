import { SuccessResponse } from '../common/successResponse';
import { Blog, BlogCategory } from './blog';

export interface CreateBlogPostRequestBody {
  authorId?: string;
  title: string;
  content: string;
  category: BlogCategory;
}

export interface CreateBlogPostResponse extends SuccessResponse {
  data: Blog;
}

export const enum CreateBlogPostErrorMessage {
  CAN_NOT_CREATE_BLOG_POST = 'Can not create blog post',
}
