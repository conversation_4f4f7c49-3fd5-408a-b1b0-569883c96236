import { SuccessResponse } from '../common/index';

/**
 * API Path: /media/upload
 * method: POST
 * body: FileUploadRequestBody
 * response: UploadFileResponse
 */

export class IUploadSingleFileReq {
  file: any;
}

export interface FileUploadRequestBody {
  filenames: string[];
  featureName: string;
}

export interface FileUploadResponse {
  filename: string;
  featureName: string;
  presignedUrl: string;
  s3UploadedURLKey: string;
}

export interface UploadFileSuccessResponse extends SuccessResponse {
  data: FileUploadResponse[];
}

export interface PublicUploadFileSuccessResponse extends SuccessResponse {
  data: string;
}

export const enum IUploadFileErrorEnum {
  NO_FILE_IS_FOUND = 'No file is found',
  INVALID_FILE_TYPE = 'Invalid file type',
  ERROR_IN_UPLOADING_FILE = 'Error in uploading file',
}
