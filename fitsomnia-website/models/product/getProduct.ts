import { SuccessResponse } from '../common/index';
import { Product } from './product';

/**
 * API Path: /admin/products/:productId
 * method: GET
 * params: productId
 * response: GetProductResponse
 */

export interface GetProductParams {
  productId: string;
}

export interface GetProductSuccessResponse extends SuccessResponse {
  data: Product;
}

export const enum GetProductErrorMessages {
  CAN_NOT_GET_PRODUCT = 'Can not get product',
}
