import { SuccessResponse } from '../common/index';
import { UserProduct } from './userProduct';

/**
 * API Path: /user/products/:productId
 * method: GET
 * query: GetUserProductQuery
 * response: GetUserProductResponse
 */

export interface GetUserProductParams {
  productId: string;
}

export interface GetUserProductSuccessResponse extends SuccessResponse {
  data: UserProduct;
}

export const enum GetUserProductErrorMessages {
  CAN_NOT_GET_PRODUCT = 'Can not get product',
}
