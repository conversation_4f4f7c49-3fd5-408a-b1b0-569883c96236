import { SuccessResponse } from '../common/index';
import {
  Product,
  ProductCategory,
  ProductInfo,
  ProductManufacturer,
  ProductMeta,
  ProductPhoto,
} from './product';

/**
 * API Path: /admin/products/create-product
 * method: POST
 * body: CreateProductRequest
 * response: CreateProductResponse
 */

export interface CreateProductRequest {
  info: ProductInfo;
  meta?: ProductMeta;
  tags?: string[];
  photos?: ProductPhoto[];
  brands?: string[];
  manufacturer?: ProductManufacturer;
  categories: ProductCategory[];
}

export interface CreateProductSuccessResponse extends SuccessResponse {
  data: Product;
}

export const enum CreateProductErrorMessages {
  PRODUCT_SKU_MATCH = 'Product sku match',
  PRODUCT_FRIENDLY_PAGE_NAME_MATCH = 'Product friendly page name match',
  CAN_NOT_CREATE_NEW_PRODUCT = 'Can not create new product',
}
