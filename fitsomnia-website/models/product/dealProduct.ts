import { SuccessResponse } from '../common/index';

export interface DealProductRequest {
  productId: string;
}

interface DealProductResponseMessage {
  message: string;
}

export interface DealProductRequestResponse extends SuccessResponse {
  data: DealProductResponseMessage;
}

export const enum DealProductMessages {
  DEAL_PRODUCT_ADDED_SUCCESSFUL = 'Deal product added successfully',
  CANNOT_ADD_DEAL_PRODUCT = 'Can not add deal product',
  INVALID_PRODUCT_ID = 'Invalid product id',
  DEAL_PRODUCT_DELETED_SUCCESSFUL = 'Deal product deleted',
  CANNOT_DELETE_DEAL_PRODUCT = 'Can not delete deal product',
  DEAL_PRODUCTS_NOT_FOUND = 'Deal products not found',
}
