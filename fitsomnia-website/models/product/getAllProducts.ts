import { SuccessResponse } from '../common/index';
import { Product } from './product';

/**
 * API Path: /admin/products
 * method: GET
 * query: GetAllProductsQuery
 * response: GetAllProductsResponse
 */

export interface GetAllProductsQuery {
  offset?: number;
  limit?: number;
}

export interface GetAllProductsSuccessResponse extends SuccessResponse {
  data: Product[];
}

export const enum GetAllProductsErrorMessages {
  CAN_NOT_GET_ALL_PRODUCTS = 'Can not get all products',
}
