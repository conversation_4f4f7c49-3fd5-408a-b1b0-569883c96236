import { SuccessResponse } from '../common/index';
import { Product } from './product';

/**
 * API Path: /admin/products/sku/:sku
 * method: GET
 * params: sku
 * response: GetProductBySKUResponse
 */

export interface GetProductBySKUParams {
  sku: string;
}

export interface GetProductBySKUSuccessResponse extends SuccessResponse {
  data: Product;
}

export const enum GetProductBySKUErrorMessages {
  CAN_NOT_GET_PRODUCT = 'Can not get product',
}
