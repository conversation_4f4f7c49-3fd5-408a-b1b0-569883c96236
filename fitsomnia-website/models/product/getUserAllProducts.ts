import { SuccessResponse } from '../common/index';
import { UserProduct } from './userProduct';

/**
 * API Path: /user/products
 * method: GET
 * query: GetUserAllProductsQuery
 * response: GetUserAllProductsResponse
 */

export interface GetUserAllProductsQuery {
  offset?: number;
  limit?: number;
  brand?: string;
  manufacturer?: string;
  categoryId?: string;
  productName?: string;
  isFeatured?: boolean;
  slug?: string;
  orderBy?: string;
  minPrice?: number;
  maxPrice?: number;
}

export interface GetUserAllProductsResponseType {
  products: UserProduct[];
  manufacturers: string[];
  brands: string[];
  totalProducts: number;
}

export interface GetUserAllProductsSuccessResponse extends SuccessResponse {
  data: GetUserAllProductsResponseType;
}

export const enum GetUserAllProductsErrorMessages {
  CAN_NOT_GET_ALL_PRODUCTS = 'Can not get all products',
}
