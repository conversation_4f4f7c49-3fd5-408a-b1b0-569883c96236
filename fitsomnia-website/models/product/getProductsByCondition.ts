import { SuccessResponse } from '../common/index';
import { ProductInfo, ProductPhoto } from './product';

/**
 * API Path: /admin/products/condition
 * method: GET
 * query: GetProductsByConditionQuery
 * response: GetProductsByConditionResponse
 */

export interface GetProductsByConditionQuery {
  offset?: number;
  limit?: number;
  brand?: string;
  categoryId?: string;
  productName?: string;
  isFeatured?: boolean;
  slug?: string;
  orderBy?: string;
}

export interface ConditionalProduct {
  info: ProductInfo;
  photos?: ProductPhoto[];
  brands?: string[];
}

export interface GetProductsObject {
  products: ConditionalProduct[];
  count: number;
}

export interface GetProductsByConditionSuccessResponse extends SuccessResponse {
  data: GetProductsObject;
}

export const enum GetProductsByConditionErrorMessages {
  CAN_NOT_GET_PRODUCTS = 'Can not get products',
}
