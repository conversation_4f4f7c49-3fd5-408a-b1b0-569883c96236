import { SuccessResponse } from '../common/successResponse';
import { Category, CategoryMeta, Photo } from './category';

export interface CreateCategoryRequest {
  name: string;
  parentSlug?: string;
  photo?: Photo;
  description?: string;
  showOnHomePage?: boolean;
  includeInTopMenu?: boolean;
  allowToSelectPageSize?: boolean;
  published?: boolean;
  displayOrder?: number;
  meta?: CategoryMeta;
}
export interface CreateCategorySuccessResponse extends SuccessResponse {
  data: Category;
}

export const enum CreateCategoryErrorMessage {
  CAN_NOT_CREATE_CATEGORY = 'Can not create category',
  PARENT_SLUG_NOT_FOUND = 'Parent slug not found',
}
