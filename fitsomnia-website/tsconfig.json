{"compilerOptions": {"strictPropertyInitialization": false, "target": "es5", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@/modules/*": ["modules/*"], "@/styles/*": ["styles/*"], "@/hooks/*": ["hooks/*"]}, "types": ["cypress", "node", "jest"]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "**/*.test.tsx"], "exclude": ["node_modules"]}