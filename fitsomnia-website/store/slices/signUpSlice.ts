import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface User {
  email?: string;
  phone?: string;
  name: string;
  password: string;
}

export interface SignUpState {
  user: User;
  otp?: number;
}

const initialState: SignUpState = {
  user: { email: '', phone: '', name: '', password: '' },
  otp: 0,
};

export const signUpSlice = createSlice({
  name: 'signUp',
  initialState,
  reducers: {
    storeUserDetails: (
      state: SignUpState,
      action: PayloadAction<User>
    ) => {
      state.user = action.payload;
    },
    storeOtp: (state: SignUpState, action: PayloadAction<number>) => {
      state.otp = action.payload;
    },
  },
});

export const { storeUserDetails, storeOtp } = signUpSlice.actions;

export default signUpSlice.reducer;
