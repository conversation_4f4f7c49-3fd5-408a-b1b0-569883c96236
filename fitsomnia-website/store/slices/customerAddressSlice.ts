import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { UserAddress } from 'models';

export interface UserAddressState {
  addresses: UserAddress[];
}

const initialState: UserAddressState = {
  addresses: [],
};

export const UserAddressSlice = createSlice({
  name: 'UserAddress',
  initialState,
  reducers: {
    storeAddresses: (
      state: UserAddressState,
      action: PayloadAction<UserAddress[]>
    ) => {
      state.addresses = action.payload;
    },
    deleteAddress: (
      state: UserAddressState,
      action: PayloadAction<string>
    ) => {
      const newAddressList = state.addresses.filter(
        (address) => address.id !== action.payload
      );
      state.addresses = newAddressList;
    },
    resetAddress: (state: UserAddressState) => {
      state.addresses = initialState.addresses;
    },
  },
});

export const { storeAddresses, deleteAddress, resetAddress } =
  UserAddressSlice.actions;

export default UserAddressSlice.reducer;
