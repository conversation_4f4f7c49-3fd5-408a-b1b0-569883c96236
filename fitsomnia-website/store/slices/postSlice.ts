import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface NewsFeedItem {
  id: string;
  content: string;
  createdAt: string;
  images: {
    small: string;
    medium: string;
    thumbnail: string;
    url: string;
  }[];
  comments: {
    comment: string;
    createdAt: string;
    userId: string;
    userInfo: {
      id: string;
      name: string;
      image: {
        profile: string | null;
      };
    };
  }[];
  liked: boolean;
  likersInfo: {
    image: {
      profile: string | null;
    };
    name: string;
    userId: string;
  }[];
  location: string | null;
  privacy: string;
  reactionType: string | null;
  reactionTypeUserInfo: {
    type: string;
    users: {
      userId: string;
      name: string;
      image: string;
    }[];
  }[];
  reactions: {
    LIKE: number;
  };
  totalComments: number;
  totalLikes: number;
  totalReactions: number;
  totalShares: number;
  type: string;
  updatedAt: string;
  userId: string;
  userInfo: {
    image: {
      profile: string;
    };
    name: string;
  };
  videos: any[]; // Assuming this could be an array of video objects, adjust based on actual data
  weight: number;
}

export interface AllNewsFeedItemsState {
  allNewsFeedItems: NewsFeedItem[];
}

const initialState: AllNewsFeedItemsState = {
  allNewsFeedItems: [],
};

export const postSlice = createSlice({
  name: 'post',
  initialState,
  reducers: {
    storeAllPostItems: (
      state: AllNewsFeedItemsState,
      action: PayloadAction<NewsFeedItem[]>
    ) => {
      state.allNewsFeedItems = action.payload;
      // state.allNewsFeedItems = [...state.allNewsFeedItems, ...action.payload];
    },
  },
});


export const { storeAllPostItems } = postSlice.actions;

export default postSlice.reducer;