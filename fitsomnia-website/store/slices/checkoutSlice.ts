import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { IOrderAddress, TagType } from 'models';

export interface ShippingInfo {
  firstName: string;
  lastName: string;
  email: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  postCode: string;
  country: string;
  state: string;
  phone: string;
  tag: TagType;
}

export interface CheckoutState {
  shippingInfo: ShippingInfo | null;
  billingInfo: IOrderAddress | null;
}

const initialState: CheckoutState = {
  shippingInfo: null,
  billingInfo: null,
};

export const checkoutSlice = createSlice({
  name: 'checkout',
  initialState,
  reducers: {
    addToShippingInfo: (
      state: CheckoutState,
      action: PayloadAction<ShippingInfo>
    ) => {
      state.shippingInfo = action.payload;
    },
    addToBillingInfo: (
      state: CheckoutState,
      action: PayloadAction<IOrderAddress>
    ) => {
      state.billingInfo = action.payload;
    },
    deleteCheckoutInfo: (state: CheckoutState) => {
      state.shippingInfo = null;
      state.billingInfo = null;
    },
  },
});

export const { addToShippingInfo, addToBillingInfo, deleteCheckoutInfo } =
  checkoutSlice.actions;

export default checkoutSlice.reducer;
