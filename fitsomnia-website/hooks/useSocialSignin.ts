import { useState } from 'react';
import { useRouter } from 'next/router';
import { signIn, getSession } from 'next-auth/react';
import { toast } from 'react-toastify';
import { useAppDispatch } from 'store/hooks/index';
import { storeUserToken } from 'store/slices/authSlice';
import { storeAllCartItems } from 'store/slices/cartSlice';
import { storeCompare } from 'store/slices/compareSlice';
import { storeAddresses } from 'store/slices/customerAddressSlice';
import { storeWishlist } from 'store/slices/productsSlice';
import { storeCustomerDetails } from 'store/slices/userSlice';
import { userAPI } from 'APIs';

interface UseSocialSigninReturn {
  isLoading: boolean;
  signInWithGoogle: () => Promise<void>;
  signInWithFacebook: () => Promise<void>;
}

export const useSocialSignin = (): UseSocialSigninReturn => {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const dispatch = useAppDispatch();

  const fetchUserData = async (token: string) => {
    try {
      const response = await userAPI.getCustomer(token);
      dispatch(storeCustomerDetails(response?.data));
      dispatch(storeAddresses(response?.data?.addresses!));
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };

  const fetchCart = async (token: string) => {
    try {
      const response = await userAPI.getCart(token);
      if ('data' in response) {
        dispatch(storeAllCartItems(response?.data?.items || []));
      }
    } catch (error) {
      console.error('Error fetching cart:', error);
    }
  };

  const fetchWishlist = async (token: string) => {
    try {
      const response = await userAPI.getCustomerWishlist(token);
      dispatch(storeWishlist(response || []));
    } catch (error) {
      console.error('Error fetching wishlist:', error);
    }
  };

  const fetchCompare = async () => {
    try {
      const response = await userAPI.getCompare();
      if ('data' in response) {
        dispatch(storeCompare(response?.data || []));
      }
    } catch (error) {
      console.error('Error fetching compare:', error);
    }
  };

  const authenticateWithBackend = async (provider: 'google' | 'facebook') => {
    try {
      // Get the session to extract the access token
      const session = await getSession();

      if (!session?.accessToken) {
        throw new Error('No access token found in session');
      }

      // Call the appropriate backend endpoint
      const endpoint = provider === 'google' ? '/api/googlesignin' : '/api/facebooksignin';

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(session.accessToken),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if ('data' in data && data.data?.token) {
        // Store the backend JWT token
        dispatch(storeUserToken(data.data.token));

        // Fetch user data and other resources
        await Promise.all([
          fetchUserData(data.data.token),
          fetchCart(data.data.token),
          fetchWishlist(data.data.token),
          fetchCompare(),
        ]);

        // Navigate to the main app
        router.push('/post/newsfeed');

        toast.success('Logged in successfully!', {
          containerId: 'bottom-right',
        });
      } else {
        throw new Error(data?.error?.message || 'Authentication failed');
      }
    } catch (error) {
      console.error('Backend authentication error:', error);
      throw error;
    }
  };

  const signInWithProvider = async (provider: 'google' | 'facebook') => {
    setIsLoading(true);

    try {
      // First, sign in with NextAuth
      const result = await signIn(provider, {
        redirect: false,
        callbackUrl: '/post/newsfeed',
      });

      if (result?.error) {
        throw new Error(result.error);
      }

      // Wait a bit for the session to be established
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Then authenticate with our backend
      await authenticateWithBackend(provider);

    } catch (error) {
      console.error(`${provider} signin error:`, error);
      toast.error(`Failed to sign in with ${provider}. Please try again.`, {
        containerId: 'bottom-right',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const signInWithGoogle = () => signInWithProvider('google');
  const signInWithFacebook = () => signInWithProvider('facebook');

  return {
    isLoading,
    signInWithGoogle,
    signInWithFacebook,
  };
};
