@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Custom container class */

:root {
  --primary: white;
  --secondary: #C0C0C0;
}

* {
  font-family: 'Outfit';
  letter-spacing: 1px;
}

.container {
  max-width: 1170px;
  /* opacity: 75%; */
}
@media only screen and (max-width: 767px) {
  .container {
    max-width: 464px;
  }
}
@media only screen and (max-width: 479px) {
  .container {
    width: 100%;
  }
}

/* Dropdown menu component */
@media only screen and (max-width: 479px) {
  .w-dnd {
    width: calc(100% - 2rem) !important;
  }
  .categoryDropdown {
    -webkit-overflow-scrolling: auto !important;
  }
}

/* Full screen shadow for sidebar */
.dnd-shadow {
  box-shadow: 0 0 0 500vmax rgb(0 0 0 / 0.5);
}

/* Swiper components */

.swiper-button-prev {
  display: none !important;
}

.swiper-button-next {
  display: none !important;
}

.swiper:hover .swiper-button-prev {
  display: block !important;
}

.swiper:hover .swiper-button-next {
  display: block !important;
}

.swiper-button-prev::after {
  font-size: 30px !important;
  color: #777;
}

.swiper-button-next::after {
  font-size: 30px !important;
  color: #777;
}

.swiper-pagination-bullet-active {
  background-color: #16a34a !important;
}

.flip-card {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.2);
  border-radius: 0.1em;
}

.top,
.bottom,
.flip-card .top-flip,
.flip-card .bottom-flip {
  height: 0.75em;
  line-height: 1;
  padding: 0.25em;
  overflow: hidden;
}

.top,
.flip-card .top-flip {
  background-color: #006440;
  border-top-right-radius: 0.1em;
  border-top-left-radius: 0.1em;
  border-bottom: 2px solid #24475b;
}

.bottom,
.flip-card .bottom-flip {
  background-color: #006440;
  display: flex;
  align-items: flex-end;
  border-bottom-right-radius: 0.1em;
  border-bottom-left-radius: 0.1em;
}

.flip-card .top-flip {
  position: absolute;
  width: 100%;
  animation: flip-top 250ms ease-in;
  transform-origin: bottom;
}

@keyframes flip-top {
  100% {
    transform: rotateX(90deg);
  }
}

.flip-card .bottom-flip {
  position: absolute;
  bottom: 0;
  width: 100%;
  animation: flip-bottom 250ms ease-out 250ms;
  transform-origin: top;
  transform: rotateX(90deg);
}

@keyframes flip-bottom {
  100% {
    transform: rotateX(0deg);
  }
}

.timer-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.colon {
  color: white;
  font-size: 40px;
}

.container-segment {
  display: flex;
  flex-direction: column;
  gap: 10px;
  align-items: center;
}

.segment {
  display: flex;
  gap: 0.1em;
  border-radius: 5px;
  font-size: 46px;
  color: white;
}

.segment-title {
  font-size: 1rem;
  text-transform: uppercase;
  color: white;
}

@media only screen and (max-width: 767px) {
  .timer-container {
    display: flex;
    gap: 10px;
    justify-content: center;
  }
}

/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--secondary) var(--primary);
}

/* Chrome, Edge, and Safari */
*::-webkit-scrollbar {
  width: 10px;
}

*::-webkit-scrollbar-track {
  background: var(--primary);
  border-radius: 1px;

}

*::-webkit-scrollbar-thumb {
  background-color: var(--secondary);
  border-radius: 14px;
  border: 3px solid var(--primary);
}

html {
  scroll-behavior: smooth;
}



