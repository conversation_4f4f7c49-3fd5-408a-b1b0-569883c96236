import NextAuth from 'next-auth';
import GoogleProvider from 'next-auth/providers/google';
import FacebookProvider from 'next-auth/providers/facebook';
const { GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, FACEBOOK_CLIENT_ID, FACEBOOK_CLIENT_SECRET, NEXTAUTH_SECRET } =
  process?.env;

export default NextAuth({
  providers: [
    GoogleProvider({
      clientId: GOOGLE_CLIENT_ID!,
      clientSecret: GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code',
        },
      },
    }),
    FacebookProvider({
      clientId: FACEBOOK_CLIENT_ID!,
      clientSecret: FACEBOOK_CLIENT_SECRET!,
      authorization: {
        params: {
          auth_type: 'reauthenticate'
        }
      }
    }),
  ],
  secret: NEXTAUTH_SECRET!,
  callbacks: {
    session: async ({ session, token }) => {
      // Pass the access token to the session
      if (token.access_token) {
        session.accessToken = token.access_token as string;
      }
      return session;
    },
    jwt: ({ token, account }) => {
      // Store the access token in the JWT token
      if (account?.access_token) {
        token.access_token = account.access_token;
      }
      return token;
    },
  },
});
