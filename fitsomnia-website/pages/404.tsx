import Image from 'next/legacy/image';
import Link from 'next/link';

import type { NextPage } from 'next';

//import Breadcrumb from '@/modules/common/breadcrumbs/breadcrumb';
import myImageLoader from 'image/loader';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';
import { useEffect } from 'react';

const Breadcrumb = dynamic(
  () => import('@/modules/common/breadcrumbs/breadcrumb')
);

const PageNotFOund: NextPage = () => {
  const imageDimensions = { width: 1024, height: 456 };

  //  const router = useRouter();
   

  //  useEffect(() => {
  //    router.replace('/'); // Redirect to homepage
  //  }, [router]);

  //  return null;

  return (
    <>
      {/* <Breadcrumb
        title="404 Not found"
        pathArray={['Home', '404 Not Found']}
        linkArray={['/market', '/market']}
      /> */}
      <section className="container mx-auto px-4">
        <div className="flex flex-col items-center py-16">
          {/* <div className="mb-8">
            <Image
              loader={myImageLoader}
              src="/public/404-error.png"
              alt="Page not found!!"
              width={imageDimensions.width}
              height={imageDimensions.height}
              className="mb-8"
            />
          </div> */}
          <span className="mb-2 font-bold">Ooops! Error 404</span>
          <span className="mb-4 text-center font-extralight">
            Sorry, But the page you are looking for doesn&apos;t exist!
          </span>
          <button className="rounded-md bg-green-600 py-2 px-6 font-light text-white transition-all duration-200 ease-linear hover:bg-stone-900">
            <Link prefetch={false} href="/">
              Go to home page
            </Link>
          </button>
        </div>
      </section>
    </>
  );
};

export default PageNotFOund;
