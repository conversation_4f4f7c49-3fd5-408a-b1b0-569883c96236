import WithAuth from '@/modules/auth/withAuth';
import Loading from '@/modules/common/loader';
import PostModal from '@/modules/common/modal/postModal';
import StoryComponent from '@/modules/story';
import { userAPI } from 'APIs';
import { timeAgo } from 'helper/timeAgoUtil';
import myImageLoader from 'image/loader';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import Carousel from 'react-multi-carousel';
import 'react-multi-carousel/lib/styles.css';
import { toast } from 'react-toastify';
// import { useAppSelector } from 'store/hooks';
import { HeaderPage } from '@/modules/landingPage/components/First';
import { reactions } from 'helper/reactions';
import Link from 'next/link';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import { NewsFeedItem } from 'store/slices/postSlice';
var cookie = require('cookie');

// // const NewsFeedComponent = dynamic(() => import('@/modules/cart/index'), {
// //   suspense: true,
// // });

const NewsFeed = () => {
  const dispatch = useAppDispatch();
  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  const [posts, setPosts] = useState<NewsFeedItem[]>([]);
  // console.log(posts[0]?.reactionType)
  // console.log('post',posts.length)
  // const posts = useAppSelector(
  //   (state) => state.persistedReducer.posts.allNewsFeedItems
  // );
  const [hasMore, setHasMore] = useState(true);

  const customer = useAppSelector(
    (state) => state.persistedReducer.user.customerDetails!
  );

  const fetchNewsFeed = async () => {
    try {
      const res = await userAPI.getNewsFeed(token);
      // console.log('post is fetched')// Include lastPostId to get the next chunk

      if ('data' in res) {
        if (res.data.length === 0) {
          setHasMore(false); // No more posts
          return;
        }

        // console.log(res.data.length);

        // dispatch(storeAllPostItems(res.data));

        setPosts((prevPosts) => [...prevPosts, ...res.data]); // Append new posts to the list
        // setPosts(res.data);
      } else {
        toast.error(res?.error.message, { containerId: 'bottom-right' });
        setHasMore(false);
      }
    } catch (error) {
      console.error(error);
      setHasMore(false);
    }
  };

  useEffect(() => {
    fetchNewsFeed();
    // Initial fetch
  }, []);

  const [modalOn, setModalOn] = useState(false);
  const [choice, setChoice] = useState(false);
  const [hoveredPost, setHoveredPost] = useState<any>();

  const handleReactionClick = async (
    postId: string,
    userId: string,
    reactionLabel: string,
    currentReactionType: string | null
  ) => {
    if (currentReactionType === null) {
      // Send the new reaction and set isReacting to true (indicating a new reaction)
      await updateReaction(postId, userId, reactionLabel, true);
    } else {
      // Case 2: If the user already reacted with the same reaction, we want to remove it (set isReacting to false)
      if (currentReactionType === reactionLabel) {
        // Remove the reaction by setting isReacting to false
        await updateReaction(postId, userId, reactionLabel, false);
      } else {
        // Case 3: If the user has reacted with a different reaction, we need to update it
        // Send the new reaction label with isReacting set to true
        await updateReaction(postId, userId, reactionLabel, true);
      }
    }

    setHoveredPost(null);
  };

  const updateReaction = async (
    postId: string,
    userId: string,
    reactionLabel: string,
    isReacting: boolean
  ) => {
    try {
      const resReaction = await userAPI.sendMultiReaction(
        postId,
        userId,
        reactionLabel,
        isReacting
      );

      if ('data' in resReaction) {
        setPosts((prevPosts) =>
          prevPosts.map((post) =>
            post.id === postId
              ? {
                  ...post,
                  totalReactions: resReaction.data.totalReactions,
                  reactionType: resReaction.data.reactionType,
                }
              : post
          )
        );
      }
    } catch (error) {
      console.error('Error updating reaction:', error);
    }
  };

  return (
    <>
      <HeaderPage />
      <hr />
      <div>
        <StoryComponent url={customer?.image?.profile} />
      </div>
      <hr />

      {modalOn && (
        <PostModal
          setModalOn={setModalOn}
          setChoice={setChoice}
          modalTitle="Create Post"
          bodyText="Proceed to login?"
        />
      )}

      <div className="mx-auto flex max-w-4xl items-center justify-center gap-2 space-y-6 p-4">
        <Link prefetch={false} href="/myAccount">
          <Image
            src={customer?.image?.profile || '/user.png'}
            alt="User"
            width={40}
            height={40}
            className="mt-4 h-12 cursor-pointer  w-12 rounded-full"
            loader={myImageLoader}
            // unoptimized
          />
        </Link>

        <div
          onClick={() => setModalOn(true)}
          className="flex h-[60px] w-[884px] cursor-pointer items-center gap-2 rounded-lg border border-[#E0E2E5] bg-white px-4 py-6"
        >
          <input
            type="text"
            placeholder="what you’re thinking.........."
            className="w-full cursor-pointer bg-transparent outline-none"
          />
        </div>
      </div>
      <InfiniteScroll
        dataLength={posts.length}
        next={fetchNewsFeed} // Fetch next chunk of posts
        hasMore={hasMore}
        loader={<Loading />}
        endMessage={<p style={{ textAlign: 'center' }}>No more posts</p>}
      >
        <div className="mx-auto max-w-4xl space-y-6 p-4">
          {posts.map((item, index) => (
            <div
              key={index + 1}
              className="mb-4  border border-gray-200 bg-white shadow-md"
            >
              {/* Header */}
              <div className="flex items-center space-x-3">
                <Image
                  src={item?.userInfo?.image?.profile ?? '/user.png'}
                  alt="User"
                  width={40}
                  height={40}
                  className="h-10 w-10 rounded-full"
                  // loader={myImageLoader}
                  unoptimized
                />
                <div>
                  <p className="font-semibold">{item.userInfo.name}</p>
                  <div className="flex gap-1">
                    <Image
                      src="/people.png"
                      alt="people"
                      width={20}
                      height={12}
                    />
                    <span className="text-sm text-black">
                      {timeAgo(item.createdAt)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Post Content */}
              <p className="my-3 break-words text-gray-800">{item.content}</p>

              <Carousel
                additionalTransfrom={0}
                arrows={false}
                autoPlaySpeed={3000}
                centerMode={false}
                className=""
                containerClass="container"
                dotListClass=""
                draggable
                focusOnSelect={false}
                infinite
                itemClass=""
                keyBoardControl
                minimumTouchDrag={80}
                pauseOnHover
                renderArrowsWhenDisabled={false}
                renderButtonGroupOutside={false}
                renderDotsOutside={false}
                responsive={{
                  desktop: {
                    breakpoint: {
                      max: 2000,
                      min: 1024,
                    },
                    items: 1,
                  },
                  mobile: {
                    breakpoint: {
                      max: 464,
                      min: 0,
                    },
                    items: 1,
                  },
                  tablet: {
                    breakpoint: {
                      max: 1024,
                      min: 464,
                    },
                    items: 1,
                  },
                }}
                rewind={false}
                rewindWithAnimation={false}
                rtl={false}
                shouldResetAutoplay
                showDots
                sliderClass=""
                slidesToSlide={1}
                swipeable
              >
                {[
                  ...item.images.map((image) => ({
                    type: 'image',
                    src: image.small,
                  })),
                  ...item.videos.map((video) => ({
                    type: 'video',
                    src: video.thumbnail,
                    // Assuming each video has a URL
                  })),
                ].map((media, index) => (
                  <div
                    key={index}
                    style={{
                      width: '100%',
                      height: '600px',
                      overflow: 'hidden',
                    }}
                  >
                    {media.type === 'image' ? (
                      <Image
                        src={media.src}
                        alt={`Media ${index + 1}`}
                        width={40}
                        height={40}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover',
                        }}
                        unoptimized
                      />
                    ) : (
                      <Image
                        src={media.src}
                        alt={`Media ${index + 1}`}
                        width={40}
                        height={40}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover',
                        }}
                        unoptimized
                      />
                    )}
                  </div>
                ))}
              </Carousel>

              <div className="relative mt-2 flex justify-between">
                <div className="item-center relative flex gap-4">
                  {/* Like Button with Reactions */}
                  <div
                    className="relative flex cursor-pointer items-center gap-2"
                    onMouseEnter={() => setHoveredPost(item.id)}
                    onMouseLeave={() => setHoveredPost(null)}
                  >
                    <Image
                      src="/reaction/like.png"
                      alt="reaction"
                      height={20}
                      width={20}
                      unoptimized
                    />
                    <span>{item.totalReactions} likes</span>

                    {/* Reaction Options - Shown on Hover */}
                    {hoveredPost === item.id && (
                      <div
                        className="absolute bottom-8 left-0 flex h-[54px] w-[300px] items-center  gap-4 rounded-full bg-white  p-[9px] px-[15px] shadow-lg "
                        //  className="absolute bottom-full left-0 z-10 mb-2 flex h-[54px] w-[372px] items-center gap-4 rounded-full bg-white p-[9px] px-[15px] shadow-md transition-opacity duration-200"
                      >
                        {reactions.map((reaction, index) => (
                          <div
                            key={index}
                            onClick={() =>
                              handleReactionClick(
                                item.id,
                                item.userId,
                                reaction.label,
                                item.reactionType
                              )
                            }
                            className="transform cursor-pointer transition-transform hover:scale-110"
                          >
                            <Image
                              src={reaction.url}
                              alt={reaction.label}
                              height={30}
                              width={30}
                              unoptimized
                            />
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Comments Section */}
                  <div className="flex items-center gap-2">
                    <Image
                      src="/reaction/comment.png"
                      alt="reaction"
                      height={20}
                      width={20}
                      unoptimized
                    />
                    <span>{item.totalComments} comments</span>
                  </div>
                </div>

                {/* Reaction & Comments Icons */}
                <div className="item-center flex gap-2">
                  <Image
                    src="/reaction/comments1.png"
                    alt="reaction"
                    height={32}
                    width={32}
                    unoptimized
                  />
                  <Image
                    src={
                      reactions.find(
                        (reaction) => reaction.label === item.reactionType
                      )?.url ?? '/reaction/likes1.png'
                    }
                    alt={item.reactionType || 'Default Reaction'}
                    height={32}
                    width={32}
                    unoptimized
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </InfiniteScroll>
    </>
  );
};

export default WithAuth(NewsFeed);
