import { GetServerSideProps, NextPage } from 'next';
var cookie = require('cookie');

//import Profile from '@/modules/myAccount/profile/components';
import { userAPI } from 'APIs';
import { GetUserInformationSuccessResponse } from 'models';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import { storeCustomerDetails } from 'store/slices/userSlice';
import dynamic from 'next/dynamic';
import { Suspense, useEffect } from 'react';
import Loading from '@/modules/common/loader';
import { toast } from 'react-toastify';

const Profile = dynamic(
  () => import('@/modules/myAccount/profile/components'),
  {
    suspense: true,
  }
);

interface Props {
  customerInformation: GetUserInformationSuccessResponse;
}

const MyAccount: NextPage<Props> = ({ customerInformation }: Props) => {
  // console.log(customerInformation);
  const dispatch = useAppDispatch();

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  const getCustomerAddressData = async () => {
    try {
      const customerProfile = await userAPI.getCustomer(token);
      if ('data' in customerProfile) {
        dispatch(storeCustomerDetails(customerProfile?.data!));
      } else {
        toast.error(customerProfile?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
  };

  useEffect(() => {
    getCustomerAddressData();
  }, []);

  return (
    <Suspense fallback={<Loading />}>
      <div>
        <Profile />;
      </div>
    </Suspense>
  );
};
export default MyAccount;

// export const getServerSideProps: GetServerSideProps = async (context) => {
//   const token = cookie?.parse(context.req?.headers?.cookie);
//   const customerInformation = await userAPI.getCustomer(token.token);
//   return {
//     props: {
//       customerInformation,
//     },
//   };
// };
