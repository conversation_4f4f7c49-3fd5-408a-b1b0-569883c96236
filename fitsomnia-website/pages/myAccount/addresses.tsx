import type { GetServerSideProps, NextPage } from 'next';
var cookie = require('cookie');

//import AddressesComponent from '@/modules/myAccount/addresses/components';
import { userAPI } from 'APIs';
import { User } from 'models';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import { storeAddresses } from 'store/slices/customerAddressSlice';
import { storeCustomerDetails } from 'store/slices/userSlice';

import dynamic from 'next/dynamic';
import { Suspense, useEffect } from 'react';
import Loading from '@/modules/common/loader';
import { toast } from 'react-toastify';

const AddressesComponent = dynamic(
  () => import('@/modules/myAccount/addresses/components'),
  {
    suspense: true,
  }
);

interface Props {
  customerProfile: User;
}

const Addresses: NextPage<Props> = ({ customerProfile }) => {
  const dispatch = useAppDispatch();
  dispatch(storeCustomerDetails(customerProfile!));
  dispatch(storeAddresses(customerProfile?.addresses!));

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );

  const getCustomerAddressData = async () => {
    try {
      const customerProfile = await userAPI.getCustomerProfile(token);
      if ('data' in customerProfile) {
        dispatch(storeCustomerDetails(customerProfile?.data!));
        dispatch(storeAddresses(customerProfile?.data.addresses!));
      } else {
        toast.error(customerProfile?.error.message, {
          containerId: 'bottom-right',
        });
      }
    } catch (error) {}
  };

  useEffect(() => {
    getCustomerAddressData();
  }, []);

  return (
    <Suspense fallback={<Loading />}>
      <div>
        <AddressesComponent />
      </div>
    </Suspense>
  );
};

// export const getServerSideProps: GetServerSideProps = async (context) => {
//   const token = cookie?.parse(context.req?.headers?.cookie);
//   const customerProfile = await userAPI.getCustomerProfile(token.token);

//   return {
//     props: {
//       customerProfile: customerProfile?.data,
//     },
//   };
// };

export default Addresses;
