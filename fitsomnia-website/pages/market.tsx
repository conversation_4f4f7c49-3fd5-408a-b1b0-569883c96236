import { GetServerSideProps, NextPage } from 'next';
var cookie = require('cookie');

import { userAPI } from 'APIs';
import { NestedCategoryList, UserProduct, Wishlist } from 'models';
import { useAppDispatch, useAppSelector } from 'store/hooks';
import { storeCategory } from 'store/slices/categorySlice';
import {
  storeFeaturedProducts,
  storeProducts,
  storeWishlist,
} from 'store/slices/productsSlice';

import axios from 'axios';
import { getSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import { toast } from 'react-toastify';
import { storeUserToken } from 'store/slices/authSlice';
import { storeAllCartItems } from 'store/slices/cartSlice';
import { storeCompare } from 'store/slices/compareSlice';
import { storeAddresses } from 'store/slices/customerAddressSlice';
import { storeCustomerDetails } from 'store/slices/userSlice';
import HomeComponent from '@/modules/home';
import { config } from 'config';

interface Props {
  products: UserProduct[];
  featuredProducts: UserProduct[];
  categories: NestedCategoryList[];
  wishlistedProducts: Wishlist;
}

const Home: NextPage<Props> = ({
  products,
  featuredProducts,
  categories,
  wishlistedProducts,
}: Props) => {
  const router = useRouter();

  const fetchWislist = async (token: string) => {
    const wishlistedProducts = await userAPI.getCustomerWishlist(token);
    dispatch(storeWishlist(wishlistedProducts!));
  };

  const fetchCart = async (token: string) => {
    const cartProducts = await userAPI.getCart(token);
    if ('data' in cartProducts)
      dispatch(storeAllCartItems(cartProducts?.data?.items!));
    else {
      toast.error(cartProducts?.error.message, {
        containerId: 'bottom-right',
      });
    }
  };

  const fetchCompare = async () => {
    const compareProducts = await userAPI.getCompare();
    if ('data' in compareProducts!)
      dispatch(storeCompare(compareProducts?.data!));
  };

  const providerName = useAppSelector(
    (state) => state?.persistedReducer?.provider?.provider
  );

  const token = useAppSelector(
    (state) => state?.persistedReducer?.auth.access_token
  );

  const handleSession = async () => {
    const session = await getSession();
    if (session) {
      try {
        const accessToken = (
          await axios.get('http://localhost:4002/api/auth/tokenHandler')
        ).data.token;
        const url =
          providerName === 'google'
            ? '/api/googlesignin'
            : '/api/facebooksignin';
        const tokenfromBE = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(accessToken),
        });
        const res = await tokenfromBE.json();
        if ('data' in res!) {
          dispatch(storeUserToken(res.data.token));
          await userAPI.getCustomer(res?.data?.token).then((response) => {
            dispatch(storeCustomerDetails(response?.data));
            dispatch(storeAddresses(response?.data?.addresses!));
          });
          await fetchCart(res?.data?.token);
          await fetchWislist(res?.data?.token);
          await fetchCompare();
          //router.push('/market');
          toast.success('Logged in successfully!', {
            containerId: 'bottom-right',
          });
        } else {
          router.push('/account/sign-in');
          toast.error('Something went wrong', {
            containerId: 'bottom-right',
          });
        }
      } catch (error) {
        console.log(error);
      }
    }
  };

  const dispatch = useAppDispatch();

  dispatch(storeCategory(categories));
  dispatch(storeProducts(products));
  dispatch(storeFeaturedProducts(featuredProducts));
  dispatch(storeWishlist(wishlistedProducts));

  useEffect(() => {
    if (token === '') handleSession();
  }, []);

  return <HomeComponent />;
};

export const getServerSideProps: GetServerSideProps = async (context) => {
  const reqCookie = context.req.headers.cookie;
  const token = reqCookie === undefined ? undefined : cookie.parse(reqCookie);
  const allProducts = await userAPI.getPublicProducts();

  const featuredProducts = await userAPI.getFeaturedProducts();
  const responseCategory = await userAPI.getCategoryList();
  //const result = responseCategory.find(responseCategory => responseCategory.data)

  let wishlistedProducts;
  if (reqCookie) {
    wishlistedProducts = await userAPI.getCustomerWishlist(token?.token);
  } else {
    wishlistedProducts = [];
  }

  return {
    props: {
      products: allProducts?.data?.products,
      featuredProducts: featuredProducts,
      categories: responseCategory?.data?.categories,
      // products: [],
      // featuredProducts: [],
      // categories: [],
      wishlistedProducts: wishlistedProducts,
    },
  };
};

export default Home;
