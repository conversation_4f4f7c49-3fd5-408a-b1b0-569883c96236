import { userAP<PERSON> } from 'APIs';
import axios, { default as Axios } from 'axios';
import { FileUploadRequestBody } from 'models';

export const handleMediaUpload = async (
  fileData: FileUploadRequestBody,
  files: File[],
  token: string,
  isPublic: boolean
): Promise<string[]> => {
  try {
    let uploadedFiles: string[] = [];

    if (!isPublic) {
      // Step 1: Get presigned URLs for all files
      const fileUploadData = {
        featureName: fileData.featureName,
        filenames: files.map((file) => file.name), // Ensure multiple filenames are sent
      };

      // console.log('fileUploadData',fileUploadData);

      const res = await userAPI.uploadPrivateMedia(fileUploadData);
      // console.log('res data', res)

      if (!res?.data?.length) {
        throw new Error('Failed to get presigned URLs.');
      }


      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const presignedUrl = res.data[i]?.presignedUrl;
        const s3UploadedURLKey = res.data[i]?.s3UploadedURLKey || '';

        // console.log('presigned url', presignedUrl)
        // console.log('s3 upload key',s3UploadedURLKey)

        if (!presignedUrl) {
          console.error(`No presigned URL found for file: ${file.name}`);
          continue;
        }

        try {
          // Step 2: Temporarily remove Authorization header
          delete axios.defaults.headers.common['Authorization'];

          // Step 3: Upload file to S3
          await axios.put(presignedUrl, file, {
            headers: {
              'Content-Type': file.type,
            },
          });

          // Step 4: Restore Authorization header
          axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;

          uploadedFiles.push(s3UploadedURLKey);
        } catch (uploadError) {
          console.error(`Upload failed for file: ${file.name}`, uploadError);
        }
      }
    } else {
      for (const file of files) {
        const res = await userAPI.uploadPublicMedia(file, fileData.featureName);
       
        if (res?.data) {
          uploadedFiles.push(res.data);
        }
      }
    }

  
    return uploadedFiles;
  } catch (error) {
    console.error('Error uploading media:', error);
    throw error;
  }
};
