import { Injectable, Logger } from '@nestjs/common';
import { AzureOpenAI } from 'openai';
import type { ChatCompletionCreateParams, ChatCompletionMessageParam } from 'openai/resources/chat/completions';
import { Readable } from 'stream';

export interface ScannerInput {
  imageUrl: string;
  prompt: string;
  options: {
    temperature?: number;
    max_tokens?: number;
    response_format?: { type: 'json_object' };
    deployment?: string;
  };
}

export interface ImageAnalysisResponse {
  content: string;
  raw?: any;
}

@Injectable()
export class AzureOpenAIService{
  private readonly logger = new Logger(AzureOpenAIService.name);
  private client: AzureOpenAI;

  constructor() {
    const endpoint = process.env.AZURE_OPENAI_ENDPOINT || 'https://fitsomnia-ai.openai.azure.com/';
    const apiKey = process.env.AZURE_OPENAI_API_KEY;
    const apiVersion = process.env.AZURE_OPENAI_API_VERSION || '2025-01-01-preview';
    const deployment = process.env.AZURE_OPENAI_DEPLOYMENT || 'gpt-4.1';

    if (!apiKey) {
      throw new Error('AZURE_OPENAI_API_KEY is required');
    }

    if (!endpoint) {
      throw new Error('AZURE_OPENAI_ENDPOINT is required');
    }

    this.client = new AzureOpenAI({
      endpoint,
      apiKey,
      apiVersion,
      deployment,
    });
  }


  /**
   * Generate a chat completion from Azure OpenAI
   */
  async generateChatCompletion(
    messages: ChatCompletionMessageParam[],
    options: {
      temperature?: number;
      max_tokens?: number;
      response_format?: ChatCompletionCreateParams['response_format'];
      deployment?: string;
    } = {},
  ) {
    try {
      const deployment = options.deployment || 'gpt-4.1';
      
      const result = await this.client.chat.completions.create({
        model: deployment,
        messages,
        max_tokens: options.max_tokens || 800,
        temperature: options.temperature || 0.3,
        response_format: options.response_format,
      });

      return result.choices[0]?.message?.content || '';
    } catch (error) {
      this.logger.error('Error generating chat completion:', error);
      
      // Enhanced error handling
      if (error.response) {
        const statusCode = error.response.status;
        const errorMessage = error.response.data?.error?.message || 'Unknown API error';
        
        this.logger.error(`Azure OpenAI API error (${statusCode}): ${errorMessage}`);
        
        if (statusCode === 400) {
          throw new Error(`Bad request to Azure OpenAI: ${errorMessage}`);
        } else if (statusCode === 401) {
          throw new Error('Authentication error with Azure OpenAI API. Check your API key.');
        } else if (statusCode === 429) {
          throw new Error('Rate limit exceeded for Azure OpenAI API. Please try again later.');
        }
      }
      
      throw error;
    }
  }

  /**
   * Generate a streaming chat completion from Azure OpenAI
   */
  async streamChatCompletion(
    messages: ChatCompletionMessageParam[],
    options: {
      temperature?: number;
      max_tokens?: number;
      deployment?: string;
    } = {},
  ): Promise<Readable> {
    try {
      const deployment = options.deployment || process.env.AZURE_OPENAI_DEPLOYMENT || 'gpt-4.1';
      
      const stream = await this.client.chat.completions.create({
        model: deployment,
        messages,
        max_tokens: options.max_tokens || 800,
        temperature: options.temperature || 0.3,
        stream: true,
      });

      const readableStream = new Readable({
        read() {
          // Required method for Readable stream
        },
      });

      (async () => {
        try {
          for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content || '';
            if (content) {
              readableStream.push(content);
            }
          }
          readableStream.push(null); // End the stream
        } catch (error) {
          readableStream.destroy(error);
        }
      })();

      return readableStream;
    } catch (error) {
      this.logger.error('Error streaming chat completion:', error);
      
      // Enhanced error handling
      if (error.response) {
        const statusCode = error.response.status;
        const errorMessage = error.response.data?.error?.message || 'Unknown API error';
        
        this.logger.error(`Azure OpenAI API error (${statusCode}): ${errorMessage}`);
        
        if (statusCode === 400) {
          throw new Error(`Bad request to Azure OpenAI: ${errorMessage}`);
        } else if (statusCode === 401) {
          throw new Error('Authentication error with Azure OpenAI API. Check your API key.');
        } else if (statusCode === 429) {
          throw new Error('Rate limit exceeded for Azure OpenAI API. Please try again later.');
        }
      }
      
      throw error;
    }
  }

  /**
   * Analyze an image using Azure OpenAI Vision capabilities
   */
  async analyzeImage(
    input: ScannerInput
  ): Promise<ImageAnalysisResponse> {
    try {
      const deployment = input.options.deployment || 'gpt-4.1';

      console.log('deployment', deployment);
      
      const result = await this.client.chat.completions.create({
        model: deployment,
        messages: [
          {
            role: 'system',
            content: input.prompt,
          },
          {
            role: 'user',
            content: [
              {
                type: 'image_url',
                image_url: {
                  url: input.imageUrl,
                },
              },
            ],
          },
        ],
        max_tokens: input.options.max_tokens || 800,
        temperature: input.options.temperature || 0.3,
        response_format: input.options.response_format,
      });

      return {
        content: result.choices[0]?.message?.content || '',
        raw: result
      };
    } catch (error) {
      this.logger.error('Error analyzing image:', error);
      
      // Enhanced error handling
      if (error.response) {
        const statusCode = error.response.status;
        const errorMessage = error.response.data?.error?.message || 'Unknown API error';
        
        this.logger.error(`Azure OpenAI API error (${statusCode}): ${errorMessage}`);
        
        if (statusCode === 400) {
          throw new Error(`Bad request to Azure OpenAI: ${errorMessage}`);
        } else if (statusCode === 401) {
          throw new Error('Authentication error with Azure OpenAI API. Check your API key.');
        } else if (statusCode === 429) {
          throw new Error('Rate limit exceeded for Azure OpenAI API. Please try again later.');
        }
      }
      
      throw error;
    }
  }


  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.generateChatCompletion([
        { role: 'user', content: 'Hello' }
      ], { max_tokens: 10 });
      return !!result;
    } catch (error) {
      this.logger.error('Health check failed:', error);
      return false;
    }
  }
}