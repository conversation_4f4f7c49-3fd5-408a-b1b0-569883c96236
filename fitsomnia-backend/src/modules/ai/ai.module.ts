import { Module } from '@nestjs/common';
import { ChatbotProvider } from './providers/chatbot.provider';
import { FoodScannerAIProvider } from './providers/food-scanner.provider';
import { AzureOpenAIService } from './services/azure-openai.service';
@Module({
  controllers: [],
  providers: [
    AzureOpenAIService,
    FoodScannerAIProvider,
    ChatbotProvider,
  ],
  exports: [
    AzureOpenAIService,
    FoodScannerAIProvider,
    ChatbotProvider
  ],
})
export class AIModule {}