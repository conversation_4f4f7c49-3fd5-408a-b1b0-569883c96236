import { Injectable } from '@nestjs/common';
import { BodyStatus } from '../../common/const/enum';
import { AvatarRepository } from '../repositories/avatar.repository';

@Injectable()
export class AvatarHelperService {
  constructor(private readonly avatarRepo: AvatarRepository) {}

  async calculateBodyStatusFromWeight(
    avatarId: string,
    weight: number,
  ): Promise<BodyStatus> {
    const bodyStatus = await this.avatarRepo.calculateBodyStatusFromWeight(
      avatarId,
      weight,
    );
    return bodyStatus as BodyStatus;
  }

  async generateAvatarAssetUrl(avatarState: any): Promise<string> {
    const { avatarId, avatarType, bodyStatus, emotion } = avatarState;

    // Try to get specific asset from database
    const asset = await this.avatarRepo.findEmotionAssetByConditions(
      avatarId,
      avatarType,
      bodyStatus,
      emotion,
    );

    if (asset) {
      return asset.assetPath;
    }

    // Fallback to generated path
    return `cdn/avatars/${avatarType}/${avatarId}/${bodyStatus}_${emotion}.${
      avatarType === '3d' ? 'glb' : 'png'
    }`;
  }
}
