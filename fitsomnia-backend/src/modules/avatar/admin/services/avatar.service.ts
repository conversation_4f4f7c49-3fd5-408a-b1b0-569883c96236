import { HttpStatus, Injectable } from '@nestjs/common';
import { Helper } from 'src/helper/helper.interface';
import { APIException } from 'src/internal/exception/api.exception';
import { Accessory } from '../../common/entities/accessory';
import { Avatar } from '../../common/entities/avatar';
import { AvatarBaseBody } from '../../common/entities/avatar-base-body';
import { AvatarEmotionSupport } from '../../common/entities/avatar-emotion';
import { AvatarEmotionAsset } from '../../common/entities/avatar-emotion-asset';
import { AvatarWeightThreshold } from '../../common/entities/avatar-weight';
import {
  CreateWeightThresholdDto,
  CreateWeightThresholdResponseDto,
  CreateWeightThresholdSuccessResponseDto,
} from '../dtos/create-avatar-weight.dto';

import { deepCasting } from 'src/internal/casting/object.casting';
import { GetAvatarDetailsSuccessResponseDto } from '../dtos/admin-get-avatar.dto';
import {
  GetAllAvatarsResponseDto,
  GetAllAvatarsSuccessResponseDto,
} from '../dtos/admin-get-avatars.dto';
import {
  CreateAccessoryDto,
  CreateAccessoryResponseDto,
  CreateAccessorySuccessResponseDto,
} from '../dtos/create-accessory.dto';
import {
  CreateAvatarDto,
  CreateAvatarResponseDto,
  CreateAvatarSuccessResponseDto,
} from '../dtos/create-avatar.dto';
import {
  CreateBaseBodyDto,
  CreateBaseBodyResponseDto,
  CreateBaseBodySuccessResponseDto,
} from '../dtos/create-base-body.dto';
import {
  CreateEmotionAssetDto,
  CreateEmotionAssetResponseDto,
  CreateEmotionAssetSuccessResponseDto,
} from '../dtos/create-emotion-asset.dto';
import {
  CreateEmotionDto,
  CreateEmotionResponseDto,
  CreateEmotionSuccessResponseDto,
} from '../dtos/create-emotion.dto';
import { AvatarRepository } from '../repositories/avatar.repository';

@Injectable()
export class AdminAvatarService {
  constructor(
    private readonly avatarRepo: AvatarRepository,
    private readonly helper: Helper,
  ) {}

  async createAvatar(
    createAvatarDto: CreateAvatarDto,
  ): Promise<CreateAvatarSuccessResponseDto> {
    const avatarData: Avatar = {
      ...createAvatarDto,
      isActive: true,
    };

    const result = await this.avatarRepo.createAvatar(avatarData);

    if (!result) {
      throw new APIException(
        'Failed to create avatar',
        'AVATAR_CREATION_FAILED',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const responseDto = deepCasting(CreateAvatarResponseDto, result);

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async addWeightThreshold(
    avatarId: string,
    createThresholdDto: CreateWeightThresholdDto,
  ): Promise<CreateWeightThresholdSuccessResponseDto> {
    // Verify avatar exists
    const avatar = await this.avatarRepo.findAvatarById(avatarId);
    if (!avatar) {
      throw new APIException(
        'Avatar not found',
        'AVATAR_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const thresholdData: AvatarWeightThreshold = {
      avatarId,
      ...createThresholdDto,
    };

    const result = await this.avatarRepo.createWeightThreshold(thresholdData);

    if (!result) {
      throw new APIException(
        'Failed to create weight threshold',
        'THRESHOLD_CREATION_FAILED',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const responseDto = deepCasting(CreateWeightThresholdResponseDto, result);

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async addBaseBody(
    avatarId: string,
    createBaseBodyDto: CreateBaseBodyDto,
  ): Promise<CreateBaseBodySuccessResponseDto> {
    // Verify avatar exists
    const avatar = await this.avatarRepo.findAvatarById(avatarId);
    if (!avatar) {
      throw new APIException(
        'Avatar not found',
        'AVATAR_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const baseBodyData: AvatarBaseBody = {
      avatarId,
      ...createBaseBodyDto,
    };

    const result = await this.avatarRepo.createBaseBody(baseBodyData);

    if (!result) {
      throw new APIException(
        'Failed to create base body',
        'BASE_BODY_CREATION_FAILED',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const responseDto = deepCasting(CreateBaseBodyResponseDto, result);

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async addEmotion(
    avatarId: string,
    createEmotionDto: CreateEmotionDto,
  ): Promise<CreateEmotionSuccessResponseDto> {
    // Verify avatar exists
    const avatar = await this.avatarRepo.findAvatarById(avatarId);
    if (!avatar) {
      throw new APIException(
        'Avatar not found',
        'AVATAR_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const emotionData: AvatarEmotionSupport = {
      avatarId,
      ...createEmotionDto,
    };

    const result = await this.avatarRepo.createEmotion(emotionData);

    if (!result) {
      throw new APIException(
        'Failed to create emotion',
        'EMOTION_CREATION_FAILED',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    const responseDto = deepCasting(CreateEmotionResponseDto, result);

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async addEmotionAsset(
    avatarId: string,
    createEmotionAssetDto: CreateEmotionAssetDto,
  ): Promise<CreateEmotionAssetSuccessResponseDto> {
    // Verify avatar exists
    const avatar = await this.avatarRepo.findAvatarById(avatarId);
    if (!avatar) {
      throw new APIException(
        'Avatar not found',
        'AVATAR_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    // Verify emotion exists for this avatar
    const emotions = await this.avatarRepo.findEmotionsByAvatarId(avatarId);
    const emotionExists = emotions.some(
      (e) => e.emotion === createEmotionAssetDto.emotion,
    );

    if (!emotionExists) {
      throw new APIException(
        'Emotion not supported for this avatar',
        'EMOTION_NOT_SUPPORTED',
        HttpStatus.BAD_REQUEST,
      );
    }

    const assetData: AvatarEmotionAsset = {
      avatarId,
      ...createEmotionAssetDto,
    };

    const result = await this.avatarRepo.createEmotionAsset(assetData);

    if (!result) {
      throw new APIException(
        'Failed to create emotion asset',
        'EMOTION_ASSET_CREATION_FAILED',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    const responseDto = deepCasting(CreateEmotionAssetResponseDto, result);

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async createAccessory(
    createAccessoryDto: CreateAccessoryDto,
  ): Promise<CreateAccessorySuccessResponseDto> {
    // Verify all compatible avatars exist
    for (const avatarId of createAccessoryDto.compatibleAvatars) {
      const avatar = await this.avatarRepo.findAvatarById(avatarId);
      if (!avatar) {
        throw new APIException(
          `Avatar ${avatarId} not found`,
          'AVATAR_NOT_FOUND',
          HttpStatus.NOT_FOUND,
        );
      }
    }

    const accessoryData: Accessory = {
      ...createAccessoryDto,
      isActive: true,
    };

    const result = await this.avatarRepo.createAccessory(accessoryData);

    if (!result) {
      throw new APIException(
        'Failed to create accessory',
        'ACCESSORY_CREATION_FAILED',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const responseDto = deepCasting(CreateAccessoryResponseDto, result);

    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getAvatarDetails(
    avatarId: string,
  ): Promise<GetAvatarDetailsSuccessResponseDto> {
    const avatar = await this.avatarRepo.findAvatarById(avatarId);
    if (!avatar) {
      throw new APIException(
        'Avatar not found',
        'AVATAR_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const [thresholds, baseBodies, emotions, emotionAssets] = await Promise.all(
      [
        this.avatarRepo.findWeightThresholdsByAvatarId(avatarId),
        this.avatarRepo.findBaseBodyByAvatarId(avatarId),
        this.avatarRepo.findEmotionsByAvatarId(avatarId),
        this.avatarRepo.findEmotionAssetsByAvatarId(avatarId),
      ],
    );

    return this.helper.serviceResponse.successResponse({
      avatar,
      thresholds,
      baseBodies,
      emotions,
      emotionAssets,
    });
  }

  async getAllAvatars(): Promise<GetAllAvatarsSuccessResponseDto> {
    const avatars = await this.avatarRepo.findAllActiveAvatars();
    const responseDto = avatars.map((item) =>
      deepCasting(GetAllAvatarsResponseDto, item),
    );
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getBaseBodies(avatarId: string) {
    const avatar = await this.avatarRepo.findAvatarById(avatarId);
    if (!avatar) {
      throw new APIException(
        'Avatar not found',
        'AVATAR_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const baseBodies = await this.avatarRepo.findBaseBodyByAvatarId(avatarId);
    return this.helper.serviceResponse.successResponse(baseBodies);
  }

  async updateBaseBody(
    avatarId: string,
    baseBodyId: string,
    updateBaseBodyDto: CreateBaseBodyDto,
  ) {
    const baseBody = await this.avatarRepo.findBaseBodyById(baseBodyId);
    if (!baseBody || baseBody.avatarId !== avatarId) {
      throw new APIException(
        'Base body not found',
        'BASE_BODY_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const result = await this.avatarRepo.updateBaseBody(
      baseBodyId,
      updateBaseBodyDto,
    );

    if (!result) {
      throw new APIException(
        'Failed to update base body',
        'BASE_BODY_UPDATE_FAILED',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const responseDto = deepCasting(CreateBaseBodyResponseDto, result);
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async deleteBaseBody(avatarId: string, baseBodyId: string) {
    const baseBody = await this.avatarRepo.findBaseBodyById(baseBodyId);
    if (!baseBody || baseBody.avatarId !== avatarId) {
      throw new APIException(
        'Base body not found',
        'BASE_BODY_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const result = await this.avatarRepo.deleteBaseBody(baseBodyId);
    if (!result) {
      throw new APIException(
        'Failed to delete base body',
        'BASE_BODY_DELETE_FAILED',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return this.helper.serviceResponse.successResponse({
      message: 'Base body deleted successfully',
    });
  }

  // Emotions CRUD Service Methods
  async getEmotions(avatarId: string) {
    // Validate avatar exists
    const avatar = await this.avatarRepo.findAvatarById(avatarId);
    if (!avatar) {
      throw new APIException(
        'Avatar not found',
        'AVATAR_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const emotions = await this.avatarRepo.findEmotionsByAvatarId(avatarId);

    return this.helper.serviceResponse.successResponse(emotions);
  }

  async updateEmotion(
    avatarId: string,
    emotionId: string,
    updateEmotionDto: CreateEmotionDto,
  ) {
    // Validate avatar exists
    const avatar = await this.avatarRepo.findAvatarById(avatarId);
    if (!avatar) {
      throw new APIException(
        'Avatar not found',
        'AVATAR_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    // Validate emotion exists and belongs to the avatar
    const existingEmotion = await this.avatarRepo.findEmotionById(emotionId);
    if (!existingEmotion) {
      throw new APIException(
        'Emotion not found',
        'EMOTION_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    if (existingEmotion.avatarId !== avatarId) {
      throw new APIException(
        'Emotion does not belong to this avatar',
        'EMOTION_MISMATCH',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Check for duplicate emotion type if updating emotion type
    if (
      updateEmotionDto.emotion &&
      updateEmotionDto.emotion !== existingEmotion.emotion
    ) {
      const isDuplicate = await this.avatarRepo.checkEmotionExists(
        avatarId,
        updateEmotionDto.emotion,
      );
      if (isDuplicate) {
        throw new APIException(
          'Emotion type already exists for this avatar',
          'DUPLICATE_EMOTION_TYPE',
          HttpStatus.CONFLICT,
        );
      }
    }

    const updatedEmotion = await this.avatarRepo.updateEmotion(
      emotionId,
      updateEmotionDto,
    );

    if (!updatedEmotion) {
      throw new APIException(
        'Failed to update emotion',
        'EMOTION_UPDATE_FAILED',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const responseDto = deepCasting(CreateEmotionResponseDto, updatedEmotion);
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async deleteEmotion(avatarId: string, emotionId: string) {
    // Validate avatar exists
    const avatar = await this.avatarRepo.findAvatarById(avatarId);
    if (!avatar) {
      throw new APIException(
        'Avatar not found',
        'AVATAR_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    // Validate emotion exists and belongs to the avatar
    const existingEmotion = await this.avatarRepo.findEmotionById(emotionId);
    if (!existingEmotion) {
      throw new APIException(
        'Emotion not found',
        'EMOTION_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    if (existingEmotion.avatarId !== avatarId) {
      throw new APIException(
        'Emotion does not belong to this avatar',
        'EMOTION_MISMATCH',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Check if emotion has associated assets before deletion
    const hasAssets = await this.avatarRepo.findEmotionAssetsByEmotionId(
      emotionId,
    );
    if (hasAssets && hasAssets.length > 0) {
      throw new APIException(
        'Cannot delete emotion with associated assets. Delete assets first.',
        'EMOTION_HAS_ASSETS',
        HttpStatus.CONFLICT,
      );
    }

    const deletedResponse = await this.avatarRepo.deleteEmotion(emotionId);
    if (!deletedResponse) {
      throw new APIException(
        'Failed to delete emotion',
        'EMOTION_DELETE_FAILED',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return this.helper.serviceResponse.successResponse({
      message: 'Emotion deleted successfully',
    });
  }

  async getEmotionAssets(avatarId: string) {
    // Validate avatar exists
    const avatar = await this.avatarRepo.findAvatarById(avatarId);
    if (!avatar) {
      throw new APIException(
        'Avatar not found',
        'AVATAR_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const emotionAssets = await this.avatarRepo.findEmotionAssetsByAvatarId(
      avatarId,
    );

    return this.helper.serviceResponse.successResponse({
      emotionAssets,
    });
  }

  async getEmotionAssetById(avatarId: string, assetId: string) {
    // Validate avatar exists
    const avatar = await this.avatarRepo.findAvatarById(avatarId);
    if (!avatar) {
      throw new APIException(
        'Avatar not found',
        'AVATAR_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    // Find the emotion asset
    const emotionAsset = await this.avatarRepo.findEmotionAssetById(assetId);
    if (!emotionAsset) {
      throw new APIException(
        'Emotion asset not found',
        'EMOTION_ASSET_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }
    if (emotionAsset.avatarId !== avatarId) {
      throw new APIException(
        'Emotion asset does not belong to this avatar',
        'EMOTION_ASSET_MISMATCH',
        HttpStatus.BAD_REQUEST,
      );
    }

    const responseDto = deepCasting(
      CreateEmotionAssetResponseDto,
      emotionAsset,
    );
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async updateEmotionAsset(
    avatarId: string,
    assetId: string,
    updateEmotionAssetDto: CreateEmotionAssetDto,
  ) {
    // Validate avatar exists
    const avatar = await this.avatarRepo.findAvatarById(avatarId);
    if (!avatar) {
      throw new APIException(
        'Avatar not found',
        'AVATAR_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    // Validate emotion asset exists and belongs to the avatar
    const existingAsset = await this.avatarRepo.findEmotionAssetById(assetId);
    if (!existingAsset) {
      throw new APIException(
        'Emotion asset not found',
        'EMOTION_ASSET_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    if (existingAsset.avatarId !== avatarId) {
      throw new APIException(
        'Emotion asset does not belong to this avatar',
        'EMOTION_ASSET_MISMATCH',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Validate emotion exists if emotionId is being updated
    if (
      updateEmotionAssetDto.emotion &&
      updateEmotionAssetDto.emotion !== existingAsset.emotion
    ) {
      const emotion = await this.avatarRepo.findEmotionById(
        updateEmotionAssetDto.emotion,
      );
      if (!emotion) {
        throw new APIException(
          'Emotion not found',
          'EMOTION_NOT_FOUND',
          HttpStatus.NOT_FOUND,
        );
      }

      if (emotion.avatarId !== avatarId) {
        throw new APIException(
          'Emotion does not belong to this avatar',
          'EMOTION_MISMATCH',
          HttpStatus.BAD_REQUEST,
        );
      }
    }
    // if (updateEmotionAssetDto. &&
    //   updateEmotionAssetDto.assetType !== existingAsset.assetType) {
    // const isDuplicate = await this.avatarRepo.checkEmotionAssetExists(
    //   avatarId,
    //   updateEmotionAssetDto.emotionId || existingAsset.emotionId,
    //   updateEmotionAssetDto.assetType,
    // );

    // }

    const updatedAsset = await this.avatarRepo.updateEmotionAsset(
      assetId,
      updateEmotionAssetDto,
    );

    if (!updatedAsset) {
      throw new APIException(
        'Failed to update emotion asset',
        'EMOTION_ASSET_UPDATE_FAILED',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    const responseDto = deepCasting(
      CreateEmotionAssetResponseDto,
      updatedAsset,
    );
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async deleteEmotionAsset(avatarId: string, assetId: string) {
    // Validate avatar exists
    const avatar = await this.avatarRepo.findAvatarById(avatarId);
    if (!avatar) {
      throw new APIException(
        'Avatar not found',
        'AVATAR_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    // Validate emotion asset exists and belongs to the avatar
    const existingAsset = await this.avatarRepo.findEmotionAssetById(assetId);
    if (!existingAsset) {
      throw new APIException(
        'Emotion asset not found',
        'EMOTION_ASSET_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }
    if (existingAsset.avatarId !== avatarId) {
      throw new APIException(
        'Emotion asset does not belong to this avatar',
        'EMOTION_ASSET_MISMATCH',
        HttpStatus.BAD_REQUEST,
      );
    }

    const isDeleted = await this.avatarRepo.deleteEmotionAsset(assetId);
    if (!isDeleted) {
      throw new APIException(
        'Failed to delete emotion asset',
        'EMOTION_ASSET_DELETE_FAILED',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return this.helper.serviceResponse.successResponse({
      message: 'Emotion asset deleted successfully',
    });
  }
}
