import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { AccessoryType } from '../../common/const/enum';

export class CreateAccessoryDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty({ enum: AccessoryType })
  @IsEnum(AccessoryType)
  type: AccessoryType;

  @ApiProperty()
  @IsNumber()
  pointsRequired: number;

  @ApiProperty({ type: [String] })
  @IsArray()
  @IsString({ each: true })
  compatibleAvatars: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  assetPath2D?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  assetPath3D?: string;

  @ApiProperty()
  position: {
    '2d'?: string;
    '3d'?: string;
  };
}

// ...existing code...

export class CreateAccessoryResponseDto {
  @Expose()
  @ApiProperty({ description: 'Unique identifier for the accessory' })
  id: string;

  @Expose()
  @ApiProperty({ description: 'Name of the accessory' })
  name: string;

  @Expose()
  @ApiProperty({ description: 'Type of the accessory', enum: AccessoryType })
  type: AccessoryType;

  @Expose()
  @ApiProperty({ description: 'Points required to unlock this accessory' })
  pointsRequired: number;

  @Expose()
  @ApiProperty({
    description: 'Compatible avatars for this accessory',
    type: [String],
  })
  compatibleAvatars: string[];

  @Expose()
  @ApiProperty({ description: '2D asset path', required: false })
  assetPath2D?: string;

  @Expose()
  @ApiProperty({ description: '3D asset path', required: false })
  assetPath3D?: string;

  @Expose()
  @ApiProperty({ description: 'Position settings for 2D and 3D' })
  position: {
    '2d'?: string;
    '3d'?: string;
  };
}

export class CreateAccessorySuccessResponseDto {
  @Expose()
  @ApiProperty({ description: 'Details of the created accessory' })
  @IsObject()
  @IsNotEmpty()
  data: CreateAccessoryResponseDto;
}
