import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsObject, IsString } from 'class-validator';
import { AvatarType, BodyStatus } from '../../common/const/enum';

export class CreateBaseBodyDto {
  @ApiProperty({ enum: AvatarType })
  @IsEnum(AvatarType)
  type: AvatarType;

  @ApiProperty({ enum: BodyStatus })
  @IsEnum(BodyStatus)
  bodyStatus: BodyStatus;

  @ApiProperty()
  @IsString()
  assetPath: string;
}

export class CreateBaseBodyResponseDto {
  @Expose()
  @ApiProperty({ description: 'Unique identifier for the base body' })
  id: string;

  @Expose()
  @ApiProperty({ description: 'Type of the avatar', enum: AvatarType })
  type: AvatarType;

  @Expose()
  @ApiProperty({ description: 'Body status', enum: BodyStatus })
  bodyStatus: BodyStatus;

  @Expose()
  @ApiProperty({ description: 'Asset path for the base body' })
  assetPath: string;
}

export class CreateBaseBodySuccessResponseDto {
  @Expose()
  @ApiProperty({ description: 'Details of the created base body' })
  @IsObject()
  @IsNotEmpty()
  data: CreateBaseBodyResponseDto;
}
