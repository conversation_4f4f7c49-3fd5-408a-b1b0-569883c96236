import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsObject, IsString } from 'class-validator';
import { AvatarEmotion, AvatarType, BodyStatus } from '../../common/const/enum';

export class CreateEmotionAssetDto {
  @ApiProperty({ enum: AvatarType })
  @IsEnum(AvatarType)
  type: AvatarType;

  @ApiProperty({ enum: BodyStatus })
  @IsEnum(BodyStatus)
  bodyStatus: BodyStatus;

  @ApiProperty({ enum: AvatarEmotion })
  @IsEnum(AvatarEmotion)
  emotion: AvatarEmotion;

  @ApiProperty()
  @IsString()
  assetPath: string;
}

export class CreateEmotionAssetResponseDto {
  @Expose()
  @ApiProperty({ description: 'Unique identifier for the emotion asset' })
  id: string;

  @Expose()
  @ApiProperty({ description: 'Type of the avatar', enum: AvatarType })
  type: AvatarType;

  @Expose()
  @ApiProperty({ description: 'Body status', enum: BodyStatus })
  bodyStatus: BodyStatus;

  @Expose()
  @ApiProperty({ description: 'Avatar emotion type', enum: AvatarEmotion })
  emotion: AvatarEmotion;

  @Expose()
  @ApiProperty({ description: 'Asset path for the emotion asset' })
  assetPath: string;
}

export class CreateEmotionAssetSuccessResponseDto {
  @Expose()
  @ApiProperty({ description: 'Details of the created emotion asset' })
  @IsObject()
  @IsNotEmpty()
  data: CreateEmotionAssetResponseDto;
}
