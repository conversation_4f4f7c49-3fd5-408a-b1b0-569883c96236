import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsEnum, IsNotEmpty, IsObject } from 'class-validator';
import { AvatarEmotion } from '../../common/const/enum';

export class CreateEmotionDto {
  @ApiProperty({ enum: AvatarEmotion })
  @IsEnum(AvatarEmotion)
  emotion: AvatarEmotion;
}

export class CreateEmotionResponseDto {
  @Expose()
  @ApiProperty({ description: 'Unique identifier for the emotion' })
  id: string;

  @Expose()
  @ApiProperty({ description: 'Avatar emotion type', enum: AvatarEmotion })
  emotion: AvatarEmotion;
}

export class CreateEmotionSuccessResponseDto {
  @Expose()
  @ApiProperty({ description: 'Details of the created emotion' })
  @IsObject()
  @IsNotEmpty()
  data: CreateEmotionResponseDto;
}
