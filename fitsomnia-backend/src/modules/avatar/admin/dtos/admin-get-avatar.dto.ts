import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsString,
} from 'class-validator';
import { ServiceSuccessResponse } from 'src/helper/serviceResponse/service.response.interface';
import {
  AvatarEmotion,
  AvatarGender,
  AvatarStyle,
  AvatarType,
  BodyStatus,
} from '../../common/const/enum';

export class AvatarDetailsDto {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @Expose()
  @ApiProperty({ required: true, enum: AvatarGender })
  @IsEnum(AvatarGender)
  @IsNotEmpty()
  gender: AvatarGender;

  @Expose()
  @ApiProperty({ required: true, enum: AvatarStyle })
  @IsEnum(AvatarStyle)
  @IsNotEmpty()
  style: AvatarStyle;

  @Expose()
  @ApiProperty({ required: true, enum: AvatarType })
  @IsEnum(AvatarType)
  @IsNotEmpty()
  type: AvatarType;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  thumbnail: string;

  @Expose()
  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsDateString()
  @IsNotEmpty()
  createdAt: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsDateString()
  @IsNotEmpty()
  updatedAt: string;
}

export class ThresholdDetailsDto {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  avatarId: string;

  @Expose()
  @ApiProperty({ required: true, enum: BodyStatus })
  @IsEnum(BodyStatus)
  @IsNotEmpty()
  bodyStatus: BodyStatus;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsNumber()
  @IsNotEmpty()
  min: number;

  @Expose()
  @ApiProperty({ required: true, type: Number })
  @IsNumber()
  @IsNotEmpty()
  max: number;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsDateString()
  @IsNotEmpty()
  createdAt: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsDateString()
  @IsNotEmpty()
  updatedAt: string;
}

export class BaseBodyDetailsDto {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  avatarId: string;

  @Expose()
  @ApiProperty({ required: true, enum: AvatarType })
  @IsEnum(AvatarType)
  @IsNotEmpty()
  type: AvatarType;

  @Expose()
  @ApiProperty({ required: true, enum: BodyStatus })
  @IsEnum(BodyStatus)
  @IsNotEmpty()
  bodyStatus: BodyStatus;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  assetPath: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsDateString()
  @IsNotEmpty()
  createdAt: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsDateString()
  @IsNotEmpty()
  updatedAt: string;
}

export class EmotionDetailsDto {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  avatarId: string;

  @Expose()
  @ApiProperty({ required: true, enum: AvatarEmotion })
  @IsEnum(AvatarEmotion)
  @IsNotEmpty()
  emotion: AvatarEmotion;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsDateString()
  @IsNotEmpty()
  createdAt: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsDateString()
  @IsNotEmpty()
  updatedAt: string;
}

export class EmotionAssetDetailsDto {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  avatarId: string;

  @Expose()
  @ApiProperty({ required: true, enum: AvatarType })
  @IsEnum(AvatarType)
  @IsNotEmpty()
  type: AvatarType;

  @Expose()
  @ApiProperty({ required: true, enum: BodyStatus })
  @IsEnum(BodyStatus)
  @IsNotEmpty()
  bodyStatus: BodyStatus;

  @Expose()
  @ApiProperty({ required: true, enum: AvatarEmotion })
  @IsEnum(AvatarEmotion)
  @IsNotEmpty()
  emotion: AvatarEmotion;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  assetPath: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsDateString()
  @IsNotEmpty()
  createdAt: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsDateString()
  @IsNotEmpty()
  updatedAt: string;
}

export class GetAvatarDetailsResponseDto {
  @Expose()
  @ApiProperty({ required: true, type: AvatarDetailsDto })
  @IsObject()
  @IsNotEmpty()
  avatar: AvatarDetailsDto;

  @Expose()
  @ApiProperty({ required: true, type: [ThresholdDetailsDto] })
  @IsArray()
  thresholds: ThresholdDetailsDto[];

  @Expose()
  @ApiProperty({ required: true, type: [BaseBodyDetailsDto] })
  @IsArray()
  baseBodies: BaseBodyDetailsDto[];

  @Expose()
  @ApiProperty({ required: true, type: [EmotionDetailsDto] })
  @IsArray()
  emotions: EmotionDetailsDto[];

  @Expose()
  @ApiProperty({ required: true, type: [EmotionAssetDetailsDto] })
  @IsArray()
  emotionAssets: EmotionAssetDetailsDto[];
}

export class GetAvatarDetailsSuccessResponseDto
  implements ServiceSuccessResponse
{
  @Expose()
  @ApiProperty({ required: true, type: GetAvatarDetailsResponseDto })
  @IsObject()
  @IsNotEmpty()
  data: GetAvatarDetailsResponseDto;
}
