import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Patch,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  OnlyAuthGuard,
  OnlyRoleGuard,
  Role,
  Roles,
} from 'src/authentication/guards/auth-role.guard';
import { GetAvatarDetailsSuccessResponseDto } from '../dtos/admin-get-avatar.dto';
import { GetAllAvatarsSuccessResponseDto } from '../dtos/admin-get-avatars.dto';
import {
  CreateAccessoryDto,
  CreateAccessorySuccessResponseDto,
} from '../dtos/create-accessory.dto';
import {
  CreateAvatarDto,
  CreateAvatarSuccessResponseDto,
} from '../dtos/create-avatar.dto';
import {
  CreateBaseBodyDto,
  CreateBaseBodySuccessResponseDto,
} from '../dtos/create-base-body.dto';
import {
  CreateEmotionAssetDto,
  CreateEmotionAssetSuccessResponseDto,
} from '../dtos/create-emotion-asset.dto';
import {
  CreateEmotionDto,
  CreateEmotionSuccessResponseDto,
} from '../dtos/create-emotion.dto';
import { AdminAvatarService } from '../services/avatar.service';

@Controller('admin/avatars')
@ApiBearerAuth()
@UseGuards(OnlyAuthGuard, OnlyRoleGuard)
@Roles(Role.Admin)
@ApiTags('Avatar API - Admin')
export class AdminAvatarController {
  constructor(private readonly adminAvatarService: AdminAvatarService) {}

  @Post()
  @ApiOperation({ summary: 'Create new avatar' })
  @ApiResponse({
    description: 'Returns a single avatar request data',
    type: CreateAvatarSuccessResponseDto,
    status: HttpStatus.OK,
  })
  async createAvatar(@Body() createAvatarDto: CreateAvatarDto) {
    return await this.adminAvatarService.createAvatar(createAvatarDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all avatars' })
  @ApiResponse({
    description: 'Returns all avatars',
    type: GetAllAvatarsSuccessResponseDto,
    status: HttpStatus.OK,
  })
  async getAllAvatars() {
    return await this.adminAvatarService.getAllAvatars();
  }

  @Get(':avatarId')
  @ApiOperation({ summary: 'Get avatar details' })
  @ApiResponse({
    description: 'Returns the details of a single avatar',
    type: GetAvatarDetailsSuccessResponseDto,
    status: HttpStatus.OK,
  })
  async getAvatarDetails(@Param('avatarId') avatarId: string) {
    return await this.adminAvatarService.getAvatarDetails(avatarId);
  }

  // @Post(':avatarId/thresholds')
  // @ApiOperation({ summary: 'Add weight threshold for avatar' })
  // @ApiResponse({
  //   description: 'Returns the created weight threshold',
  //   type: CreateWeightThresholdSuccessResponseDto,
  //   status: HttpStatus.CREATED,
  // })
  // async addWeightThreshold(
  //   @Param('avatarId') avatarId: string,
  //   @Body() createThresholdDto: CreateWeightThresholdDto,
  // ) {
  //   return await this.adminAvatarService.addWeightThreshold(
  //     avatarId,
  //     createThresholdDto,
  //   );
  // }

  @Post(':avatarId/base-bodies')
  @ApiOperation({ summary: 'Add base body for avatar' })
  @ApiResponse({
    description: 'Returns the created base body',
    type: CreateBaseBodySuccessResponseDto,
    status: HttpStatus.CREATED,
  })
  async addBaseBody(
    @Param('avatarId') avatarId: string,
    @Body() createBaseBodyDto: CreateBaseBodyDto,
  ) {
    return await this.adminAvatarService.addBaseBody(
      avatarId,
      createBaseBodyDto,
    );
  }

  @Get(':avatarId/base-bodies')
  @ApiOperation({ summary: 'Get all base bodies for avatar' })
  async getBaseBodies(@Param('avatarId') avatarId: string) {
    return await this.adminAvatarService.getBaseBodies(avatarId);
  }
  @Patch(':avatarId/base-bodies/:baseBodyId')
  @ApiOperation({ summary: 'Update base body' })
  async updateBaseBody(
    @Param('avatarId') avatarId: string,
    @Param('baseBodyId') baseBodyId: string,
    @Body() updateBaseBodyDto: CreateBaseBodyDto,
  ) {
    return await this.adminAvatarService.updateBaseBody(
      avatarId,
      baseBodyId,
      updateBaseBodyDto,
    );
  }

  @Delete(':avatarId/base-bodies/:baseBodyId')
  @ApiOperation({ summary: 'Delete base body' })
  async deleteBaseBody(
    @Param('avatarId') avatarId: string,
    @Param('baseBodyId') baseBodyId: string,
  ) {
    return await this.adminAvatarService.deleteBaseBody(avatarId, baseBodyId);
  }

  @Post(':avatarId/emotions')
  @ApiOperation({ summary: 'Add emotion for avatar' })
  @ApiResponse({
    description: 'Returns the created emotion',
    type: CreateEmotionSuccessResponseDto,
    status: HttpStatus.CREATED,
  })
  async addEmotion(
    @Param('avatarId') avatarId: string,
    @Body() createEmotionDto: CreateEmotionDto,
  ) {
    return await this.adminAvatarService.addEmotion(avatarId, createEmotionDto);
  }
  // ...existing code...

  @Get(':avatarId/emotions')
  @ApiOperation({ summary: 'Get all emotions for avatar' })
  @ApiResponse({
    description: 'Returns all emotions for the avatar',
    status: HttpStatus.OK,
  })
  async getEmotions(@Param('avatarId') avatarId: string) {
    return await this.adminAvatarService.getEmotions(avatarId);
  }

  @Patch(':avatarId/emotions/:emotionId')
  @ApiOperation({ summary: 'Update emotion' })
  @ApiResponse({
    description: 'Returns the updated emotion',
    type: CreateEmotionSuccessResponseDto,
    status: HttpStatus.OK,
  })
  async updateEmotion(
    @Param('avatarId') avatarId: string,
    @Param('emotionId') emotionId: string,
    @Body() updateEmotionDto: CreateEmotionDto,
  ) {
    return await this.adminAvatarService.updateEmotion(
      avatarId,
      emotionId,
      updateEmotionDto,
    );
  }

  @Delete(':avatarId/emotions/:emotionId')
  @ApiOperation({ summary: 'Delete emotion' })
  @ApiResponse({
    description: 'Returns deletion confirmation',
    status: HttpStatus.OK,
  })
  async deleteEmotion(
    @Param('avatarId') avatarId: string,
    @Param('emotionId') emotionId: string,
  ) {
    return await this.adminAvatarService.deleteEmotion(avatarId, emotionId);
  }

  // ...existing code...

  @Post(':avatarId/emotion-assets')
  @ApiOperation({ summary: 'Add emotion asset for avatar' })
  @ApiResponse({
    description: 'Returns the created emotion asset',
    type: CreateEmotionAssetSuccessResponseDto,
    status: HttpStatus.CREATED,
  })
  async addAsset(
    @Param('avatarId') avatarId: string,
    @Body() createEmotionAssetDto: CreateEmotionAssetDto,
  ) {
    return await this.adminAvatarService.addEmotionAsset(
      avatarId,
      createEmotionAssetDto,
    );
  }

  @Get(':avatarId/emotion-assets')
  @ApiOperation({ summary: 'Get all emotion assets for avatar' })
  @ApiResponse({
    description: 'Returns all emotion assets for the avatar',
    status: HttpStatus.OK,
  })
  async getEmotionAssets(@Param('avatarId') avatarId: string) {
    return await this.adminAvatarService.getEmotionAssets(avatarId);
  }

  @Get(':avatarId/emotion-assets/:assetId')
  @ApiOperation({ summary: 'Get emotion asset by ID' })
  @ApiResponse({
    description: 'Returns the emotion asset details',
    type: CreateEmotionAssetSuccessResponseDto,
    status: HttpStatus.OK,
  })
  async getEmotionAssetById(
    @Param('avatarId') avatarId: string,
    @Param('assetId') assetId: string,
  ) {
    return await this.adminAvatarService.getEmotionAssetById(avatarId, assetId);
  }

  @Patch(':avatarId/emotion-assets/:assetId')
  @ApiOperation({ summary: 'Update emotion asset' })
  @ApiResponse({
    description: 'Returns the updated emotion asset',
    type: CreateEmotionAssetSuccessResponseDto,
    status: HttpStatus.OK,
  })
  async updateEmotionAsset(
    @Param('avatarId') avatarId: string,
    @Param('assetId') assetId: string,
    @Body() updateEmotionAssetDto: CreateEmotionAssetDto,
  ) {
    return await this.adminAvatarService.updateEmotionAsset(
      avatarId,
      assetId,
      updateEmotionAssetDto,
    );
  }

  @Delete(':avatarId/emotion-assets/:assetId')
  @ApiOperation({ summary: 'Delete emotion asset' })
  @ApiResponse({
    description: 'Returns deletion confirmation',
    status: HttpStatus.OK,
  })
  async deleteEmotionAsset(
    @Param('avatarId') avatarId: string,
    @Param('assetId') assetId: string,
  ) {
    return await this.adminAvatarService.deleteEmotionAsset(avatarId, assetId);
  }

  @Post('accessories')
  @ApiOperation({ summary: 'Create accessory' })
  @ApiResponse({
    description: 'Returns the created accessory',
    type: CreateAccessorySuccessResponseDto,
    status: HttpStatus.CREATED,
  })
  async createAccessory(@Body() createAccessoryDto: CreateAccessoryDto) {
    return await this.adminAvatarService.createAccessory(createAccessoryDto);
  }
}
