import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';

import { AvatarType, BodyStatus } from '../const/enum';
import { AvatarBaseBody } from '../entities/avatar-base-body';

const AvatarBaseBodySchema = new Schema<AvatarBaseBody>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    avatarId: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      enum: AvatarType,
      required: true,
    },
    bodyStatus: {
      type: String,
      enum: BodyStatus,
      required: true,
    },
    assetPath: {
      type: String,
      required: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

AvatarBaseBodySchema.index(
  { avatarId: 1, type: 1, bodyStatus: 1 },
  { unique: true },
);

export const AvatarBaseBodyModel = model<AvatarBaseBody>(
  'avatar-base-body',
  AvatarBaseBodySchema,
);
