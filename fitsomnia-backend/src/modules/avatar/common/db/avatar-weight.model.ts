import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { BodyStatus } from '../const/enum';
import { AvatarWeightThreshold } from '../entities/avatar-weight';

const AvatarWeightThresholdSchema = new Schema<AvatarWeightThreshold>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    avatarId: {
      type: String,
      required: true,
    },
    bodyStatus: {
      type: String,
      enum: BodyStatus,
      required: true,
    },
    min: {
      type: Number,
      required: true,
    },
    max: {
      type: Number,
      required: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

AvatarWeightThresholdSchema.index(
  { avatarId: 1, bodyStatus: 1 },
  { unique: true },
);

export const AvatarWeightThresholdModel = model<AvatarWeightThreshold>(
  'avatar-weight-threshold',
  AvatarWeightThresholdSchema,
);
