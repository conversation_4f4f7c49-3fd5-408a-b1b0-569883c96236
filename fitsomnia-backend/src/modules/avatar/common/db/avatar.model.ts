import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import { AvatarGender, AvatarStyle, AvatarType } from '../const/enum';
import { Avatar } from '../entities/avatar';

const AvatarSchema = new Schema<Avatar>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    name: {
      type: String,
      required: true,
    },
    gender: {
      type: String,
      enum: AvatarGender,
      required: true,
    },
    style: {
      type: String,
      enum: AvatarStyle,
      required: true,
    },
    type: {
      type: String,
      enum: AvatarType,
      required: true,
    },
    thumbnail: {
      type: String,
      required: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

export const AvatarModel = model<Avatar>('avatar', AvatarSchema);
