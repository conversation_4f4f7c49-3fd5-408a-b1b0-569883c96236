import { randomUUID } from 'crypto';
import { model, Schema } from 'mongoose';
import {
  AvatarEmotion,
  AvatarStyle,
  AvatarType,
  BodyStatus,
} from '../const/enum';
import { UserAvatarState } from '../entities/user-avatar';

const UserAvatarStateSchema = new Schema<UserAvatarState>(
  {
    id: {
      type: String,
      default: () => randomUUID(),
      unique: true,
    },
    userId: {
      type: String,
      required: true,
      unique: true,
    },
    avatarId: {
      type: String,
      required: true,
    },
    avatarStyle: {
      type: String,
      enum: AvatarStyle,
      required: true,
    },
    avatarType: {
      type: String,
      enum: AvatarType,
      required: true,
    },
    emotion: {
      type: String,
      enum: AvatarEmotion,
      required: true,
    },
    bodyStatus: {
      type: String,
      enum: BodyStatus,
      required: true,
    },
    accessories: [
      {
        type: String,
      },
    ],
    lastUpdated: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  },
);

export const UserAvatarStateModel = model<UserAvatarState>(
  'user-avatar-state',
  UserAvatarStateSchema,
);
