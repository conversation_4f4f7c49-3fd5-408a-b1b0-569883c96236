import {
  Body,
  Controller,
  Get,
  HttpStatus,
  Post,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  OnlyAuthGuard,
  OnlyRoleGuard,
  Role,
  Roles,
} from 'src/authentication/guards/auth-role.guard';
import { User as UserInfo } from 'src/decorators/auth.decorator';
import { User } from 'src/entity/user';
import {
  SelectAvatarDto,
  SelectAvatarSuccessResponseDto,
} from '../dtos/create-user-avatar.dto';
import { GetAvailableAvatarsSuccessResponseDto } from '../dtos/get-user-avatars.dto';
import { UserAvatarService } from '../services/user-avatar.service';

@Controller('user/avatar')
@ApiBearerAuth()
@UseGuards(OnlyAuthGuard, OnlyRoleGuard)
@Roles(Role.User)
@ApiTags('Avatar API - User')
export class UserAvatarController {
  constructor(private readonly userAvatarService: UserAvatarService) {}

  @Get('available')
  @ApiResponse({
    description: 'Returns the available avatars',
    type: GetAvailableAvatarsSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({ summary: 'Get available avatars for selection' })
  async getAvailableAvatars(@UserInfo() user: User) {
    return await this.userAvatarService.getAvailableAvatars(user.id);
  }

  @Post('select')
  @ApiOperation({ summary: 'Select avatar for user' })
  @ApiResponse({
    description: 'Returns the selected avatar state',
    type: SelectAvatarSuccessResponseDto,
    status: HttpStatus.OK,
  })
  async selectAvatar(
    @UserInfo() user: User,
    @Body() selectAvatarDto: SelectAvatarDto,
  ) {
    return await this.userAvatarService.selectAvatar(
      user.id,
      selectAvatarDto.avatarId,
    );
  }

  @Get('state')
  @ApiOperation({ summary: 'Get current user avatar state' })
  async getUserAvatarState(@UserInfo() user: User) {
    return await this.userAvatarService.getUserAvatarState(user.id);
  }

  // @Get('accessories')
  // @ApiOperation({ summary: 'Get available accessories for current avatar' })
  // async getAvailableAccessories(@UserInfo() user: User) {
  //   return await this.userAvatarService.getAvailableAccessories(user.id);
  // }

  // @Post('accessories/redeem')
  // @ApiOperation({ summary: 'Redeem accessory with points' })
  // async redeemAccessory(
  //   @UserInfo() user: User,
  //   @Body() redeemDto: RedeemAccessoryDto,
  // ) {
  //   return await this.userAvatarService.redeemAccessory(
  //     user.id,
  //     redeemDto.accessoryId,
  //   );
  // }

  // Additional endpoints that match your API specification
  // @Post('users/:userId/avatar/select')
  // @ApiOperation({ summary: 'Select avatar for specific user (admin use)' })
  // @ApiParam({ name: 'userId', type: 'string' })
  // async selectAvatarForUser(
  //   @Param('userId') userId: string,
  //   @Body() selectAvatarDto: SelectAvatarDto,
  // ) {
  //   return await this.userAvatarService.selectAvatar(
  //     userId,
  //     selectAvatarDto.avatarId,
  //   );
  // }

  // @Get('users/:userId/avatar-state')
  // @ApiOperation({ summary: 'Get avatar state for specific user (admin use)' })
  // @ApiParam({ name: 'userId', type: 'string' })
  // async getUserAvatarStateById(@Param('userId') userId: string) {
  //   return await this.userAvatarService.getUserAvatarState(userId);
  // }
}
