import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsString,
} from 'class-validator';
import { ServiceSuccessResponse } from 'src/helper/serviceResponse/service.response.interface';
import { AvatarGender, AvatarStyle, AvatarType } from '../../common/const/enum';

export class GetAvailableAvatarsResponseDto {
  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  id: string;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  name: string;

  @Expose()
  @ApiProperty({ required: true, enum: AvatarGender })
  @IsEnum(AvatarGender)
  @IsNotEmpty()
  gender: AvatarGender;

  @Expose()
  @ApiProperty({ required: true, enum: AvatarStyle })
  @IsEnum(AvatarStyle)
  @IsNotEmpty()
  style: AvatarStyle;

  @Expose()
  @ApiProperty({ required: true, enum: AvatarType })
  @IsEnum(AvatarType)
  @IsNotEmpty()
  type: AvatarType;

  @Expose()
  @ApiProperty({ required: true, type: String })
  @IsString()
  @IsNotEmpty()
  thumbnail: string;

  @Expose()
  @ApiProperty({ required: true, type: Boolean })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;
}

export class GetAvailableAvatarsSuccessResponseDto
  implements ServiceSuccessResponse
{
  @Expose()
  @ApiProperty({ required: true, type: [GetAvailableAvatarsResponseDto] })
  @IsArray()
  @IsNotEmpty()
  data: GetAvailableAvatarsResponseDto[];
}
