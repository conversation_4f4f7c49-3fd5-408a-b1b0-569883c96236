import { ApiProperty } from '@nestjs/swagger';
import { AvatarEmotion } from '../../common/const/enum';

export class UserAvatarStateDto {
  @ApiProperty({ description: 'User ID' })
  userId: string;

  @ApiProperty({ description: 'Current selected avatar ID' })
  currentAvatarId: string;

  @ApiProperty({ description: 'Avatar name' })
  avatarName: string;

  @ApiProperty({ description: 'Avatar asset path/URL' })
  avatarAssetPath: string;

  @ApiProperty({ description: 'User body status based on BMI' })
  bodyStatus: string;

  @ApiProperty({ description: 'Current weight in kg' })
  currentWeight: number;

  @ApiProperty({
    enum: AvatarEmotion,
    description: 'Current emotion based on diet history',
  })
  emotion: AvatarEmotion;

  @ApiProperty({ description: 'Emotion asset path/URL' })
  emotionAssetPath: string;

  @ApiProperty({ description: 'Reason for current emotion' })
  emotionReason: string;

  @ApiProperty({ description: 'Avatar state creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Avatar state last update date' })
  updatedAt: Date;
}

export class GetUserAvatarStateSuccessResponseDto {
  @ApiProperty({
    type: UserAvatarStateDto,
    description: 'User avatar state with diet-based emotion',
  })
  data: UserAvatarStateDto;

  @ApiProperty({ description: 'Success status' })
  success: boolean;

  @ApiProperty({ description: 'Response message' })
  message: string;
}
