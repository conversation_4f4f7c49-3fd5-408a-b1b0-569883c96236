import { Injectable } from '@nestjs/common';
import { AvatarType } from '../../common/const/enum';
import { AccessoryModel } from '../../common/db/accessory.model';
import { AvatarBaseBodyModel } from '../../common/db/avatar-base-body.model';
import { AvatarEmotionAssetModel } from '../../common/db/avatar-emotion-asset.model';
import { AvatarWeightThresholdModel } from '../../common/db/avatar-weight.model';
import { AvatarModel } from '../../common/db/avatar.model';
import { UserAvatarStateModel } from '../../common/db/user-avatar.model';
import { Accessory } from '../../common/entities/accessory';
import { Avatar } from '../../common/entities/avatar';
import { AvatarBaseBody } from '../../common/entities/avatar-base-body';
import { AvatarEmotionAsset } from '../../common/entities/avatar-emotion-asset';
import { AvatarWeightThreshold } from '../../common/entities/avatar-weight';
import { UserAvatarState } from '../../common/entities/user-avatar';

@Injectable()
export class UserAvatarRepository {
  // Avatar Methods
  async findAllActiveAvatars(): Promise<Avatar[]> {
    try {
      return await AvatarModel.find({ isActive: true }).lean();
    } catch (error) {
      console.log(error.message);
      return [];
    }
  }

  async findAvatarById(avatarId: string): Promise<Avatar | null> {
    try {
      return await AvatarModel.findOne({ id: avatarId }).lean();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  // User Avatar State Methods
  async findUserAvatarState(userId: string): Promise<UserAvatarState | null> {
    try {
      return await UserAvatarStateModel.findOne({ userId }).lean();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async createUserAvatarState(
    avatarState: UserAvatarState,
  ): Promise<UserAvatarState | null> {
    try {
      const state = await UserAvatarStateModel.create(avatarState);
      return state?.toObject();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async updateUserAvatarState(
    userId: string,
    updates: Partial<UserAvatarState>,
  ): Promise<UserAvatarState | null> {
    try {
      const updatedState = await UserAvatarStateModel.findOneAndUpdate(
        { userId },
        { ...updates, lastUpdated: new Date() },
        { returnDocument: 'after' },
      );
      return updatedState?.toObject();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  // Accessory Methods
  async findAllActiveAccessories(): Promise<Accessory[]> {
    try {
      return await AccessoryModel.find({ isActive: true }).lean();
    } catch (error) {
      console.log(error.message);
      return [];
    }
  }

  async findAccessoryById(accessoryId: string): Promise<Accessory | null> {
    try {
      return await AccessoryModel.findOne({
        id: accessoryId,
        isActive: true,
      }).lean();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  // Weight Threshold Methods
  async findWeightThresholdsByAvatarId(
    avatarId: string,
  ): Promise<AvatarWeightThreshold[]> {
    try {
      return await AvatarWeightThresholdModel.find({ avatarId }).lean();
    } catch (error) {
      console.log(error.message);
      return [];
    }
  }

  // Avatar Asset Methods
  async findEmotionAssetByConditions(
    avatarId: string,
    type: AvatarType,
    bodyStatus: string,
    emotion: string,
  ): Promise<AvatarEmotionAsset | null> {
    try {
      return await AvatarEmotionAssetModel.findOne({
        avatarId,
        type,
        bodyStatus,
        emotion,
      }).lean();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  // Helper method for calculating body status from weight

  async findBaseBodyByAvatarIdAndBodyStatus(
    avatarId: string,
    bodyStatus: string,
    avatarType: AvatarType,
  ): Promise<AvatarBaseBody | null> {
    try {
      return await AvatarBaseBodyModel.findOne({
        avatarId,
        bodyStatus,
        type: avatarType,
      }).lean();
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }
}
