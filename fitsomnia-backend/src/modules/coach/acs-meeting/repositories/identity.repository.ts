import { Injectable } from "@nestjs/common";
import { ACSIdentityModel } from "src/database/acs-identity/indentity.model";
import { ACSIdentity } from "../entities/identity.entity";

@Injectable()
export class IdentityRepository {

  async createACSIdentity(info: ACSIdentity): Promise<ACSIdentity> {
    const identity = new ACSIdentityModel(info);
    const identityDocument = await identity.save();
    return identityDocument.toObject();
  }

  async getACSIdentity(userId: string): Promise<ACSIdentity | null> {
    const identityDoc = await ACSIdentityModel.findOne({ userId }).exec();
    return identityDoc !== null ? identityDoc.toObject() : null;
  }

}