import { CommunicationIdentityClient, GetTokenOptions } from '@azure/communication-identity';
import { Injectable, Logger } from '@nestjs/common';
import { azureConfig } from 'config/azure';
import {
  CreateAccessTokenRequestDto,
  CreateAccessTokenResponseDto,
  CreateIdentityAndTokenResponseDto,
  MeetingScope
} from '../dtos/meeting.dtos';
import { IdentityRepository } from '../repositories/identity.repository';

@Injectable()
export class AcsService {
  private readonly logger = new Logger(AcsService.name);
  private readonly identityClient: CommunicationIdentityClient;

  constructor(private readonly identityRepository: IdentityRepository) {
    if (!azureConfig.acs_connection_string) {
      throw new Error('ACS_CONNECTION_STRING is not configured');
    }
    
    this.identityClient = new CommunicationIdentityClient(azureConfig.acs_connection_string);
    this.logger.log('Azure Communication Services client initialized');
  }

  /**
   * Issue an access token for an existing identity
   */
  async createAccessToken(request: CreateAccessTokenRequestDto): Promise<CreateAccessTokenResponseDto> {
    try {
      // Create identity object from userId
      const identity = { communicationUserId: request.communicationUserId };
      
      // Prepare token options
      const tokenOptions: GetTokenOptions = {};
      if (azureConfig.acs_token_expire_time) {
        tokenOptions.tokenExpiresInMinutes = azureConfig.acs_token_expire_time;
      }

      // Issue the token
      const tokenResponse = await this.identityClient.getToken(
        identity, 
        [MeetingScope.VOIP], 
        tokenOptions
      );

      this.logger.log(`Issued access token for user: ${request.communicationUserId}`);

      return {
        token: tokenResponse.token,
        expiresOn: tokenResponse.expiresOn,
        communicationUserId: request.communicationUserId,
      };
    } catch (error) {
      this.logger.error(`Failed to create access token for user: ${request.communicationUserId}`, error);
      throw new Error('Failed to create access token');
    }
  }

  /**
   * Create identity and issue access token in one operation
   */
  async createIdentityAndToken(userId: string): Promise<CreateIdentityAndTokenResponseDto> {
    try {
      // Prepare token options
      const tokenOptions: GetTokenOptions = {};
      if (azureConfig.acs_token_expire_time) {
        tokenOptions.tokenExpiresInMinutes = azureConfig.acs_token_expire_time;
      }

      let identity = await this.identityRepository.getACSIdentity(userId);

      if  (!identity) {
        identity = await this.identityClient.createUser();
        this.identityRepository.createACSIdentity({
          userId,
          communicationUserId: identity.communicationUserId,
        });
      };
      const identityTokenResponse = await this.identityClient.getToken(
        {communicationUserId: identity.communicationUserId}, 
        [MeetingScope.VOIP], 
        tokenOptions
      );
      this.logger.log(`Created identity and token: ${identity.communicationUserId}`);

      return {
        token: identityTokenResponse.token,
        expiresOn: identityTokenResponse.expiresOn,
        communicationUserId: identity.communicationUserId,
      };
    } catch (error) {
      console.log('[createIdentityAndToken] catch error:', error);
      this.logger.error('Failed to create identity and token', error);
      throw new Error('Failed to create identity and access token');
    }
  }

  /**
   * Revoke access tokens for a user
   */
  async revokeAccessTokens(communicationUserId: string): Promise<void> {
    try {
      const identity = { communicationUserId };
      await this.identityClient.revokeTokens(identity);
      
      this.logger.log(`Revoked access tokens for user: ${communicationUserId}`);
    } catch (error) {
      this.logger.error(`Failed to revoke tokens for user: ${communicationUserId}`, error);
      throw new Error('Failed to revoke access tokens');
    }
  }

  /**
   * Delete a Communication Services identity
   */
  async deleteIdentity(communicationUserId: string): Promise<void> {
    try {
      const identity = { communicationUserId };
      await this.identityClient.deleteUser(identity);
      
      this.logger.log(`Deleted identity: ${communicationUserId}`);
    } catch (error) {
      this.logger.error(`Failed to delete identity: ${communicationUserId}`, error);
      throw new Error('Failed to delete identity');
    }
  }
}
