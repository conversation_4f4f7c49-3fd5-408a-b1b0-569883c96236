import { Body, Controller, HttpStatus, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { OnlyAuthGuard, OnlyRoleGuard, Role, Roles } from 'src/authentication/guards/auth-role.guard';
import { User as UserInfo } from 'src/decorators/auth.decorator';
import { User } from 'src/entity/user';
import {
  CreateAccessTokenRequestDto,
  CreateAccessTokenResponseDto,
  CreateIdentityAndTokenResponseDto,
} from '../dtos/meeting.dtos';
import { AcsService } from '../services/meeting.service';

@ApiTags('Coach Meeting API')
@ApiBearerAuth()
@Controller('coach/meeting')
export class MeetingController {
  constructor(private readonly acsService: AcsService) {}

  @Post('access-token')
  @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
  @Roles(Role.User)
  @ApiOperation({ summary: 'Issue an access token for an existing identity' })
  @ApiBody({
    description: 'Access token creation request',
    type: CreateAccessTokenRequestDto,
  })
  @ApiResponse({
    description: 'Returns the access token for Communication Services',
    type: CreateAccessTokenResponseDto,
    status: HttpStatus.CREATED,
  })
  async createAccessToken(
    @Body() request: CreateAccessTokenRequestDto,
  ): Promise<CreateAccessTokenResponseDto> {
    return await this.acsService.createAccessToken(request);
  }

  @Post('identity-and-token')
  @UseGuards(OnlyAuthGuard, OnlyRoleGuard)
  @Roles(Role.User)
  @ApiOperation({ summary: 'Create identity and issue access token in one operation' })
  @ApiResponse({
    description: 'Returns the identity and access token for Communication Services',
    type: CreateIdentityAndTokenResponseDto,
    status: HttpStatus.CREATED,
  })
  async createIdentityAndToken(
    @UserInfo() user: User,
  ): Promise<CreateIdentityAndTokenResponseDto> {
    return await this.acsService.createIdentityAndToken(user.id);
  }
}