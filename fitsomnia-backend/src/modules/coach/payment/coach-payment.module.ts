import { forwardRef, Module } from '@nestjs/common';
import { AccountingModule } from 'src/modules/accounting/accounting.rest.module';
import { BkashRestModule } from 'src/modules/bkash/bkash.rest.module';
import { CoachIncomeProfileModule } from '../income-profile/coach-income-profile.module';
import { CoachProgramModule } from '../program/coach-program.module';
import { CoachSubscriptionModule } from '../subscription/coach-subscription.module';
import { CoachSubscriptionPaymentControllerForUser } from './controllers/user.controller';
import { CoachPaymentProviderForInternal } from './providers/internal.provider';
import { UserPaymentProviderForUser } from './providers/user.provider';
import { CoachSubscriptionPaymentRepositoryForInternal } from './repositories/internal.repository';
import { CoachSubscriptionPaymentRepositoryForUser } from './repositories/user.repository';
import { CoachSubscriptionPaymentServiceForUser } from './services/user.service';

@Module({
  controllers: [CoachSubscriptionPaymentControllerForUser],
  providers: [
    CoachSubscriptionPaymentServiceForUser,
    CoachSubscriptionPaymentRepositoryForUser,
    CoachPaymentProviderForInternal,
    CoachSubscriptionPaymentRepositoryForInternal,
    UserPaymentProviderForUser,
  ],
  imports: [
    forwardRef(() => CoachIncomeProfileModule),
    forwardRef(() => CoachSubscriptionModule),
    forwardRef(() => CoachProgramModule),
    forwardRef(() => AccountingModule),
    forwardRef(() => BkashRestModule),
  ],
  exports: [CoachPaymentProviderForInternal, UserPaymentProviderForUser],
})
export class CoachPaymentModule {}
