import { Module } from '@nestjs/common';
import { NotificationModule } from '../notification/notification.module';
import { MeetingModule } from './acs-meeting/meeting.module';
import { CoachCategoryModule } from './category/category.module';
import { CoachCronJobModule } from './cron-job/coach-cron-job.module';
import { CoachIncomeProfileModule } from './income-profile/coach-income-profile.module';
import { CoachPaymentModule } from './payment/coach-payment.module';
import { PendingProfileModule } from './pending-profile/pending-profile.module';
import { CoachProfileRatingModule } from './profile-rating/profile-rating.module';
import { CoachProgramRatingModule } from './program-rating/program-rating.module';
import { CoachProgramModule } from './program/coach-program.module';
import { CoachRefundModule } from './refund/refund.module';
import { CoachSubCategoryModule } from './sub-category/coach-sub-category.module';
import { CoachSubscriptionModule } from './subscription/coach-subscription.module';
import { UserInfoModule } from './user-info/user-info.module';
import { VerifiedProfileModule } from './verified-profile/verified-profile.module';
import { CoachWithdrawModule } from './withdraw/withdraw.module';

@Module({
  imports: [
    PendingProfileModule,
    VerifiedProfileModule,
    CoachIncomeProfileModule,
    CoachCategoryModule,
    CoachSubCategoryModule,
    CoachProfileRatingModule,
    CoachProgramRatingModule,
    CoachProgramModule,
    CoachSubscriptionModule,
    CoachPaymentModule,
    CoachRefundModule, 
    UserInfoModule,
    CoachCronJobModule,
    CoachWithdrawModule,
    NotificationModule,
    MeetingModule
  ],
})
export class CoachModule {}
