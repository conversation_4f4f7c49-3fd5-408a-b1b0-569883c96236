import { Injectable } from "@nestjs/common";
import { CategoryRepositoryForAdmin } from "../repositories/admin.repository";

@Injectable()
export class CategoryProviderForAdmin {

  constructor(private readonly categoryRepository: CategoryRepositoryForAdmin) { }

  async incrementCoachCountOfCategory(categoryId: string): Promise<void> {
    return await this.categoryRepository.incrementCoachCount(categoryId);
  }

  async incrementCoachCountOfCategories(categoryIds: string[]): Promise<void> {
    return await this.categoryRepository.incrementCoachCountOfCategories(categoryIds);
  }

  async getCoachCategoryName(categoryId: string): Promise<string | null> {
    return await this.categoryRepository.getCoachCategoryName(categoryId);
  }

  async validateCategory(categoryId: string): Promise<boolean> {
    return await this.categoryRepository.validateCategory(categoryId);
  }

  /**
   * Invokers:
   * 
   * #1 - when admin will approve a coach profile then that coach will be added to the
   * corresponding category. So in that case this function will be called from pending
   * profile sub-module.
   * @param coachId 
   * @param categoryId 
   * @returns 
   */
  async addCoachToCategory(coachId: string, categoryId: string): Promise<void> {
    return await this.categoryRepository.addCoachToCategory(coachId, categoryId);
  }

  async removeCoachFromCategory(coachId: string, categoryId: string): Promise<void> {
    return await this.categoryRepository.removeCoachFromCategory(coachId, categoryId);
  }

}