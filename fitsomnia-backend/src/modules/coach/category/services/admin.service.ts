import { forwardRef, Inject, Injectable } from "@nestjs/common";
import { Helper } from 'src/helper/helper.interface';
import { deepCasting, shallowCasting } from "src/internal/casting/object.casting";
import { throwNotFoundErr } from "src/internal/exception/api.exception.ext";
import { COACH_CATEGORY_NOT_FOUND } from "../../common/const/coach.const";
import { CoachCategoryEntity } from "../../common/entities/coach-category.entity";
import { SubCategoryProviderForAdmin } from "../../sub-category/providers/admin.provider";
import { CreateCoachCategoryRequestDto, CreateCoachCategoryResponseDto, CreateCoachCategorySuccessResponseDto } from "../dtos/create-coach-category.dto";
import { GetCoachCategoryMapResponseDto, GetCoachCategoryMapSuccessResponseDto, GetCoachSubCategoryMapResponseDto } from "../dtos/get-coach-category-map.dto";
import { GetCoachCategoryResponseDto, GetCoachCategorySuccessResponseDto, GetMultipleCoachCategorySuccessResponseDto } from "../dtos/get-coach-category.dto";
import { UpdateCoachCategoryRequestDto, UpdateCoachCategoryResponseDto, UpdateCoachCategorySuccessResponseDto } from "../dtos/update-coach-category.dto";
import { CategoryRepositoryForAdmin } from "../repositories/admin.repository";

@Injectable()
export class CategoryServiceForAdmin {

  constructor(
    private readonly categoryRepository: CategoryRepositoryForAdmin,
    private readonly helper: Helper,
    @Inject(forwardRef(() => SubCategoryProviderForAdmin))
    private readonly subCategoryProvider: SubCategoryProviderForAdmin,
  ) { }

  async createCoachCategory(reqDto: CreateCoachCategoryRequestDto): Promise<CreateCoachCategorySuccessResponseDto> {
    const categoryInfo = shallowCasting(CoachCategoryEntity, reqDto);
    const newObj = await this.categoryRepository.createCoachCategory(categoryInfo);
    const responseDto = deepCasting(CreateCoachCategoryResponseDto, newObj);
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getMultipleCoachCategories(): Promise<GetMultipleCoachCategorySuccessResponseDto> {
    const categoryDocs = await this.categoryRepository.getCoachCategories();
    const responseDto = categoryDocs.map(item => deepCasting(GetCoachCategoryResponseDto, item));
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getSingleCoachCategory(categoryId: string): Promise<GetCoachCategorySuccessResponseDto> {
    const categoryDoc = await this.categoryRepository.getCoachCategory(categoryId);
    throwNotFoundErr(!categoryDoc, "Coach category not found!", COACH_CATEGORY_NOT_FOUND);

    const responseDto = deepCasting(GetCoachCategoryResponseDto, categoryDoc);
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async updateSingleCoachCategory(categoryId: string, updateDto: UpdateCoachCategoryRequestDto): Promise<UpdateCoachCategorySuccessResponseDto> {
    const categoryInfo = shallowCasting(CoachCategoryEntity, updateDto);
    const newObj = await this.categoryRepository.updateCoachCategory(categoryId, categoryInfo);
    throwNotFoundErr(!newObj, "Coach category not found!", COACH_CATEGORY_NOT_FOUND);

    const responseDto = deepCasting(UpdateCoachCategoryResponseDto, newObj);
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async deleteSingleCoachCategory(categoryId: string): Promise<GetCoachCategorySuccessResponseDto> {
    const categoryDoc = await this.categoryRepository.deleteCoachCategory(categoryId);
    throwNotFoundErr(!categoryDoc, "Coach category not found!", COACH_CATEGORY_NOT_FOUND);

    const responseDto = deepCasting(GetCoachCategoryResponseDto, categoryDoc);
    await this.subCategoryProvider.deleteMultipleCoachSubCategories(categoryId);
    return this.helper.serviceResponse.successResponse(responseDto);
  }

  async getCoachCategoryMap(): Promise<GetCoachCategoryMapSuccessResponseDto> {
    const categories = await this.categoryRepository.getCoachCategories();
    const subCategories = await this.subCategoryProvider.getCoachSubCategoriesAll(0, 1000);
    const responseDto = categories.map(item => deepCasting(GetCoachCategoryMapResponseDto, item));
    responseDto.forEach(category => {
      const subCategoryObjects = subCategories.filter(sub => sub.parentId === category.id);
      category.subCategories = subCategoryObjects.map(sub => deepCasting(GetCoachSubCategoryMapResponseDto, sub));
    });
    return this.helper.serviceResponse.successResponse(responseDto);
  }

}