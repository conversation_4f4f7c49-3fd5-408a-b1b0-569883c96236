import { Injectable } from "@nestjs/common";
import { CoachCategoryEntityModel, CoachesInCategoryModel } from "src/modules/coach/common/db/coach-category.model";
import { CoachCategoryEntity } from "../../common/entities/coach-category.entity";

@Injectable()
export class CategoryRepositoryForAdmin {

  async createCoachCategory(info: CoachCategoryEntity): Promise<CoachCategoryEntity> {
    const category = new CoachCategoryEntityModel(info);
    const categoryDocument = await category.save();
    return categoryDocument.toObject();
  }

  async getCoachCategory(categoryId: string): Promise<CoachCategoryEntity | null> {
    const searchQuery = { id: categoryId, isDeleted: false };
    const categoryDoc = await CoachCategoryEntityModel.findOne(searchQuery).exec();
    return categoryDoc !== null ? categoryDoc.toObject() : null;
  }

  async getCoachCategories(): Promise<CoachCategoryEntity[]> {
    const searchQuery = { isDeleted: false };
    const categoryDoc = await CoachCategoryEntityModel.find(searchQuery).exec();
    return categoryDoc.map(item => item.toObject());
  }

  async getCoachCategoryName(categoryId: string): Promise<string | null> {
    const searchQuery = { id: categoryId, isDeleted: false };
    const categoryDoc = await CoachCategoryEntityModel.findOne(searchQuery).exec();
    return categoryDoc !== null ? categoryDoc.toObject().title : null;
  }

  async updateCoachCategory(categoryId: string, info: CoachCategoryEntity): Promise<CoachCategoryEntity> {
    const updatedDoc = await CoachCategoryEntityModel.findOneAndUpdate(
      { id: categoryId, isDeleted: false },
      { $set: info },
      { new: true, runValidators: true }
    ).exec();
    return updatedDoc !== null ? updatedDoc.toObject() : null;
  }

  async deleteCoachCategory(categoryId: string): Promise<CoachCategoryEntity> {
    const newInfo = { isDeleted: true };
    const updatedDoc = await CoachCategoryEntityModel.findOneAndUpdate(
      { id: categoryId, isDeleted: false },
      { $set: newInfo },
      { new: true, runValidators: true }
    ).exec();
    return updatedDoc !== null ? updatedDoc.toObject() : null;
  }

  async incrementCoachCountOfCategories(categoryIds: string[]): Promise<void> {
    await CoachCategoryEntityModel.updateMany(
      { id: { $in: categoryIds }, isDeleted: false },
      { $inc: { coachCount: 1 } }
    ).exec();
  }

  async incrementCoachCount(categoryId: string): Promise<void> {
    await CoachCategoryEntityModel.findOneAndUpdate(
      { id: categoryId, isDeleted: false },
      { $inc: { coachCount: 1 } }
    ).exec();
  }

  async validateCategory(categoryId: string): Promise<boolean> {
    const searchQuery = { id: categoryId, isDeleted: false };
    const categoryDoc = await CoachCategoryEntityModel.findOne(searchQuery).exec();
    return categoryDoc !== null;
  }

  async addCoachToCategory(coachId: string, categoryId: string): Promise<void> {
    let totalCoachInCategory = -1;

    const session = await CoachesInCategoryModel.startSession();
    session.startTransaction();

    try {
      const oldInfo = await CoachesInCategoryModel.findOne({ coachId, categoryId }).session(session);

      if (oldInfo !== null) {
        await CoachesInCategoryModel.findOneAndUpdate({ coachId, categoryId }, { $inc: { occurance: 1 } }, { session }).exec();
      } else {
        await CoachesInCategoryModel.create([{ coachId, categoryId, occurance: 1 }], { session });
      }

      totalCoachInCategory = await CoachesInCategoryModel.countDocuments({ categoryId }).session(session);

      await session.commitTransaction();
      session.endSession();
    } catch (error) {
      await session.abortTransaction();
      totalCoachInCategory = -1;
    } finally {
      session.endSession();
    }

    if (totalCoachInCategory !== -1) {
      await CoachCategoryEntityModel.findOneAndUpdate(
        { id: categoryId, isDeleted: false },
        { $set: { totalCoach: totalCoachInCategory } }
      ).exec();
    }
  }

  async removeCoachFromCategory(coachId: string, categoryId: string): Promise<void> {
    let totalCoachInCategory = -1;

    const session = await CoachesInCategoryModel.startSession();
    session.startTransaction();

    try {
      const oldInfo = (await CoachesInCategoryModel.findOne({ coachId, categoryId }).session(session));

      if (oldInfo !== null) {
        if (oldInfo.toObject().occurance > 1) {
          await CoachesInCategoryModel.findOneAndUpdate({ coachId, categoryId }, { $inc: { occurance: -1 } }, { session }).exec();
        } else {
          await CoachesInCategoryModel.deleteOne({ coachId, categoryId }, { session }).exec();
        }
      }

      totalCoachInCategory = await CoachesInCategoryModel.countDocuments({ categoryId }).session(session);

      await session.commitTransaction();
      session.endSession();
    } catch (error) {
      await session.abortTransaction();
      totalCoachInCategory = -1;
    } finally {
      session.endSession();
    }

    if (totalCoachInCategory !== -1) {
      await CoachCategoryEntityModel.findOneAndUpdate(
        { id: categoryId, isDeleted: false },
        { $set: { totalCoach: totalCoachInCategory } }
      ).exec();
    }
  }

}