import { HttpStatus, Injectable } from '@nestjs/common';
import {
  CreateChatbotErrorMessages,
  DeleteChatbotHistoryErrorMessages,
  DeleteChatbotHistorySuccessMessages,
  GetChatbotSuccessResponse,
  GetChatbotSuccessResponseNull,
  GetUserInformationErrorMessages,
  QueryChatbotSuccessResponse,
} from 'models';
import { Helper } from 'src/helper/helper.interface';
import { APIException } from 'src/internal/exception/api.exception';
import { UserRepository } from 'src/modules/user/repositories';
import { ChatbotRepository } from '../repositories';

import { Observable } from 'rxjs';

@Injectable()
export class ChatbotService {
  constructor(
    private chatBotRepo: ChatbotRepository,
    private helper: Helper,
    private userRepository: UserRepository,
  ) {}

  async streamChatbotQuery(
    query: string,
    userId: string,
  ): Promise<Observable<string>> {
    const doesUserExist = await this.userRepository.findUser({ id: userId });
    if (!doesUserExist) {
      throw new APIException(
        GetUserInformationErrorMessages.USER_NOT_FOUND,
        'USER_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const doesSessionExist = await this.chatBotRepo.findSessionByUserId(userId);

    const responseStream = doesSessionExist
      ? await this.chatBotRepo.createMessageStream(query, userId)
      : await this.chatBotRepo.createSessionStream(query, userId);
    return new Observable<string>((observer) => {
      responseStream.on('data', (chunk) => observer.next(chunk));
      responseStream.on('end', () => observer.complete());
      responseStream.on('error', (error) => observer.error(error));
    });
  }

  async chatbotQuery(
    query: string,
    userId: string,
  ): Promise<QueryChatbotSuccessResponse> {
    const doesUserExist = await this.userRepository.findUser({ id: userId });
    if (!doesUserExist) {
      throw new APIException(
        GetUserInformationErrorMessages.USER_NOT_FOUND,
        'USER_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }
    const doesSessionExist = await this.chatBotRepo.findSessionByUserId(userId);
    if (doesSessionExist) {
      const Message = await this.chatBotRepo.addMessageToSession(query, userId);
      if (!Message) {
        throw new APIException(
          CreateChatbotErrorMessages.CAN_NOT_CREATE_MESSAGE,
          'CAN_NOT_CREATE_MESSAGE',
          HttpStatus.BAD_REQUEST,
        );
      }
      return this.helper.serviceResponse.successResponse(Message);
    }
    const Session = await this.chatBotRepo.createChatSession(query, userId);
    if (!Session) {
      throw new APIException(
        CreateChatbotErrorMessages.CAN_NOT_CREATE_SESSION,
        'CAN_NOT_CREATE_SESSION',
        HttpStatus.BAD_REQUEST,
      );
    }
    return this.helper.serviceResponse.successResponse(Session);
  }

  async getChatHistory(
    userId: string,
    limit?: number,
    offset?: number,
  ): Promise<GetChatbotSuccessResponse | GetChatbotSuccessResponseNull> {
    const chatbotSession = await this.chatBotRepo.findSessionByUserId(
      userId,
      limit,
      offset,
    );
    if (!chatbotSession) {
      const response: GetChatbotSuccessResponseNull = {
        data: {
          id: null,
          userId: userId,
          messages: [],
        },
      };
      return response;
    }
    return this.helper.serviceResponse.successResponse(chatbotSession);
  }

  async deleteChatHistory(userId: string): Promise<GetChatbotSuccessResponse> {
    const doesSessionExist = await this.chatBotRepo.findSessionByUserId(userId);
    if (!doesSessionExist) {
      throw new APIException(
        DeleteChatbotHistoryErrorMessages.CAN_NOT_FIND_CHAT_HISTORY,
        'CAN_NOT_FIND_CHAT_HISTORY',
        HttpStatus.NOT_FOUND,
      );
    }
    await this.chatBotRepo.deleteHistory(userId);
    return this.helper.serviceResponse.successResponse({
      message: DeleteChatbotHistorySuccessMessages.HISTORY_DELETED_SUCCESSFULLY,
    });
  }
}
