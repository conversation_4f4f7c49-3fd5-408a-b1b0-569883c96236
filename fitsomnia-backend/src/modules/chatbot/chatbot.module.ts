import { Modu<PERSON> } from '@nestjs/common';
import { UserRepository } from '../user/repositories';
import { AIModule } from '../ai/ai.module';
import { ChatbotRepository } from './repositories';
import { ChatbotHelperRepository } from './repositories/chatbot.repository.helper';
import { ChatbotController } from './rest/chatbot.controller';
import { ChatbotService } from './services';

@Module({
  imports: [AIModule],
  controllers: [ChatbotController],
  providers: [
    ChatbotService,
    ChatbotRepository,
    ChatbotHelperRepository,
    UserRepository,
  ],
})
export class ChatbotModule {}
