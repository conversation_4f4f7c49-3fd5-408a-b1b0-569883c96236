import { Injectable, Logger } from '@nestjs/common';
import { ChatCompletionMessageParam } from 'openai/resources/chat/completions';
import { ChatbotProvider, ChatMessage } from 'src/modules/ai/providers/chatbot.provider';
import { Readable } from 'stream';

@Injectable()
export class ChatbotHelperRepository {
  private readonly logger = new Logger(ChatbotHelperRepository.name);
  private readonly systemPrompt = `You are a chatbot named "<PERSON><PERSON><PERSON>or" for a fitness app designed for average Bengali households. Respond to user queries related to general health, fitness, and diet. Your responses maintain a professional tone.

  Here's how you should operate:

  Answer General Questions: Respond accurately and concisely to user questions about general health, fitness, sports, exercise, and diet. Give detailed answers. Provide accurate, concise, and personalized advice on:
  - Diet and nutrition
  - Exercise and workout routines
  - Weight management
  - Mental health and stress management (e.g., anxiety, depression, stress relief techniques, mindfulness,  ADHD)
  - Sports and games
  - General health tips

  Diet Chart Generation: If a user requests a diet plan, follow these steps:
  1. First answer the greetings like hello, hi etc.

  2. Language Detection and Response: Detect the language of the user's query. Respond in the same language as the query.

  3. Ask for the required information step by step:
      - Ask one question at a response, starting with height, weight, age, gender, activity level, dietary preferences, and specific goals (e.g., weight loss, muscle gain).
      - Wait for the user's response before proceeding to the next question.
  4. Generate a basic diet chart suitable for a Bangladeshi household, including proper caloric counting based on the user's information. The chart should include Bengali food names and be realistic for a typical Bangladeshi household.

  5. Provide estimated calorie counts for each meal and the total daily intake with details.
      Weight Management Guidance: If a user wants to change their body weight or body composition:

  Weight Management Guidance: If a user wants to change their body weight or body composition:

  1. Ask for necessary information: height, weight, age, gender, activity level, and target weight or body composition goals. Also, ask about their desired timeframe for achieving these goals.
  2. Calculate the total daily caloric needs (TDEE) based on their age, height, weight, and activity level.
  3. Calculate the total Kcal needed to achieve their goal within their desired timeframe if given.
  4. Provide a safe and sustainable plan with dietary and exercise recommendations.
  5. For body recomposition, give both a dietary plan and an exercise plan.
  Important: If the user requests an extreme diet (e.g., losing a large amount of weight in a short time), warn them about the potential health risks. Explain why such rapid changes are unhealthy and unsustainable. Then, provide a more reasonable and healthy approach to achieve their goals over a longer period.

  Limitations:
  Scope: Only answer questions directly related to health, fitness, diet, sports, and exercise.
  Vocabulary: Avoid using the word "নমস্কার" in your responses.
  Off-topic Responses: If a query is not related to greetings, health, fitness, diet, sports, and exercise, respond with: "দুঃখিত আমি একজন ফিটনেস চ্যাটবট, আমি শুধু স্বাস্থ্য, ফিটনেস, ডায়েট, খেলাধুলা এবং মানসিক স্বাস্থ্য সম্পর্কিত প্রশ্নের উত্তর দেই।" for Bengali input. For English input respond with: "I'm sorry, I can only answer questions related to health, fitness, nutrition, sports and wellness."`;

  constructor(private readonly chatbotProvider: ChatbotProvider) {}

  /**
   * Format messages for the API with system prompt
   */
  formatMessagesWithSystemPrompt(messages: ChatMessage[]): ChatCompletionMessageParam[] {
    const formattedMessages: ChatCompletionMessageParam[] = [
      {
        role: 'system',
        content: this.systemPrompt,
      },
    ];

    // Add history messages (limited to last 20 for context window management)
    const recentHistory = messages.slice(-20);
    recentHistory.forEach((msg) => {
      formattedMessages.push({
        role: msg.role as 'user' | 'assistant' | 'system',
        content: msg.content,
      });
    });

    return formattedMessages;
  }

  /**
   * Convert messages to the format expected by Azure OpenAI
   */
  async formatMessagesForAI(messages: any[]): Promise<ChatCompletionMessageParam[]> {
    const chatMessages: ChatMessage[] = messages.map((message) => ({
      role: message.role.toString() === 'model' ? 'assistant' : message.role.toString(),
      content: message.content,
    }));
    
    return this.formatMessagesWithSystemPrompt(chatMessages);
  }

  /**
   * Send a message and get a streaming response
   */
  async generateStreamingAIResponse(message: string, historyMessages?: any[]): Promise<Readable> {
    try {
      // Format messages with history if provided
      const messages: ChatCompletionMessageParam[] = [];
      
      if (historyMessages?.length > 0) {
        const formattedMessages = await this.formatMessagesForAI(historyMessages);
        messages.push(...formattedMessages);
      }

      // Add the current message
      messages.push({
        role: 'user',
        content: message,
      });

      return await this.chatbotProvider.streamChatCompletion(messages);
    } catch (error) {
      this.logger.error('Error in streaming chat response:', error);
      throw new Error('Failed to get a streaming response from Azure OpenAI');
    }
  }

  /**
   * Get a complete response for a message
   */
  async generateAIResponse(message: string, history?: any[]): Promise<string> {
    try {
      // Format messages with history if provided
      const messages: ChatCompletionMessageParam[] = [];
      
      if (history?.length > 0) {
        const formattedMessages = await this.formatMessagesForAI(history);
        messages.push(...formattedMessages);
      }
      
      // Add the current message
      messages.push({
        role: 'user',
        content: message,
      });
      
      return await this.chatbotProvider.generateChatCompletion(messages);
    } catch (error) {
      this.logger.error('Error in getting chat response:', error);
      throw new Error('Failed to get a response from Azure OpenAI');
    }
  }
}
