import { Injectable } from '@nestjs/common';
import { chatbotSession } from 'models';
import { ChatbotModel } from 'src/database/chatbot/chatbot.model';
import { dietPlanModel } from 'src/database/diet/dietPlan.model';
import { Message, Role } from 'src/entity/chatbot';
import { Readable } from 'stream';
import { ChatbotHelperRepository } from './chatbot.repository.helper';

@Injectable()
export class ChatbotRepository {
  constructor(private chatbotHelperRepository: ChatbotHelperRepository) {}

  async findSessionByUserId(
    userId: string,
    limit?: number,
    offset?: number,
  ): Promise<chatbotSession | null> {
    try {
      if (!limit && !offset) {
        return await ChatbotModel.findOne({ userId: userId });
      }
      limit = Number(limit);
      offset = Number(offset);
      const result = await ChatbotModel.aggregate([
        { $match: { userId } },
        {
          $project: {
            _id: 0,
            messages: {
              $let: {
                vars: {
                  messageCount: { $size: '$messages' },
                },
                in: {
                  $cond: {
                    if: { $gte: [offset, '$$messageCount'] },
                    then: [],
                    else: {
                      $slice: [
                        {
                          $reverseArray: {
                            $slice: [
                              { $reverseArray: '$messages' },
                              offset + limit,
                            ],
                          },
                        },
                        limit,
                      ],
                    },
                  },
                },
              },
            },
          },
        },
      ]);
      if (result.length > 0) {
        result[0].userId = userId;
        return result[0];
      }
      return null;
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async findSession(id: string): Promise<chatbotSession | null> {
    try {
      return await ChatbotModel.findOne({ id: id });
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async findAllSessionsByUserId(userId: string): Promise<chatbotSession[]> {
    try {
      return await ChatbotModel.find({ userId: userId }).sort({
        createdAt: -1,
      });
    } catch (error) {
      console.log(error);
      return [];
    }
  }

  async createMessageStream(query: string, userId: string): Promise<Readable> {
    const chatbotSession = await ChatbotModel.findOne({ userId: userId });

    const userMessage: Message = {
      role: Role.USER,
      content: query,
      createdAt: new Date(),
    };

    chatbotSession.messages.push(userMessage);

    const responseStream = await this.chatbotHelperRepository.generateStreamingAIResponse(
      query,
      chatbotSession.messages.slice(-20),
    );

    const readableStream = new Readable({
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      read() {},
    });

    (async () => {
      try {
        let fullResponse = '';
        for await (const chunk of responseStream) {
          fullResponse += chunk;
          readableStream.push(chunk);
        }
        const modelMessage: Message = {
          role: Role.MODEL,
          content: fullResponse,
          createdAt: new Date(),
        };
        chatbotSession.messages.push(modelMessage);
        await chatbotSession.save();
        readableStream.push(null);
      } catch (error) {
        readableStream.destroy(error);
      }
    })();

    return readableStream;
  }

  async createSessionStream(query: string, userId: string): Promise<Readable> {
    const responseStream = await this.chatbotHelperRepository.generateStreamingAIResponse(
      query,
    );

    const readableStream = new Readable({
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      read() {},
    });

    (async () => {
      try {
        const chatbotSession = await ChatbotModel.create({
          userId: userId,
          messages: [{ role: Role.USER, content: query }],
        });

        let fullResponse = '';
        for await (const chunk of responseStream) {
          fullResponse += chunk;
          readableStream.push(chunk);
        }
        const modelMessage: Message = {
          role: Role.MODEL,
          content: fullResponse,
          createdAt: new Date(),
        };
        chatbotSession.messages.push(modelMessage);
        await chatbotSession.save();
        readableStream.push(null); // End the stream
      } catch (error) {
        readableStream.destroy(error); // Handle errors
      }
    })();

    return readableStream;
  }

  async addMessageToSession(
    query: string,
    userId: string,
  ): Promise<Message[] | null> {
    const history: chatbotSession = await this.findSessionByUserId(userId);
    try {
      const content = await this.chatbotHelperRepository.generateAIResponse(
        query,
        history.messages.slice(-20),
      );
      const chatbotSession = await ChatbotModel.findOne({ userId: userId });
      const userMessage: Message = {
        role: Role.USER,
        content: query,
        createdAt: new Date(),
      };

      const modelMessage: Message = {
        role: Role.MODEL,
        content: content,
        createdAt: new Date(),
      };
      chatbotSession.messages.push(userMessage, modelMessage);
      const session = await chatbotSession.save();
      const response = session.messages.slice(-2);
      return response;
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async createChatSession(
    query: string,
    userId: string,
  ): Promise<Message[] | null> {
    let updateQuery = query;
    try {
      const userInfo = await dietPlanModel.findOne({ 'user.id': userId });
      if (userInfo) {
        updateQuery = `${query}. User info: height = ${userInfo.user.height}, weight = ${userInfo.user.weight}, age = ${userInfo.user.age}, gender = ${userInfo.user.gender}`;
      }
      const content = await this.chatbotHelperRepository.generateAIResponse(
        updateQuery,
      );
      const response = await ChatbotModel.create({
        userId: userId,
        messages: [
          { role: 'user', content: updateQuery },
          { role: 'model', content: content },
        ],
      });
      return response.messages;
    } catch (error) {
      console.log(error);
      return null;
    }
  }

  async deleteHistory(userId: string): Promise<chatbotSession | null> {
    const chatbotSession = await ChatbotModel.findOne({ userId: userId });
    if (chatbotSession) {
      chatbotSession.messages = [];
      await chatbotSession.save();
    }
    return chatbotSession;
  }
}
