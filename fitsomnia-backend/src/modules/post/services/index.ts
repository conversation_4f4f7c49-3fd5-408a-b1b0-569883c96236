import { HttpStatus, Injectable } from '@nestjs/common';
import { redisCacheConfig } from 'config/cache';
import { sharedConfig } from 'config/shared';
import {
  CreatePostCommentRequest,
  CreatePostCommentResponse,
  CreatePostRequest,
  CreatePostResponse,
  FitBuddiesStatus,
  FitBuddiesType,
  GetPostCommentsResponse,
  GetPostInfoResponse,
  GetPostLikersQuery,
  GetPostListResponse,
  GetPostListWithUserInfo,
  GetPostMediaResponse,
  GetPostReactionerInfoResponse,
  GetPostReactionerQuery,
  GetSinglePostResponse,
  GetTimelineListResponse,
  LikePostQuery,
  NotificationCategory,
  NotificationModule,
  NotificationType,
  PostErrorEnum,
  PostMedia,
  ProfileVisibilityEnum,
  SharePostRequest,
  SharePostSuccessResponse,
  UpdatePostBody,
  UpdatePostResponse,
} from 'models';
import {
  DeletePostErrorMessages,
  DeletePostSuccessMessages,
  DeletePostSuccessResponse,
} from 'models/post/deletePost';
import { RedisCacheHelper } from 'src/cache/helper';
import { getMyFeed } from 'src/cache/post';
import {
  Post,
  PostContent,
  PostPrivacy,
  PostReactionsType,
  PostType,
} from 'src/entity/post';
import { Helper } from 'src/helper/helper.interface';
import { APIException } from 'src/internal/exception/api.exception';
import { NotificationHelperService } from 'src/modules/notification/services/helper.notification.service';
import { TaskProgressService } from 'src/modules/task/task-progress.service';
import { QueueInstance } from 'src/queue-system';
import { QueueName, QueuePayloadType } from 'src/queue-system/predefined-data';
import { getImageVersionKey } from 'src/utils/response';
import { PostRepository } from '../repository';
import { LikePostApiResponse } from '../rest/dto';

@Injectable()
export class PostService {
  constructor(
    private readonly postRepository: PostRepository,
    private notificationHelperService: NotificationHelperService,
    private helper: Helper,
    private readonly taskProgressService: TaskProgressService,
  ) {}

  async createPost(
    userId: string,
    body: CreatePostRequest,
  ): Promise<CreatePostResponse> {
    const { content, videos, images } = body;
    if (!content && !images && !videos) {
      throw new APIException(
        PostErrorEnum.PROVIDE_NECESSARY_CONTENT,
        'PROVIDE_NECESSARY_CONTENT',
        HttpStatus.CONFLICT,
      );
    }
    const newPost = await this.postRepository.createPost({
      ...body,
      userId,
    });
    if (!newPost)
      throw new APIException(
        PostErrorEnum.ERROR_IN_CREATING_NEW_POST,
        'ERROR_IN_CREATING_NEW_POST',
        HttpStatus.CONFLICT,
      );

    // Send to the post-approval queue
    try {
      const payload = {
        payloadType: QueuePayloadType.POST_APPROVE,
        ...newPost,
      };

      (await QueueInstance).sendPayload(
        QueueName.POST_APPROVE_QUEUE,
        Buffer.from(JSON.stringify(payload)),
      );
    } catch (error: any) {
      console.log(error.message);
    }

    // Track the post creation activity for task progress
    await this.taskProgressService.trackPostCreation(userId);

    return this.helper.serviceResponse.successResponse({
      ...(await this.mappedGeneratePresignedUrl({
        ...newPost,
        userInfo: {
          name: null,
          image: {
            profile: null,
          },
        },
      })),
      sharedPost: null,
      productInfo: null,
      weight: 0,
      liked: false,
      totalLikes: 0,
      totalComments: 0,
      totalShares: 0,
      comments: [],
    });
  }

  async deletePost(
    postId: string,
    userId: string,
  ): Promise<DeletePostSuccessResponse> {
    const post = await this.postRepository.getPost({
      id: postId,
      userId,
    });
    if (!post)
      throw new APIException(
        DeletePostErrorMessages.NO_POST_EXIST,
        'NO_POST_EXIST',
        HttpStatus.BAD_REQUEST,
      );

    const deletePost = await this.postRepository.deletePost(postId, userId);
    if (!deletePost) {
      throw new APIException(
        DeletePostErrorMessages.CAN_NOT_DELETE_POST,
        'CAN_NOT_DELETE_POST',
        HttpStatus.BAD_REQUEST,
      );
    }

    // delete from cache
    await RedisCacheHelper.deleteData(
      `${redisCacheConfig.post_channel}/${postId}`,
    );

    return this.helper.serviceResponse.successResponse({
      message: DeletePostSuccessMessages.POST_DELETED_SUCCESSFULLY,
    });
  }

  async getPost(myId: string, postId: string): Promise<GetSinglePostResponse> {
    const post = await this.postRepository.getPostAggregate({
      id: postId,
    });
    if (!post)
      throw new APIException(
        PostErrorEnum.POST_NOT_FOUND,
        'POST_NOT_FOUND',
        HttpStatus.BAD_REQUEST,
      );

    const [comments, likedPost, buddy] = await Promise.all([
      await this.postRepository.getPostComments({ postId: post.id }, 0, 2),
      // await this.postRepository.checkLikedPost({
      //   postId: post.id,
      //   userId: myId,
      //   type: PostReactionsType.LIKE,
      // }),
      await this.postRepository.checkPostReaction({
        postId: post.id,
        userId: myId,
      }),
      myId !== post.userId &&
        (await this.postRepository.findFitBuddy({
          $or: [
            {
              userId: myId,
              fitBuddyId: post.userId,
            },
            {
              userId: post.userId,
              fitBuddyId: myId,
            },
          ],
        })),
    ]);

    // console.log(likedPost.type)

    // console.log('post',post)

    if (
      myId !== post.userId &&
      ((post.privacy === PostPrivacy.FRIENDS &&
        (!buddy || buddy?.type === FitBuddiesType.FOLLOWER)) ||
        (buddy && buddy.status === FitBuddiesStatus.BLOCKED))
    )
      throw new APIException(
        PostErrorEnum.CAN_NOT_SEE_POST,
        'CAN_NOT_SEE_POST',
        HttpStatus.BAD_REQUEST,
      );

    (post.comments as any) = comments;
    (post as any).liked = likedPost ? true : false;
    (post as any).reactionType = likedPost ? likedPost.type : null;
    return this.helper.serviceResponse.successResponse(
      await this.mappedGeneratePresignedUrl(post),
    );
  }

  async updatePost(
    userId: string,
    data: UpdatePostBody,
    postId: string,
  ): Promise<UpdatePostResponse> {
    const updatedPost = await this.postRepository.updatePost(
      { id: postId, userId },
      data,
    );

    if (!updatedPost)
      throw new APIException(
        PostErrorEnum.POST_CAN_NOT_BE_UPDATED,
        'POST_CAN_NOT_BE_UPDATED',
        HttpStatus.BAD_REQUEST,
      );

    await this.updateRedisCache(updatedPost);
    return this.helper.serviceResponse.successResponse(
      await this.mappedGeneratePresignedUrl(updatedPost),
    );
  }

  async getPostList(
    userId: string,
    refresh: any,
  ): Promise<GetPostListResponse> {
    refresh = refresh == 'true' || refresh == true ? true : false;
    const postList = await getMyFeed(userId, refresh);
    return this.helper.serviceResponse.successResponse(
      postList
        ? await this.setNewsFeedPostsAndProductsPresignedUrl(postList)
        : [],
    );
  }

  async getPostImages(
    myId: string,
    userId: string,
    offset?: number,
    limit?: number,
  ): Promise<GetPostMediaResponse> {
    const buddy =
      myId !== userId &&
      (await this.postRepository.findFitBuddy({
        userId: myId,
        fitBuddyId: userId,
      }));
    if (buddy && buddy.status === FitBuddiesStatus.BLOCKED)
      throw new APIException(
        PostErrorEnum.POST_LIST_NOT_FOUND,
        'POST_LIST_NOT_FOUND',
        HttpStatus.BAD_REQUEST,
      );

    const posts =
      myId !== userId
        ? await this.postRepository.getPostMedia(userId, buddy)
        : await this.postRepository.getOwnPostMedia(myId);

    let tmpImagesCount = 0;
    const images = [];
    let loopBreak = false;

    // set the images' pre-signed url.
    for (let index = 0, len = posts?.length; index < len; index++) {
      if (posts[index].images?.length) {
        for (
          let imagesIndex = 0, imagesLength = posts[index].images.length;
          imagesIndex < imagesLength;
          imagesIndex++
        ) {
          if (
            posts[index].images[imagesIndex]?.url &&
            posts[index].images[imagesIndex]?.url !== ''
          ) {
            tmpImagesCount++;
            tmpImagesCount > (offset || 0) &&
              images.push({
                postId: posts[index].id,
                url: posts[index].images[imagesIndex].url,
              });
          }

          // The number of images has reached its limit.
          if (images.length === (limit || sharedConfig.defaultLimit)) {
            loopBreak = true;
            break;
          }
        }
      }
      // The number of images has reached its limit.
      if (loopBreak) break;
    }
    return this.helper.serviceResponse.successResponse(
      await Promise.all(
        images.map(async (image: PostMedia) => {
          image = getImageVersionKey<PostMedia>(image, image?.url, [
            'small',
            'medium',
            'thumbnail',
          ]);
          image.url =
            await this.helper.azureBlobStorageService.generatePreSignedUrl(
              image?.url,
              'original/post',
            );
          image.small =
            await this.helper.azureBlobStorageService.generatePreSignedUrl(
              image?.small,
              'small/original/post',
            );
          image.medium =
            await this.helper.azureBlobStorageService.generatePreSignedUrl(
              image?.medium,
              'medium/original/post',
            );
          image.thumbnail =
            await this.helper.azureBlobStorageService.generatePreSignedUrl(
              image?.thumbnail,
              'thumbnail/original/post',
            );
          return image;
          // image.url =
          //   await this.helper.azureBlobStorageService.generatePreSignedUrl(
          //     image.url,
          //     'original/post',
          //   );
          // return image;
        }),
      ),
    );
  }

  async getPostVideos(
    myId: string,
    userId: string,
    offset?: number,
    limit?: number,
  ): Promise<GetPostMediaResponse> {
    const buddy =
      myId !== userId &&
      (await this.postRepository.findFitBuddy({
        userId: myId,
        fitBuddyId: userId,
      }));
    if (buddy && buddy.status === FitBuddiesStatus.BLOCKED)
      throw new APIException(
        PostErrorEnum.POST_LIST_NOT_FOUND,
        'POST_LIST_NOT_FOUND',
        HttpStatus.BAD_REQUEST,
      );

    const posts =
      myId !== userId
        ? await this.postRepository.getPostMedia(userId, buddy)
        : await this.postRepository.getOwnPostMedia(myId);

    let tmpVideosCount = 0;
    const videos = [];
    let loopBreak = false;

    // set the videos' pre-signed url.
    for (let index = 0, len = posts?.length; index < len; index++) {
      if (posts[index].videos?.length) {
        for (
          let videosIndex = 0, videosLength = posts[index].videos.length;
          videosIndex < videosLength;
          videosIndex++
        ) {
          if (
            posts[index].videos[videosIndex]?.url &&
            posts[index].videos[videosIndex]?.url !== ''
          ) {
            tmpVideosCount++;
            tmpVideosCount > (offset || 0) &&
              videos.push({
                postId: posts[index].id,
                url: posts[index].videos[videosIndex].url,
              });
          }

          // The number of videos has reached its limit.
          if (videos.length === (limit || Number.MAX_SAFE_INTEGER)) {
            loopBreak = true;
            break;
          }
        }
      }
      // The number of videos has reached its limit.
      if (loopBreak) break;
    }
    return this.helper.serviceResponse.successResponse(
      await Promise.all(
        videos.map(async (video: PostMedia) => {
          video = getImageVersionKey<PostMedia>(video, video?.url, [
            'thumbnail',
          ]);
          video.url =
            await this.helper.azureBlobStorageService.generatePreSignedUrl(
              video.url,
              'original/post',
            );
          video.thumbnail =
            await this.helper.azureBlobStorageService.generatePreSignedUrl(
              video?.thumbnail,
              'thumbnail/videos/original/post',
            );
          return video;
        }),
      ),
    );
  }

  async createPostComment(
    userId: string,
    data: CreatePostCommentRequest,
    ownerId: string,
    postId: string,
  ): Promise<CreatePostCommentResponse> {
    const [post, owner, buddy, ownInfo] = await Promise.all([
      await this.postRepository.getPost({
        id: postId,
      }),
      await this.postRepository.getUserInfo({
        id: ownerId,
      }),
      await this.postRepository.findFitBuddy({
        userId: ownerId,
        fitBuddyId: userId,
        status: FitBuddiesStatus.ACTIVE,
      }),
      await this.postRepository.getUserInfo({
        id: userId,
      }),
    ]);

    if (
      !post ||
      !owner ||
      (ownerId !== userId && post.privacy === PostPrivacy.PRIVATE) ||
      (ownerId !== userId &&
        buddy &&
        post.privacy === PostPrivacy.FRIENDS &&
        buddy.type === FitBuddiesType.FOLLOWER)
    )
      throw new APIException(
        PostErrorEnum.CAN_NOT_DO_THAT,
        'CAN_NOT_DO_THAT',
        HttpStatus.BAD_REQUEST,
      );
    await this.taskProgressService.trackPostComment(userId, postId);
    const comment = await this.postRepository.createPostComment(
      userId,
      data,
      postId,
      owner.id,
      buddy,
    );

    // ownInfo.image.profile =
    //   ownInfo.image.profile &&
    //   (await this.helper.azureBlobStorageService.generatePreSignedUrl(
    //     ownInfo.image.profile,
    //     'user',
    //   ));
    const newPost = await this.postRepository.getPost({
      id: postId,
    });
    // console.log(newPost)
    // console.log(newPost?.totalComments)

    comment.userInfo = {
      id: ownInfo.id,
      name: ownInfo.name,
      image: ownInfo.image,
    };

    // console.log('comments' , comment )

    const newreaction = {
      ...comment,
      totalComments: newPost?.totalComments,
    };

    // console.log('new reaction', newreaction)

    // comment.totalComments =newPost?.totalComments;

    // Notification Payload
    const payload = {
      recipient: owner,
      createdBy: ownInfo,
      title: 'Your post got a comment!',
      content: `${ownInfo?.name} commented on your post`,
      module: NotificationModule.POST,
      type: NotificationType.POST_COMMENT,
      category: NotificationCategory.POST_COMMENT,
      documentId: postId,
    };

    await this.notificationHelperService.sendNotifications(payload);

    // Track the post comment activity for task progress

    return this.helper.serviceResponse.successResponse(newreaction);
  }

  async getPostComments(
    userId: string,
    postId: string,
    ownerId: string,
    offset?: number,
    limit?: number,
  ): Promise<GetPostCommentsResponse> {
    const [post, owner, buddy] = await Promise.all([
      await this.postRepository.getPost({
        id: postId,
      }),
      await this.postRepository.getUserInfo({
        id: ownerId,
      }),
      await this.postRepository.findFitBuddy({
        userId: ownerId,
        fitBuddyId: userId,
        status: FitBuddiesStatus.ACTIVE,
      }),
    ]);

    // Whether the logged-in user leaves a comment on this post or not,
    // If the logged-in user does not leave a comment on it, then we should consider some conditions.
    // 1. post-privacy 2. buddy type
    if (
      !post ||
      !owner ||
      (ownerId !== userId && post.privacy === PostPrivacy.PRIVATE) ||
      (ownerId !== userId &&
        post.privacy === PostPrivacy.FRIENDS &&
        buddy?.type === FitBuddiesType.FOLLOWER)
    )
      throw new APIException(
        PostErrorEnum.CAN_NOT_DO_THAT,
        'CAN_NOT_DO_THAT',
        HttpStatus.BAD_REQUEST,
      );

    const comments = await this.postRepository.getPostComments(
      {
        postId,
      },
      offset,
      limit,
    );
    return this.helper.serviceResponse.successResponse(comments);
  }

  async getPostLikersInfo(
    postId: string,
    condition: GetPostLikersQuery,
  ): Promise<GetPostInfoResponse> {
    const post = await this.postRepository.getPost({
      id: postId,
    });

    if (!post) {
      throw new APIException(
        PostErrorEnum.POST_NOT_FOUND,
        'POST_NOT_FOUND',
        HttpStatus.BAD_REQUEST,
      );
    }

    const likersList = await this.postRepository.getPostLikersInfo(
      postId,
      condition?.offset,
      condition?.limit,
    );
    return this.helper.serviceResponse.successResponse(likersList);
  }

  async getPostReactionerInfo(
    postId: string,
    condition: GetPostReactionerQuery,
  ): Promise<GetPostReactionerInfoResponse> {
    const post = await this.postRepository.getPost({
      id: postId,
    });

    if (!post) {
      throw new APIException(
        PostErrorEnum.POST_NOT_FOUND,
        'POST_NOT_FOUND',
        HttpStatus.BAD_REQUEST,
      );
    }

    const reactionerList = await this.postRepository.getPostReactionerInfo(
      postId,
      condition?.offset,
      condition?.limit,
      condition?.reactionType,
    );
    return this.helper.serviceResponse.successResponse(reactionerList);
  }

  async addMultiPostReaction(
    userId: string,
    query: LikePostQuery,
    postId: string,
  ): Promise<LikePostApiResponse> {
    const { ownerId, reactionType, isReacting } = query;

    let setReactionValue = false;
    let setReactionType = '';

    if (query.isReacting) {
      setReactionType = query.reactionType;
      query.isReacting = String(query.isReacting) === 'true';
      setReactionValue = Boolean(query.isReacting);
    }

    const newReactionType: PostReactionsType = reactionType
      ? (reactionType as PostReactionsType)
      : PostReactionsType.LIKE;

    let reactionPost = {};

    const [post, owner, doesLikedPost, buddy, loggedInUser] = await Promise.all(
      [
        await this.postRepository.getPost({
          id: postId,
        }),
        await this.postRepository.getUserInfo({
          id: ownerId,
        }),
        await this.postRepository.checkPostReaction({
          userId,
          postId,
        }),
        await this.postRepository.findFitBuddy({
          userId: ownerId,
          fitBuddyId: userId,
          status: FitBuddiesStatus.ACTIVE,
        }),
        await this.postRepository.getUserInfo({
          id: userId,
        }),
      ],
    );

    // Whether the logged-in user likes this post or not,
    // If the logged-in user doesn't, then we should think about some conditions.
    // 1. post-privacy 2. buddy type 3. already liked this post or not

    if (
      !post ||
      !owner ||
      (ownerId !== userId && post.privacy === PostPrivacy.PRIVATE) ||
      (ownerId !== userId &&
        buddy &&
        post.privacy === PostPrivacy.FRIENDS &&
        buddy.type === FitBuddiesType.FOLLOWER)
    )
      throw new APIException(
        PostErrorEnum.CAN_NOT_DO_THAT,
        'CAN_NOT_DO_THAT',
        HttpStatus.BAD_REQUEST,
      );

    // For counting each reaction Type

    if (doesLikedPost) {
      if (doesLikedPost.type === newReactionType) {
        if (isReacting === true) {
          // User is trying to add the same reaction again
          throw new APIException(
            PostErrorEnum.CAN_NOT_DO_THAT,
            'You have already reacted to this post with the same reaction type.',
            HttpStatus.BAD_REQUEST,
          );
        } else {
          // User is removing their reaction

          await this.postRepository.updateReactionsCount(
            postId,
            doesLikedPost.type,
            false,
          );
        }
      } else {
        if (isReacting) {
          // User is changing reaction type
          await this.postRepository.updatePostReactionType(
            { userId, postId },
            newReactionType,
          );
          // console.log(post.reactions)
          // if (!post.reactions) {
          //   console.log('hello')
          await this.postRepository.updateReactionsCount(
            postId,
            doesLikedPost.type,
            false,
          );
          // }
          // Decrement old reaction count

          // Increment new reaction count
          await this.postRepository.updateReactionsCount(
            postId,
            newReactionType,
            true,
          );
        } else {
          // User is trying to remove their reaction but specifying a different type
          throw new APIException(
            PostErrorEnum.POST_CAN_NOT_BE_DELETED,
            'Invalid action: Cannot remove a different reaction type.',
            HttpStatus.BAD_REQUEST,
          );
        }
      }
    } else {
      if (isReacting || query.liked) {
        // User is adding a new reaction
        // await this.postRepository.addPostReaction(userId, postId, newReactionType, session);
        await this.postRepository.updateReactionsCount(
          postId,
          newReactionType,
          true,
        );
      } else {
      }
    }

    if (doesLikedPost && query.isReacting === true) {
      console.log('update the reaction');
      const updatedReaction = await this.postRepository.updatePostReactionType(
        { userId, postId }, // Combine userId and postId into a single filter object
        setReactionType, // The new reaction type to be updated
      );

      const newPost = await this.postRepository.getPost({
        id: postId,
      });

      const posrLikersList = await this.postRepository.getPostLikersInfo(
        postId,
        0,
        0,
      );

      //  console.log(posrLikersList)

      // console.log('update reaction', newPost)

      const newreaction = {
        ...updatedReaction,
        reactionType: updatedReaction?.type,
        totalReactions: newPost?.totalReactions,
        users: posrLikersList,
      };

      return this.helper.serviceResponse.successResponse(newreaction);
    }

    const addreaction = await this.postRepository.addPostMultiReaction(
      userId,
      postId,
      setReactionValue,
      setReactionType,

      owner.id,
      buddy,
    );

    const newPost = await this.postRepository.getPost({
      id: postId,
    });

    const posrLikersList = await this.postRepository.getPostLikersInfo(
      postId,
      0,
      0,
    );

    //  console.log(posrLikersList)

    const {
      userId: reactionUserId,
      postId: reactionPostId,
      type,
      createdAt,
      updatedAt,
    } = addreaction;

    reactionPost = {
      userId: reactionUserId,
      type,
      postId: reactionPostId,
      createdAt,
      updatedAt,
      reactionType: addreaction?.type,
      totalReactions: newPost?.totalReactions,
      users: posrLikersList,
    };

    if (Boolean(query.isReacting)) {
      // Notification Payload
      const payload = {
        recipient: owner,
        createdBy: loggedInUser,
        title: 'Your post got reacted!',
        content: `${loggedInUser?.name} reacted your post`,
        module: NotificationModule.POST,
        type: NotificationType.POST_REACTION,
        category: NotificationCategory.POST_REACTION,
        documentId: postId,
      };

      await this.notificationHelperService.sendNotifications(payload);
    }

    if (query.isReacting === true) {
      // Track the post reaction activity for task progress
      await this.taskProgressService.trackPostReaction(userId, postId);
    }

    return this.helper.serviceResponse.successResponse(reactionPost);
  }

  async addPostReaction(
    userId: string,
    query: LikePostQuery,
    postId: string,
  ): Promise<LikePostApiResponse> {
    const { ownerId } = query;
    query.liked = String(query.liked) === 'true';

    const [post, owner, doesLikedPost, buddy, loggedInUser] = await Promise.all(
      [
        await this.postRepository.getPost({
          id: postId,
        }),
        await this.postRepository.getUserInfo({
          id: ownerId,
        }),
        await this.postRepository.checkLikedPost({
          userId,
          postId,
          type: PostReactionsType.LIKE,
        }),
        await this.postRepository.findFitBuddy({
          userId: ownerId,
          fitBuddyId: userId,
          status: FitBuddiesStatus.ACTIVE,
        }),
        await this.postRepository.getUserInfo({
          id: userId,
        }),
      ],
    );

    // Whether the logged-in user likes this post or not,
    // If the logged-in user doesn't, then we should think about some conditions.
    // 1. post-privacy 2. buddy type 3. already liked this post or not
    if (
      !post ||
      !owner ||
      (Boolean(query.liked) && doesLikedPost) ||
      (!Boolean(query.liked) && !doesLikedPost) ||
      (ownerId !== userId && post.privacy === PostPrivacy.PRIVATE) ||
      (ownerId !== userId &&
        buddy &&
        post.privacy === PostPrivacy.FRIENDS &&
        buddy.type === FitBuddiesType.FOLLOWER)
    )
      throw new APIException(
        PostErrorEnum.CAN_NOT_DO_THAT,
        'CAN_NOT_DO_THAT',
        HttpStatus.BAD_REQUEST,
      );

    const likedPost = await this.postRepository.addPostReaction(
      userId,
      postId,
      Boolean(query.liked),
      PostReactionsType.LIKE,
      owner.id,
      buddy,
    );

    if (Boolean(query.liked)) {
      // Notification Payload
      const payload = {
        recipient: owner,
        createdBy: loggedInUser,
        title: 'Your post got liked!',
        content: `${loggedInUser?.name} liked your post`,
        module: NotificationModule.POST,
        type: NotificationType.POST_REACTION,
        category: NotificationCategory.POST_REACTION,
        documentId: postId,
      };

      await this.notificationHelperService.sendNotifications(payload);
    }

    return this.helper.serviceResponse.successResponse(likedPost);
  }

  // Set the pre-signed url for media.
  async mappedGeneratePresignedUrl(
    post: Post & Partial<GetPostListWithUserInfo>,
  ): Promise<(Post & Partial<GetPostListWithUserInfo>) | null> {
    try {
      post.images = post.images?.length
        ? await Promise.all(
            post.images.map(async (image) => {
              image.key = image?.url;

              image = getImageVersionKey<PostContent>(image, image?.url, [
                'small',
                'medium',
                'thumbnail',
              ]);
              image.url =
                await this.helper.azureBlobStorageService.generatePreSignedUrl(
                  image?.url,
                  'original/post',
                );
              image.small =
                await this.helper.azureBlobStorageService.generatePreSignedUrl(
                  image?.small,
                  'small/original/post',
                );
              image.medium =
                await this.helper.azureBlobStorageService.generatePreSignedUrl(
                  image?.medium,
                  'medium/original/post',
                );
              image.thumbnail =
                await this.helper.azureBlobStorageService.generatePreSignedUrl(
                  image?.thumbnail,
                  'thumbnail/original/post',
                );
              return image;
            }),
          )
        : [];

      post.videos = post.videos?.length
        ? await Promise.all(
            post.videos.map(async (video: PostMedia) => {
              // Store the original URL from database as the key
              // Store the original URL from database as the key
              video.key = video?.url;

              // Determine if this is an HLS stream
              const isHlsStream = video.url?.includes('master.m3u8');

              // Apply thumbnail version key
              video = getImageVersionKey<PostMedia>(video, video?.url, [
                'thumbnail',
              ]);

              // Generate the presigned URL with the correct feature path
              // For HLS streams, we need to preserve the full HLS path including master.m3u8
              if (isHlsStream) {
                // Extract the base path without the 'hls/' prefix
                const hlsPath = video.url;
                // Use the CDN base URL with the full HLS path to maintain HLS streaming
                video.url =
                  await this.helper.azureBlobStorageService.generatePreSignedUrl(
                    hlsPath,
                    'hls/original/post',
                  );
              } else {
                // For regular videos, use the standard approach
                video.url =
                  await this.helper.azureBlobStorageService.generatePreSignedUrl(
                    video.url,
                    'original/post',
                  );
              }
              video.thumbnail =
                await this.helper.azureBlobStorageService.generatePreSignedUrl(
                  video?.thumbnail,
                  'thumbnail/videos/original/post',
                );
              return video;
            }),
          )
        : [];

      // post.userInfo.image.profile = post.userInfo?.image?.profile
      //   ? await this.helper.azureBlobStorageService.generatePreSignedUrl(
      //       post.userInfo.image?.profile,
      //       'user',
      //     )
      //   : null;
      return post;
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async updateRedisCache(post: any): Promise<void> {
    try {
      const likedPost = await this.postRepository.checkPostReaction({
        postId: post.id,
        userId: post.userId,
      });
      const comments = await this.postRepository.getPostComments(
        { postId: post.id },
        0,
        2,
      );
      const likersList = await this.postRepository.getPostLikersInfo(
        post.id,
        0,
        2,
      );
      const likersListByType = await this.postRepository.getPostMultiLikersInfo(
        post.id,
        0,
        2,
      );

      (post.comments as any) = comments;
      (post as any).liked = likedPost ? true : false;
      (post as any).reactionType = likedPost ? likedPost.type : null;
      post.userInfo = await this.postRepository.getUserInfo({
        id: post.userId,
      });

      post.likersInfo = likersList;
      post.totalReactionerInfo = likersList;
      post.reactionTypeUserInfo = likersListByType;

      // update cache data(post)
      await RedisCacheHelper.setData<Post>(
        `${redisCacheConfig.post_channel}/${post.id}`,
        post,
      );
    } catch (error) {
      console.log(error.message);
    }
  }

  async sharePost(
    postId: string,
    ownerId: string,
    userId: string,
    body: SharePostRequest,
  ): Promise<SharePostSuccessResponse> {
    const [mainPost, owner, buddy] = await Promise.all([
      await this.postRepository.getPost({
        id: postId,
      }),
      await this.postRepository.getUserInfo({
        id: ownerId,
      }),
      await this.postRepository.findFitBuddy({
        userId: ownerId,
        fitBuddyId: userId,
        status: FitBuddiesStatus.ACTIVE,
      }),
    ]);

    // Whether the logged-in user shares this post or not,
    // If the logged-in user doesn't, then we should consider some conditions.
    // 1. post-privacy 2. buddy type
    if (
      !mainPost ||
      !owner ||
      (ownerId !== userId && mainPost.privacy === PostPrivacy.PRIVATE) ||
      (ownerId !== userId &&
        buddy &&
        mainPost.privacy === PostPrivacy.FRIENDS &&
        buddy.type === FitBuddiesType.FOLLOWER)

      // !mainPost ||
      // !owner ||
      // (ownerId !== userId && mainPost.privacy === PostPrivacy.PRIVATE) ||
      // (ownerId !== userId &&
      //   mainPost.privacy === PostPrivacy.FRIENDS
      //   //  && (!buddy || buddy.type === FitBuddiesType.FOLLOWER)
      //   )
    )
      throw new APIException(
        PostErrorEnum.CAN_NOT_DO_THAT,
        'CAN_NOT_DO_THAT',
        HttpStatus.BAD_REQUEST,
      );

    const sharedPost = await this.postRepository.sharePost(
      mainPost.id,
      mainPost.userId,
      buddy?.type,
      {
        sharedPost: mainPost,
        userId,
        type: PostType.SHARE,
        ...body,
      },
    );

    if (!sharedPost)
      throw new APIException(
        PostErrorEnum.ERROR_IN_SHARING_POST,
        'ERROR_IN_SHARING_POST',
        HttpStatus.CONFLICT,
      );

    // Send to the post-approval queue
    try {
      const payload = {
        payloadType: QueuePayloadType.POST_APPROVE,
        ...sharedPost,
      };

      (await QueueInstance).sendPayload(
        QueueName.POST_APPROVE_QUEUE,
        Buffer.from(JSON.stringify(payload)),
      );
    } catch (error: any) {
      console.log(error.message);
    }

    return this.helper.serviceResponse.successResponse(sharedPost);
  }

  async setNewsFeedPostsAndProductsPresignedUrl(postList: any[]): Promise<any> {
    try {
      return await Promise.all(
        (postList || []).map(async (post: any) => {
          // if (post.type === 'ADVERTISING_PRODUCT' && post?.productInfo) {
          //   post.productInfo =
          //     await this.mappedAdvertisingProductsPhotosPresignedUrl(
          //       post.productInfo,
          //     );
          // } else {
          post = await this.mappedGeneratePresignedUrl(post);

          post &&
            post.type === PostType.SHARE &&
            post?.sharedPost &&
            (post.sharedPost = await this.mappedGeneratePresignedUrl(
              post.sharedPost,
            ));
          // }
          return post;
        }),
      );
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  // async mappedAdvertisingProductsPhotosPresignedUrl(
  //   product: Partial<Product>,
  // ): Promise<Partial<Product> | null> {
  //   try {
  //     product.photos = product.photos?.length
  //       ? await Promise.all(
  //           product.photos.map(async (photo: { url: string }) => {
  //             photo.url =
  //               await this.helper.azureBlobStorageService.generatePreSignedUrl(
  //                 photo?.url,
  //                 'product',
  //               );
  //             return photo;
  //           }),
  //         )
  //       : [];
  //     return product;
  //   } catch (error) {
  //     console.log(error.message);
  //     return null;
  //   }
  // }

  async getUserPostList(
    myId: string,
    userId: string,
    offset?: number,
    limit?: number,
  ): Promise<GetTimelineListResponse> {
    limit =
      !limit || Number(limit) > sharedConfig.defaultMaxLImit
        ? sharedConfig.defaultLimit
        : Number(limit);
    const [buddy, preference] = await Promise.all([
      await this.postRepository.findFitBuddy({
        userId: myId,
        fitBuddyId: userId,
      }),
      await this.postRepository.getUserPreference(userId),
    ]);

    if (!preference) {
      throw new APIException(
        PostErrorEnum.CAN_NOT_GET_USER_PREFERENCE,
        'CAN_NOT_GET_USER_PREFERENCE',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (buddy && buddy.status === FitBuddiesStatus.BLOCKED) {
      throw new APIException(
        PostErrorEnum.CAN_NOT_SEE_POST,
        'CAN_NOT_SEE_POST',
        HttpStatus.BAD_REQUEST,
      );
    }

    const buddiesIds = new Set<string>().add(buddy?.fitBuddyId || userId);
    const mergeObjArray = new Set<{ userId: string; privacy: string }>();

    if (
      preference?.privacy?.profileVisibility === ProfileVisibilityEnum.LOCKED &&
      buddy?.type !== FitBuddiesType.FIT_BUDDY
    ) {
      throw new APIException(
        PostErrorEnum.USER_PROFILE_IS_LOCKED,
        'USER_PROFILE_IS_LOCKED',
        HttpStatus.BAD_REQUEST,
      );
    }

    mergeObjArray.add({
      userId: buddy?.fitBuddyId || userId,
      privacy: PostPrivacy.PUBLIC,
    });
    if (buddy?.type === FitBuddiesType.FIT_BUDDY) {
      mergeObjArray.add({
        userId: buddy?.fitBuddyId || userId,
        privacy: PostPrivacy.FRIENDS,
      });
    }

    const postList = await this.postRepository.getUserPostList(
      buddiesIds,
      mergeObjArray,
      offset,
      limit,
    );

    return this.helper.serviceResponse.successResponse(
      postList
        ? await this.setLikeCommentsAndTimelinePostsPresignedUrl(postList, myId)
        : [],
    );
  }

  async getOwnPostList(
    myId: string,
    offset?: number,
    limit?: number,
  ): Promise<GetTimelineListResponse> {
    const postList = await this.postRepository.getOwnPostList(
      myId,
      offset,
      limit,
    );
    return this.helper.serviceResponse.successResponse(
      postList
        ? await this.setLikeCommentsAndTimelinePostsPresignedUrl(postList, myId)
        : [],
    );
  }

  async setLikeCommentsAndTimelinePostsPresignedUrl(
    postList: any[],
    userId: string,
  ): Promise<any> {
    try {
      return await Promise.all(
        (postList || []).map(async (post: any) => {
          const comments = await this.postRepository.getPostComments(
            { postId: post.id },
            0,
            2,
          );
          // const likedPost = await this.postRepository.checkLikedPost({
          //   postId: post.id,
          //   userId: userId,
          //   type: PostReactionsType.LIKE,
          // });
          const likedPost = await this.postRepository.checkPostReaction({
            postId: post.id,
            userId: userId,
          });

          // console.log('liked post',likedPost);
          (post.comments as any) = comments;
          (post as any).reactionType = likedPost ? likedPost.type : null;
          (post as any).liked = likedPost ? true : false;

          post = await this.mappedGeneratePresignedUrl(post);
          post &&
            post.type === PostType.SHARE &&
            post?.sharedPost &&
            (post.sharedPost = await this.mappedGeneratePresignedUrl(
              post.sharedPost,
            ));
          return post;
        }),
      );
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }
}
