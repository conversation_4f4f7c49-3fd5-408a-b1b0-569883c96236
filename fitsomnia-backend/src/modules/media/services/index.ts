import { HttpStatus, Injectable } from '@nestjs/common';
import { azureConfig } from 'config/azure';
import {
  FileUploadRequestBody,
  IUploadFileErrorEnum,
  IUploadSingleFileReq,
  UploadFileSuccessResponse,
} from 'models';
import { Helper } from 'src/helper/helper.interface';
import { APIException } from 'src/internal/exception/api.exception';
import { successResponse } from 'src/utils/response';
import { IServiceResponse } from 'src/utils/response/service.response.interface';

@Injectable()
export class MediaService {
  constructor(private helper: Helper) {}

  async uploadPreSignedURL(
    userId: string,
    data: FileUploadRequestBody,
  ): Promise<UploadFileSuccessResponse> {
    try {
      const results = [];
      const responses: any = await Promise.all(
        data.filenames?.map(async (filename: string) => {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          return await new Promise(async (resolve, _reject) => {
            const blobKey =
              this.helper.azureBlobStorageService.generateFileKey(
                userId,
                filename,
                data.featureName?.toLowerCase(),
              );
            resolve(
              filename &&
                blobKey && {
                  filename,
                  s3UploadedURLKey: blobKey, // Keep field name for backward compatibility
                  url: await this.helper.azureBlobStorageService.generatePreSignedUrl(
                    null,
                    data.featureName,
                    blobKey,
                    'putObject',
                  ),
                },
            );
          });
        }),
      );

      for (
        let index = 0, responseLength = responses?.length;
        index < responseLength;
        index++
      ) {
        const singleResponse = responses[index];
        singleResponse &&
          results.push({
            filename: singleResponse.filename,
            presignedUrl: singleResponse.url,
            s3UploadedURLKey: singleResponse.s3UploadedURLKey, // Keep field name for backward compatibility
          });
      }
      return this.helper.serviceResponse.successResponse(results);
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async uploadPreSignedURLWithRoot(
    userId: string,
    data: FileUploadRequestBody,
    root: string,
  ): Promise<UploadFileSuccessResponse> {
    try {
      const results = [];
      const responses: any = await Promise.all(
        data.filenames?.map(async (filename: string) => {
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          return await new Promise(async (resolve, _reject) => {
            const blobKey =
              this.helper.azureBlobStorageService.generateFileKeyWithRoot(
                userId,
                filename,
                root,
                data.featureName?.toLowerCase(),
              );
            resolve(
              filename &&
                blobKey && {
                  filename,
                  s3UploadedURLKey: blobKey, // Keep field name for backward compatibility
                  url: await this.helper.azureBlobStorageService.generatePreSignedUrl(
                    null,
                    data.featureName,
                    blobKey,
                    'putObject',
                  ),
                },
            );
          });
        }),
      );

      for (
        let index = 0, responseLength = responses?.length;
        index < responseLength;
        index++
      ) {
        const singleResponse = responses[index];
        singleResponse &&
          results.push({
            filename: singleResponse.filename,
            presignedUrl: singleResponse.url,
            s3UploadedURLKey: singleResponse.s3UploadedURLKey, // Keep field name for backward compatibility
          });
      }
      return this.helper.serviceResponse.successResponse(results);
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }

  async uploadSinglePublicFile(
    userId: string,
    body: IUploadSingleFileReq,
    featureName = '',
  ): Promise<IServiceResponse<string>> {
    const { file } = body;
    if (!file) {
      throw new APIException(
        IUploadFileErrorEnum.NO_FILE_IS_FOUND,
        'NO_FILE_IS_FOUND',
        HttpStatus.BAD_REQUEST,
      );
    }
 
    const fileType = body.file.mimetype.split('/')[0];
    if (fileType !== 'image' && fileType !== 'video') {
      throw new APIException(
        IUploadFileErrorEnum.INVALID_FILE_TYPE,
        'INVALID_FILE_TYPE',
        HttpStatus.BAD_REQUEST,
      );
    }
    const key = this.helper.azureBlobStorageService.generateFileKey(
      userId,
      file.originalname,
      featureName,
    );
    let blobUrl = await this.helper.azureBlobStorageService.uploadToAzureBlob(
      file,
      key,
      true,
    );
    // replace with public cdn url
    blobUrl = blobUrl.replace(
      azureConfig.azure_pub_url,
      azureConfig.azure_pub_cdn_url,
    );
    if (!blobUrl) {
      throw new APIException(
        IUploadFileErrorEnum.ERROR_IN_UPLOADING_FILE,
        'ERROR_IN_UPLOADING_STORY',
        HttpStatus.CONFLICT,
      );
    }
    return successResponse(null, blobUrl);
  }

  /**
   * Generate a CDN presigned URL for secure content access
   * Available only when using Azure Blob Storage
   */
  async generateCdnPresignedUrl(
    url: string,
    featureName: string,
  ): Promise<string | null> {
    try {
      return this.helper.azureBlobStorageService.generateCdnPresignedUrl(
        url,
        featureName,
      );
    } catch (error) {
      console.log(error.message);
      return null;
    }
  }
}
