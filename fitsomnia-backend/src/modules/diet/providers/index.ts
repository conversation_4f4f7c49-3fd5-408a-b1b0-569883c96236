import { Injectable } from '@nestjs/common';
import { DietService } from '../services';

@Injectable()
export class DietUserInfoProvider {
  constructor(private readonly dietService: DietService) {}

  async getUserInfoFromDietPlan(userId: string): Promise<any> {
    try {
      return await this.dietService.getDietPlanUserInfo(userId);
    } catch (error) {
      // Return null if no diet plan is found instead of throwing
      return null;
    }
  }
}
