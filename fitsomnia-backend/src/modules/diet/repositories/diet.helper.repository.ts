import { Injectable } from '@nestjs/common';
import axios from 'axios';
import { foodVendorConfig } from 'config/foodVendor';

import { globalFoodModel } from 'src/database/diet/globalFood.model';

@Injectable()
export class DietHelperRepository {
  // constructor(private readonly dietRepo: DietRepository) {}
  constructor() {}

  foodMapping(foodArray) {
    const foods = [];
    foodArray.forEach((element) => {
      foods.push({
        id: element.foodId,
        name: element.label,
        knownAs: element.knownAs,
        image: element.image,
        calories: Number(element.nutrients.ENERC_KCAL.toFixed(2)),
        fat: Number(element.nutrients.FAT.toFixed(2)),
        carb: Number(element.nutrients.CHOCDF.toFixed(2)),
        protein: Number(element.nutrients.PROCNT.toFixed(2)),
        brand: element?.brand,
      });
    });

    return foods;
  }

  async getGlobalFoods(
    session: string,
    search: string,
  ): Promise<Object | null> {
    try {
      const { url, app_id, app_key } = foodVendorConfig;

      let searchUrl = `${url}&app_id=${app_id}&app_key=${app_key}`;
      if (session) searchUrl = searchUrl + `&session=${session}`;
      if (search) searchUrl = searchUrl + `&ingr=${search}`;

      const response = await axios.get(searchUrl);
      const foodArray = response.data?.hints?.map((x) => x.food);

      let nextSession = null; //next session means next page offset

      if (response.data?._links?.next?.href) {
        const url = new URL(response.data?._links?.next?.href);
        nextSession = url?.searchParams.get('session');
      }

      let foods = this.foodMapping(foodArray);

      foods = foods.filter(
        (v, i, a) => a.findIndex((v2) => v2.id === v.id) === i,
      );

      const result = {
        foods: foods,
        nextSession: nextSession || null,
      };

      return result;
    } catch (error) {
      console.log(error.response);
    }
  }

  async getNewGlobalFoods(
    session: string,
    search: string,
  ): Promise<Object | null> {
    try {
      let query = null;
      if (search) {
        query = { name: { $regex: search, $options: 'i' } };
      }

      let customFoods = [];
      if (!session) {
        customFoods = await globalFoodModel
          .find(query)
          .select('-_id -createdAt -updatedAt')
          .lean();
      }

      const { url, app_id, app_key } = foodVendorConfig;

      let searchUrl = `${url}&app_id=${app_id}&app_key=${app_key}`;
      if (session) searchUrl = searchUrl + `&session=${session}`;
      if (search) searchUrl = searchUrl + `&ingr=${search}`;

      const response = await axios.get(searchUrl);

      const measureWithQualifiers = response.data?.hints?.map((hint) => {
        const {
          foodId,
          label,
          nutrients,
          category,
          categoryLabel,
          image,
          brand,
        } = hint.food;

        return {
          id: foodId,
          name: label,
          brand,
          // category,
          // categoryLabel,
          // image,
          nutrients, // Original nutrients per 100g
          measures: (() => {
            // Create base 100g measure once
            const base100gMeasure = {
              label: '100 gram',
              weight: 100,
              nutrients: nutrients,
            };

            // Get all other measures
            const otherMeasures = hint.measures.flatMap((measure) => {
              // Calculate nutrients for this measure using the formula
              const measureNutrients = this.calculateNutrientsForWeight(
                nutrients,
                measure.weight,
              );

              const baseMeasure = {
                label: measure.label,
                weight: measure.weight,
                nutrients: measureNutrients,
              };

              // Calculate nutrients for qualifiers using the same formula
              const qualifierMeasures =
                measure.qualified?.map((qualifier) => ({
                  label: qualifier.qualifiers?.[0]?.label,
                  weight: qualifier.weight,
                  nutrients: this.calculateNutrientsForWeight(
                    nutrients,
                    qualifier.weight,
                  ),
                })) || [];

              return [baseMeasure, ...qualifierMeasures];
            });

            // Return array with 100g measure first, followed by other measures
            return [base100gMeasure, ...otherMeasures];
          })(),
        };
      });

      const foods = !session
        ? [...customFoods, ...measureWithQualifiers]
        : measureWithQualifiers;

      let nextSession = null;

      if (response.data?._links?.next?.href) {
        const url = new URL(response.data?._links?.next?.href);
        nextSession = url?.searchParams.get('session');
      }

      // const foodArray = response.data?.hints?.map((x) => x.food);
      // let foods = this.foodMapping(foodArray);
      // let foods = this.foodMapping(foodArray);

      // foods = foods.filter(
      //   (v, i, a) => a.findIndex((v2) => v2.id === v.id) === i,
      // );

      const result = {
        foods: foods,
        nextSession: nextSession || null,
      };

      return result;
    } catch (error) {
      console.log(error.response);
      return null;
    }
  }

  calculateNutrientsForWeight(baseNutrients: any, measureWeight: number) {
    // Formula: (Measure Weight / 100) × Nutrient in 100 grams
    const factor = measureWeight / 100;
    return {
      ENERC_KCAL: parseFloat((baseNutrients.ENERC_KCAL * factor).toFixed(2)),
      FAT: parseFloat((baseNutrients.FAT * factor).toFixed(2)),
      CHOCDF: parseFloat((baseNutrients.CHOCDF * factor).toFixed(2)),
      PROCNT: parseFloat((baseNutrients.PROCNT * factor).toFixed(2)),
    };
  }

  getPreviousData(previousHistory, consumedFood) {
    let previousCalories = 0;
    if (previousHistory && previousHistory.totalCalorieConsumed) {
      previousCalories = previousHistory.totalCalorieConsumed;
    }

    const ind = previousHistory.consumedFoods.findIndex(
      (x) => x.id === consumedFood.id,
    );
    previousCalories =
      previousCalories -
      previousHistory.consumedFoods[ind].calories *
        previousHistory.consumedFoods[ind].servingSize;
    previousHistory.consumedFoods[ind] = consumedFood;
    return {
      previousCalories,
      previousHistory,
    };
  }

  calculateMacroCalories(food: {
    fat: number;
    carb: number;
    protein: number;
    servingSize?: number;
  }) {
    const servingSize = food.servingSize || 1;
    return {
      fatCalories: parseFloat((food.fat * 9 * servingSize).toFixed(2)),
      carbCalories: parseFloat((food.carb * 4 * servingSize).toFixed(2)),
      proteinCalories: parseFloat((food.protein * 4 * servingSize).toFixed(2)),
      totalCalories: parseFloat(
        (
          (food.fat * 9 + food.carb * 4 + food.protein * 4) *
          servingSize
        ).toFixed(2),
      ),
    };
  }

  getUpdatedTotalCalorie(previousCalories, consumedFood) {
    // return previousCalories + consumedFood.calories * consumedFood.servingSize;
    const calories = this.calculateMacroCalories({
      fat: consumedFood.fat,
      carb: consumedFood.carb,
      protein: consumedFood.protein,
      servingSize: consumedFood.servingSize,
    });
    return previousCalories + calories.totalCalories;
  }

  getCaloriesAfterDelete(previousCalories, consumedFood) {
    const calories = this.calculateMacroCalories({
      fat: consumedFood.fat,
      carb: consumedFood.carb,
      protein: consumedFood.protein,
      servingSize: consumedFood.servingSize,
    });
    return previousCalories - calories.totalCalories;
    // return previousCalories - consumedFood.calories * consumedFood.servingSize;
  }
}
