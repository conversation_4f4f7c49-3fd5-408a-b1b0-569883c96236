import { Injectable } from '@nestjs/common';
import {
  Activity,
  ConsumedFood,
  CreateCustomFoodRequestBody,
  CreateGlobalFoodRequestBody,
  CustomFood,
  DietPlan,
  FoodSource,
  WeeklyGoal,
} from 'models';
import { CreateDietPlanRequestBody } from 'models/diet/dietPlan/createDietPlan';
import { UpdateDietPlanRequestBody } from 'models/diet/dietPlan/updateDietPlan';
import { activityModel } from 'src/database/diet/activity.model';
import { customFoodModel } from 'src/database/diet/customFood.model';
import { dietHistoryModel } from 'src/database/diet/dietHistory.model';
import { dietPlanModel } from 'src/database/diet/dietPlan.model';
import { globalFoodModel } from 'src/database/diet/globalFood.model';
import { weeklyGoalModel } from 'src/database/diet/weeklyGoal.model';
import { FoodModel } from 'src/database/food/food.model';
import { DietHistory } from 'src/entity/dietHistory';
import { Food } from 'src/entity/food';
import { GlobalFood } from 'src/entity/globalFood';
import { DietHelperRepository } from './diet.helper.repository';

@Injectable()
export class DietRepository {
  constructor(private readonly dietHelperRepo: DietHelperRepository) {}
  async createCustomFood(
    customFood: CreateCustomFoodRequestBody,
  ): Promise<CustomFood | null> {
    try {
      const food = await customFoodModel.create(customFood);
      const newCustomFood = food?.toObject();
      delete newCustomFood?._id;
      return newCustomFood;
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async getCustomFood(query: Record<string, any>): Promise<CustomFood | null> {
    try {
      return await customFoodModel
        .findOne(query)
        .select('-_id -createdAt -updatedAt')
        .lean();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async getLocalFood(query: Record<string, any>): Promise<Food | null> {
    try {
      const food = await FoodModel.findOne(query)
        .select('-_id -createdAt -updatedAt')
        .lean();

      if (food) {
        return {
          ...food,
          name: food.foodNameEnglish,
        } as Food & { name: string };
      }

      // console.log('food:', food);

      return food;
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async createGlobalCustomFood(
    customFood: CreateGlobalFoodRequestBody,
  ): Promise<GlobalFood | null> {
    try {
      const food = await globalFoodModel.create(customFood);
      const newCustomFood = food?.toObject();
      delete newCustomFood?._id;
      return newCustomFood;
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }
  // async getGlobalCustomFood(query: Record<string, any>): Promise<GlobalFood | null> {
  //   try {
  //     return await globalFoodModel
  //       .findOne(query)
  //       .select('-_id -createdAt -updatedAt')
  //       .lean();
  //   } catch (error: any) {
  //     console.log(error.message);
  //     return null;
  //   }
  // }

  async getGlobalFood(query: Record<string, any>): Promise<GlobalFood | null> {
    try {
      return await globalFoodModel
        .findOne(query)
        .select('-_id -createdAt -updatedAt')
        .lean();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async getAllCustomFoods(
    userId: string,
    offset?: number,
    limit?: number,
    search?: string,
  ): Promise<CustomFood[] | null> {
    try {
      let query = null;
      if (search) {
        query = { userId: userId, name: { $regex: search, $options: 'i' } };
      } else {
        query = { userId: userId };
      }
      return await customFoodModel
        .find(query)
        .skip(offset)
        .limit(limit)
        .select('-_id -createdAt -updatedAt')
        .lean();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async getAllGlobalCustomFoods(
    offset?: number,
    limit?: number,
    search?: string,
  ): Promise<CustomFood[] | null> {
    try {
      let query = null;
      if (search) {
        query = { name: { $regex: search, $options: 'i' } };
      }
      return await globalFoodModel
        .find(query)
        .skip(offset)
        .limit(limit)
        .select('-_id -createdAt -updatedAt')
        .lean();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async getActivityList(
    offset?: number,
    limit?: number,
  ): Promise<Activity[] | null> {
    try {
      return await activityModel
        .find()
        .skip(offset)
        .limit(limit)
        .select('-_id')
        .lean();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async getWeeklyGoalList(
    offset?: number,
    limit?: number,
  ): Promise<WeeklyGoal[] | null> {
    try {
      return await weeklyGoalModel
        .find()
        .skip(offset)
        .limit(limit)
        .select('-_id')
        .lean();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async getActivityById(id: string): Promise<Activity | null> {
    try {
      return await activityModel.findOne({ id: id }).select('-_id').lean();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async createDietPlan(
    dietPlan: CreateDietPlanRequestBody,
  ): Promise<DietPlan | null> {
    console.log(
      `create diet plan for UserId ${
        dietPlan.user.id
      } , timestamp: ${new Date().toISOString()}`,
    );

    try {
      const plan = await dietPlanModel.create(dietPlan);

      const newPlan = plan?.toObject();
      delete newPlan?._id;
      return newPlan;
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  updatedDietPlanMapping(
    dietPlan: UpdateDietPlanRequestBody,
    existedDietPlan: DietPlan,
  ): UpdateDietPlanRequestBody {
    const newUserData = dietPlan?.user;
    const existedUserData = existedDietPlan.user;

    console.log(
      `Update diet plan for UserId ${existedDietPlan.user.id} , DietId ${
        existedDietPlan.id
      } ,timestamp:  ${new Date().toISOString()}`,
    );

    const updatedDietPlan: UpdateDietPlanRequestBody = {
      user: {
        id: existedUserData?.id,
        age: newUserData?.age ? newUserData.age : existedUserData?.age,
        weight: newUserData?.weight
          ? newUserData.weight
          : existedUserData?.weight,
        targetWeight: newUserData?.targetWeight
          ? newUserData.targetWeight
          : existedUserData?.targetWeight,
        weightType: newUserData?.weightType
          ? newUserData.weightType
          : existedUserData?.weightType,
        height: newUserData?.height
          ? newUserData.height
          : existedUserData?.height,
        heightType: newUserData?.heightType
          ? newUserData.heightType
          : existedUserData?.heightType,
        gender: newUserData?.gender
          ? newUserData.gender
          : existedUserData?.gender,
        activityId: newUserData?.activityId
          ? newUserData?.activityId
          : existedUserData?.activityId,
        weeklyGoal: newUserData?.weeklyGoal
          ? newUserData?.weeklyGoal
          : existedUserData?.weeklyGoal,
      },
      type: dietPlan?.type ? dietPlan.type : existedDietPlan?.type,
      startDate: dietPlan?.startDate
        ? dietPlan.startDate
        : existedDietPlan?.startDate,
      endDate: dietPlan?.endDate ? dietPlan.endDate : existedDietPlan?.endDate,
    };

    return updatedDietPlan;
  }

  async updateDietPlan(
    dietId: string,
    dietPlan: UpdateDietPlanRequestBody,
    existedDietPlan: DietPlan,
  ): Promise<DietPlan | null> {
    const updatedDietPlan: UpdateDietPlanRequestBody =
      this.updatedDietPlanMapping(dietPlan, existedDietPlan);

    try {
      const plan = await dietPlanModel.findOneAndUpdate(
        { id: dietId },
        updatedDietPlan,
        { new: true },
      );

      const newPlan = plan?.toObject();
      delete newPlan?._id;
      return newPlan;
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async deleteDietPlan(dietPlanId: string): Promise<DietPlan | null> {
    console.log(
      `Delete diet plans for Diet ID: ${dietPlanId}, timestamp: ${new Date().toISOString()}`,
    );
    try {
      return await dietPlanModel
        .findOne({ id: dietPlanId })
        .select('-_id')
        .lean();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async getDietPlansByUserId(userId: string): Promise<DietPlan[] | null> {
    console.log(
      `Getting diet plans for user ID: ${userId}, timestamp: ${new Date().toISOString()}`,
    );
    try {
      return await dietPlanModel
        .find({ 'user.id': userId })
        .select('-_id')
        .lean();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async getDietPlanById(dietId: string): Promise<DietPlan | null> {
    console.log(
      `Getting diet plans for Diet ID: ${dietId}, timestamp: ${new Date().toISOString()}`,
    );
    try {
      return await dietPlanModel.findOne({ id: dietId }).select('-_id').lean();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async getDietPlanUserInfoByUserId(userId: string): Promise<any> {
    try {
      return await dietPlanModel
        .findOne({ 'user.id': userId })
        .select('-_id user startDate endDate type') // Include needed fields
        .lean();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async getDietHistoryByUserId(
    userId: string,
    date: Date,
  ): Promise<DietHistory | null> {
    try {
      const nextDay = new Date(new Date(date).getTime() + 24 * 60 * 60 * 1000);
      const start = new Date(date);
      start.setUTCHours(0, 0, 0, 0);
      return await dietHistoryModel
        .findOne({ userId: userId, date: { $gte: start, $lt: nextDay } })
        .select('-_id')
        .lean();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async getDietHistoryById(id: string): Promise<DietHistory | null> {
    try {
      return await dietHistoryModel.findOne({ id: id }).select('-_id').lean();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async dietHistoryWithConsumedFoodId(
    dietHistoryId: string,
    consumedFoodId: string,
  ): Promise<DietHistory | null> {
    try {
      const dietHistory = await dietHistoryModel
        .findOne({
          id: dietHistoryId,
          consumedFoods: { $elemMatch: { id: consumedFoodId } },
        })
        .lean();
      return dietHistory;
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async checkFoodSrcExists(
    source: FoodSource,
    foodId: string,
  ): Promise<CustomFood | null> {
    console.log('Checking food source existence:', source, foodId);
    let food = null;
    if (source == FoodSource.CUSTOM) {
      const customFoods = await this.getCustomFood({ id: foodId });
      food = customFoods;
    } else if (source == FoodSource.GLOBAL) {
      //Global food or food vendor
      try {
        const globalFoods = await this.dietHelperRepo.getGlobalFoods(
          '',
          foodId,
        );
        food = globalFoods['foods']?.[0];
      } catch (error) {
        if (error?.response?.data?.error == 'not_found') {
          food = null;
        }
      }
    } else if (source == FoodSource.LOCAL) {
      const localFoods = await this.getLocalFood({ id: foodId });
      food = localFoods;
    }
    return food;
  }

  async addFoodToDietHistory(
    userId: string,
    dietHistory: DietHistory,
    previousHistory: DietHistory,
    consumedFood: ConsumedFood,
  ): Promise<DietHistory | null> {
    let previousCalories = 0;
    if (previousHistory && previousHistory.totalCalorieConsumed) {
      previousCalories = previousHistory.totalCalorieConsumed;
    }

    try {
      const nextDay = new Date(
        new Date(dietHistory.date).getTime() + 24 * 60 * 60 * 1000,
      );
      const start = new Date(dietHistory?.date);
      start.setUTCHours(0, 0, 0, 0);
      const updatedHistory = await dietHistoryModel.findOneAndUpdate(
        { userId: userId, date: { $gte: start, $lt: nextDay } },
        {
          $set: {
            totalCalorieConsumed:
              previousCalories +
              consumedFood.calories * consumedFood.servingSize,
          },
          $push: { consumedFoods: consumedFood },
        },
        { returnDocument: 'after' },
      );

      const newHistory = updatedHistory?.toObject();
      delete newHistory?._id;
      return newHistory;
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async updateFoodToDietHistory(
    userId: string,
    consumedFood: ConsumedFood,
    previousHistory: DietHistory,
  ): Promise<DietHistory | null> {
    const data = this.dietHelperRepo.getPreviousData(
      previousHistory,
      consumedFood,
    );
    const totalCalorie = this.dietHelperRepo.getUpdatedTotalCalorie(
      data?.previousCalories,
      consumedFood,
    );
    previousHistory = data?.previousHistory;

    try {
      const nextDay = new Date(
        new Date(previousHistory.date).getTime() + 24 * 60 * 60 * 1000,
      );
      const start = new Date(previousHistory?.date);
      start.setUTCHours(0, 0, 0, 0);
      const updatedHistory = await dietHistoryModel.findOneAndUpdate(
        { userId: userId, date: { $gte: start, $lt: nextDay } },
        {
          $set: {
            totalCalorieConsumed: totalCalorie,
            consumedFoods: previousHistory.consumedFoods,
          },
        },
        { returnDocument: 'after' },
      );

      const newHistory = updatedHistory?.toObject();
      delete newHistory?._id;
      return newHistory;
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async deleteFoodFromDietHistory(
    dietHistoryId: string,
    dietHistory: DietHistory,
    consumedFood: ConsumedFood,
  ): Promise<DietHistory | null> {
    const totalCalorie = this.dietHelperRepo.getCaloriesAfterDelete(
      dietHistory?.totalCalorieConsumed,
      consumedFood,
    );
    try {
      return await dietHistoryModel
        .findOneAndUpdate(
          { id: dietHistoryId },
          {
            $pull: {
              consumedFoods: { id: consumedFood?.id },
            },
            $set: {
              totalCalorieConsumed: totalCalorie,
            },
          },
          { returnDocument: 'after' },
        )
        .select('-_id')
        .lean();
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async createDietHistory(
    userId: string,
    date: Date,
  ): Promise<DietHistory | null> {
    try {
      const history = await dietHistoryModel.create({ userId, date });
      return history;
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }

  async addWaterConsumption(
    dietHistoryId: string,
    waterAmount: number,
    previousWaterAmount = 0,
  ): Promise<DietHistory | null> {
    try {
      const dietHistory = await dietHistoryModel.findOneAndUpdate(
        { id: dietHistoryId },
        {
          $set: {
            waterConsumption: previousWaterAmount + waterAmount,
          },
        },
        { returnDocument: 'after' },
      );

      const newHistory = dietHistory?.toObject();
      delete newHistory?._id;
      return newHistory;
    } catch (error: any) {
      console.log(error.message);
      return null;
    }
  }
}
