import { Injectable, Logger } from '@nestjs/common';
import { azureConfig } from 'config/azure';
import { Helper } from 'src/helper/helper.interface';
import { deepCasting } from "src/internal/casting/object.casting";
import { FoodScannerAIProvider } from 'src/modules/ai/providers/food-scanner.provider';
import { FoodScannerDto, FoodScannerResponseDto } from '../dtos/scanner.dto';
import { FoodRecognitionResult, ScannerRequest } from '../entities/scanner.entitiy';
import { ScannerRepository } from '../repositories/scanner.repository';

@Injectable()
export class FoodScannerService {
  private readonly logger = new Logger(FoodScannerService.name);

  constructor(
    private readonly foodScannerAIProvider: FoodScannerAIProvider,
    private readonly helper: Helper,
    private readonly scannerRepository: ScannerRepository,
  ) {}

  async analyzeFoodImage(request: ScannerRequest): Promise<FoodScannerResponseDto> {
    const totalStart = Date.now();
    let aiStart: number | null = null;
    let aiEnd: number | null = null;
    try {
      // Validate input
      this.validateRequest(request);
      // Prepare storage path and upload image
      const blobKey = this.generateBlobKey(request.userId, request.file.originalname);
      const imageUrl = await this.uploadImageAndGetCdnUrl(request.file, blobKey);
      // Call AI with the image URL
      aiStart = Date.now();
      const aiResult = await this.analyzeWithAI(imageUrl);
      aiEnd = Date.now();
      if (!aiResult) {
        throw new Error('No response from AI service');
      }
      const items = aiResult.items.map((item) => deepCasting(FoodScannerDto, item));
      
      // Always save scan information to database (including non-food images)
      await this.scannerRepository.createFoodScannerInfo({
        userId: request.userId,
        imageUrl,
        foodInfo: items, // Will be empty array for non-food images
        isFoodDetected: aiResult.isFoodDetected,
      });
      return {
        data: items,
        isFoodDetected: aiResult.isFoodDetected
      } as FoodScannerResponseDto;
    } catch (error) {
      this.logger.error('Error analyzing food image:', error);
      throw error;
    } finally {
      const totalMs = Date.now() - totalStart;
      const totalSec = (totalMs / 1000).toFixed(2);
      this.logger.log(`FoodScanner total time: ${totalMs} ms (${totalSec}s)`);
      if (aiStart) {
        const aiMs = (aiEnd ?? Date.now()) - aiStart;
        const aiSec = (aiMs / 1000).toFixed(2);
        this.logger.log(`FoodScanner AI time: ${aiMs} ms (${aiSec}s)`);
      }
    }
  }

  private validateRequest(
    request: ScannerRequest,
  ): asserts request is ScannerRequest & { userId: string; file: Express.Multer.File } {
    if (!request?.file) {
      throw new Error('No file provided');
    }
    if (!request?.userId) {
      throw new Error('userId is required to upload image');
    }
  }

  private generateBlobKey(userId: string, originalName: string): string {
    const root = 'food-scanner';
    return this.helper.azureBlobStorageService.generateFileKeyWithRoot(
      userId,
      originalName,
      root,
    );
  }

  private async uploadImageAndGetCdnUrl(
    file: Express.Multer.File,
    blobKey: string,
  ): Promise<string> {
    let blobUrl = await this.helper.azureBlobStorageService.uploadToAzureBlob(
      file,
      blobKey,
      true,
    );
    if (!blobUrl) {
      throw new Error('Failed to upload image to storage');
    }
    // replace with public CDN url
    blobUrl = blobUrl.replace(
      azureConfig.azure_pub_url,
      azureConfig.azure_pub_cdn_url,
    );
    return blobUrl;
  }

  private async analyzeWithAI(imageUrl: string): Promise<{ items: FoodRecognitionResult[], isFoodDetected: boolean }> {
    return this.foodScannerAIProvider.analyzeFoodImage({
      imageUrl,
      prompt: this.buildFoodAnalysisPrompt(),
      options: {
        temperature: 0.3,
        max_tokens: 800,
        response_format: { type: 'json_object' },
        deployment: 'gpt-4.1',
      },
    });
  }

  private buildFoodAnalysisPrompt(): string {
    return `You are a food recognition AI. Analyze this food image and return a strict JSON object with a single "item" object and "isFoodDetected" boolean field
{"item":{"name":"","knownAs":"","servingSize":0,"calories":0,"carb":0,"fat":0,"protein":0,"confidence":0,"foodItems":[]},"isFoodDetected":true}
- All values must be in correct type (numbers, strings, array of strings, boolean).
- servingSize should be 1.
- knownAs represents the Bengali name of the food.
- foodItems is the array of food items visible in the image.
- Combine nutritional values if multiple foods are present into one comprehensive summary.
- isFoodDetected should be true if the image contains identifiable food items, false if no food is detected (e.g., people, objects, scenery, non-food items).
- If isFoodDetected is false, return: {"item":null,"isFoodDetected":false}
`;
  }
}