import { Injectable } from "@nestjs/common";
import { FoodScannerEntityModel } from "src/database/diet/food-scanner.model";
import { FoodScannerEntity } from "../entities/scanner.entitiy";

@Injectable()
export class ScannerRepository {

  async createFoodScannerInfo(info: FoodScannerEntity): Promise<FoodScannerEntity> {
    const foodScannerInfo = new FoodScannerEntityModel(info);
    const foodScannerInfoDocument = await foodScannerInfo.save();
    return foodScannerInfoDocument.toObject();
  }

  async getFoodScannerInfoByUserId(userId: string): Promise<FoodScannerEntity | null> {
    const searchQuery = { userId };
    const foodScannerInfoDoc = await FoodScannerEntityModel.findOne(searchQuery).exec();
    return foodScannerInfoDoc !== null ? foodScannerInfoDoc.toObject() : null;
  }
}