import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import {
  FoodSource,
  MealType,
  UpdateDietHistoryRequestBody,
  UpdateDietHistorySuccessResponse,
} from 'models';
import { DietHistoryDto } from './dietHistory.dto';

export class UpdateDietHistoryRequestDto
  implements UpdateDietHistoryRequestBody
{
  @ApiProperty()
  @IsString()
  consumedFoodId: string;

  @ApiProperty({
    required: true,
    enum: MealType,
    isArray: false,
  })
  @IsEnum(MealType)
  @IsOptional()
  @IsString()
  mealType: MealType;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  foodId: string;

  @ApiProperty({ required: true, type: () => Number })
  @IsNumber()
  @Type(() => Number)
  servingSize?: number;

  @ApiProperty({
    required: false,
    enum: FoodSource,
    isArray: false,
  })
  @IsOptional()
  @IsEnum(FoodSource)
  @IsOptional()
  @IsString()
  source: FoodSource;
}

export class UpdateDietHistorySuccessResponseDto
  implements UpdateDietHistorySuccessResponse
{
  @ApiProperty({ type: DietHistoryDto })
  @Type(() => DietHistoryDto)
  @ValidateNested()
  @IsObject()
  data: DietHistoryDto;
}
