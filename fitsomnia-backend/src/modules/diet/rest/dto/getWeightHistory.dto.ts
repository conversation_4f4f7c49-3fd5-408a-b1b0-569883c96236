import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsOptional, IsString } from 'class-validator';

export class GetWeightHistoryQueryDto {
  @ApiProperty({
    description: 'Start date filter',
    required: false,
    example: '2025-07-01T00:00:00.000Z',
  })
  @IsDateString()
  @IsOptional()
  startDate?: string;

  @ApiProperty({
    description: 'End date filter',
    required: false,
    example: '2025-08-06T23:59:59.999Z',
  })
  @IsDateString()
  @IsOptional()
  endDate?: string;

  @ApiProperty({
    description: 'Time period',
    enum: ['week', 'month', '3months', '6months', 'year'],
    required: false,
    example: 'month',
  })
  @IsString()
  @IsOptional()
  period?: string;
}

export class GetWeightHistorySuccessResponseDto {
  @ApiProperty()
  data: {
    weightLogs: Array<{
      id: string;
      weight: number;
      weightType: string;
      date: string;
      notes: string;
      createdAt: string;
    }>;
    summary: {
      totalEntries: number;
      latestWeight: number;
      startingWeight: number;
      weightChange: number;
      averageWeight: number;
    };
  };
}
