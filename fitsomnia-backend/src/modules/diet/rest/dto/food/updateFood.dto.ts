import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsOptional, IsString, ValidateNested } from 'class-validator';

class MineralsDto {
  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  ca?: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  fe?: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  mg?: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  p?: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  k?: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  na?: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  zn?: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  cu?: string;
}

class VitaminsDto {
  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  vitaminA?: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  retinol?: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  betaCarotene?: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  vitaminD?: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  vitaminE?: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  thiamin?: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  riboflavin?: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  niacin?: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  vitaminB6?: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  folate?: string;

  @Expose()
  @ApiProperty()
  @IsString()
  @IsOptional()
  vitaminC?: string;
}

export class UpdateFoodDto {
  @ApiProperty({ description: 'Food name in English' })
  @IsString()
  @IsOptional()
  foodNameEnglish?: string;

  @ApiProperty({ description: 'Food name in Bengali' })
  @IsString()
  @IsOptional()
  foodNameBengali?: string;

  @ApiProperty({ description: 'Food image URL' })
  @IsString()
  @IsOptional()
  image?: string;

  @ApiProperty({ description: 'Food calories' })
  @IsString()
  @IsOptional()
  calories?: string;

  @ApiProperty({ description: 'Food protein content' })
  @IsString()
  @IsOptional()
  protein?: string;

  @ApiProperty({ description: 'Food fat content' })
  @IsString()
  @IsOptional()
  fat?: string;

  @ApiProperty({ description: 'Food carbohydrate content' })
  @IsString()
  @IsOptional()
  carb?: string;

  @ApiProperty({ description: 'Food minerals' })
  @ValidateNested()
  @Type(() => MineralsDto)
  @IsOptional()
  minerals?: MineralsDto;

  @ApiProperty({ description: 'Food vitamins' })
  @ValidateNested()
  @Type(() => VitaminsDto)
  @IsOptional()
  vitamins?: VitaminsDto;
}

export class UpdateFoodResponseDto {
  @Expose()
  @ApiProperty({ description: 'Food ID' })
  id?: string;

  @Expose()
  @ApiProperty({ description: 'Food name in English' })
  foodNameEnglish?: string;
  @Expose()
  @ApiProperty({ description: 'Food name in Bengali' })
  foodNameBengali?: string;

  @Expose()
  @ApiProperty({ description: 'Food image URL' })
  image?: string;

  @Expose()
  @ApiProperty({ description: 'Food calories' })
  calories?: number;

  @Expose()
  @ApiProperty({ description: 'Food protein content' })
  protein?: string;

  @Expose()
  @ApiProperty({ description: 'Food fat content' })
  fat?: string;

  @Expose()
  @ApiProperty({ description: 'Food carbohydrate content' })
  carb?: string;

  @Expose()
  @ApiProperty({ description: 'Food minerals content' })
  minerals?: MineralsDto;

  @Expose()
  @ApiProperty({ description: 'Food vitamins content' })
  vitamins?: VitaminsDto;
}

export class UpdateSuccessFoodResponseDtoForAdmin {
  @ApiProperty({ type: UpdateFoodResponseDto })
  data: UpdateFoodResponseDto;
}
