import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsDateString,
  IsNumber,
  IsObject,
  IsString,
  ValidateNested,
} from 'class-validator';
import { FoodSource, MealType } from 'models';
import {
  CreateAiDietHistoryRequestBody,
  CreateDietHistorySuccessResponse,
} from 'models/diet/dietHistory/addDietHistory';
import { DietHistoryDto } from './dietHistory.dto';

export class CreateAIDietHistoryRequestDto
  implements CreateAiDietHistoryRequestBody
{
  @ApiProperty({
    required: true,
    example: MealType.BREAKFAST,
    examples: [
      MealType.BREAKFAST,
      MealType.LUNCH,
      MealType.DINNER,
      MealType.SNACKS,
    ],
  })
  @IsString()
  mealType: MealType;

  @ApiProperty()
  @IsDateString()
  date: Date;

  // @ApiProperty({ required: false })
  // @IsString()
  // foodId: string;

  @ApiProperty({ required: false })
  @IsString()
  name: string;

  @ApiProperty({ required: false })
  @IsString()
  knownAs: string;

  @ApiProperty({ required: false })
  @IsString()
  calories: string;

  @ApiProperty({ required: false })
  @IsString()
  carb: string;

  @ApiProperty({ required: false })
  @IsString()
  protein: string;

  @ApiProperty({ required: false })
  @IsString()
  fat: string;

  @ApiProperty()
  @IsNumber()
  servingSize: number;

  @ApiProperty({
    required: true,
    example: FoodSource.AI,
  })
  @IsString()
  source: FoodSource;
}

export class CreateDietHistorySuccessResponseDto
  implements CreateDietHistorySuccessResponse
{
  @ApiProperty({ type: DietHistoryDto })
  @Type(() => DietHistoryDto)
  @ValidateNested()
  @IsObject()
  data: DietHistoryDto;
}
