import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsDateString,
  IsNumber,
  IsObject,
  IsString,
  ValidateNested,
} from 'class-validator';
import { FoodSource, MealType } from 'models';
import {
  CreateDietHistoryRequestBody,
  CreateDietHistorySuccessResponse,
} from 'models/diet/dietHistory/addDietHistory';
import { DietHistoryDto } from './dietHistory.dto';

export class CreateDietHistoryRequestDto
  implements CreateDietHistoryRequestBody
{
  @ApiProperty({
    required: true,
    example: MealType.BREAKFAST,
    examples: [
      MealType.BREAKFAST,
      MealType.LUNCH,
      MealType.DINNER,
      MealType.SNACKS,
    ],
  })
  @IsString()
  mealType: MealType;

  @ApiProperty()
  @IsDateString()
  date: Date;

  @ApiProperty({ required: false })
  @IsString()
  foodId: string;

  @ApiProperty()
  @IsNumber()
  servingSize: number;

  @ApiProperty({
    required: true,
    example: FoodSource.GLOBAL,
    examples: [FoodSource.GLOBAL, FoodSource.CUSTOM, FoodSource.LOCAL],
  })
  @IsString()
  source: FoodSource;
}

export class CreateDietHistorySuccessResponseDto
  implements CreateDietHistorySuccessResponse
{
  @ApiProperty({ type: DietHistoryDto })
  @Type(() => DietHistoryDto)
  @ValidateNested()
  @IsObject()
  data: DietHistoryDto;
}
