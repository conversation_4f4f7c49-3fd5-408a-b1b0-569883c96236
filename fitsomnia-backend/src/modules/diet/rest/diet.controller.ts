import {
  Body,
  Controller,
  Delete,
  Get,
  HttpStatus,
  Param,
  Patch,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { RolesGuard } from 'src/authentication/guards/auth.guard';
import { User as UserInfo } from 'src/decorators/auth.decorator';
import { User } from 'src/entity/user';
import { DietService } from '../services';
import { WeightLogService } from '../services/weightLog.service';
import { CreateAIDietHistoryRequestDto } from './dto/createAiDietHistory.dto';
import {
  DeleteDietPlanRequestDto,
  DeleteDietPlanSuccessResponseDto,
} from './dto/deleteDiet.dto';
import { GetAllGlobalFoodsQueryDto } from './dto/getGlobalFoodList.dto';
import { GetAllWeeklyGoalsSuccessResponseDto } from './dto/getWeeklyGoalList.dto';
import {
  GetWeightHistoryQueryDto,
  GetWeightHistorySuccessResponseDto,
} from './dto/getWeightHistory.dto';
import {
  CreateCustomFoodRequestDto,
  CreateCustomFoodSuccessResponseDto,
  CreateDietHistoryRequestDto,
  CreateDietHistorySuccessResponseDto,
  CreateDietPlanRequestDto,
  CreateDietPlanSuccessResponseDto,
  CreateGlobalFoodRequestDto,
  CreateWaterConsumptionRequestDto,
  CreateWaterConsumptionSuccessResponseDto,
  DeleteFoodFromHistoryQueryDto,
  DeleteFoodFromHistorySuccessResponseDto,
  GetAllActivitiesSuccessResponseDto,
  GetAllCustomFoodsQueryDto,
  GetAllCustomFoodsSuccessResponseDto,
  GetAllDietHistorySuccessResponseDto,
  GetAllDietPlansSuccessResponseDto,
  GetAllGlobalCustomFoodsQueryDto,
  GetDietHistoryQueryDto,
  GetDietStatusSuccessResponseDto,
  UpdateDietHistoryRequestDto,
} from './dto/index';
import {
  LogWeightRequestDto,
  LogWeightSuccessResponseDto,
} from './dto/logWeight.dto';
import {
  UpdateDietPlanRequestDto,
  UpdateDietPlanSuccessResponseDto,
} from './dto/updateDietPlan.dto';
import { GetWeightProgressSuccessResponseDto } from './dto/weightProgress.dto';

@Controller('diet')
@ApiBearerAuth()
@UseGuards(new RolesGuard(['user']))
@ApiTags('Diet API')
export class DietController {
  constructor(
    private dietService: DietService,
    private weightLogService: WeightLogService,
  ) {}

  @Post('custom-foods')
  @ApiResponse({
    description: 'Create Custom Food Success Response',
    type: CreateCustomFoodSuccessResponseDto,
    status: HttpStatus.CREATED,
  })
  @ApiOperation({
    summary: 'Creating Custom Foods',
  })
  async createCustomFood(
    @UserInfo() user: User,
    @Body() customFood: CreateCustomFoodRequestDto,
  ) {
    return await this.dietService.createCustomFood(user.id, customFood);
  }

  @Get('custom-foods')
  @ApiResponse({
    description: 'Get Custom Food List API',
    type: GetAllCustomFoodsSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Getting All Custom Foods',
  })
  async getCustomFoodList(
    @UserInfo() user: User,
    @Query() condition: GetAllCustomFoodsQueryDto,
  ) {
    return await this.dietService.getAllCustomFoods(user.id, condition);
  }

  @Post('global-custom-foods')
  @ApiResponse({
    description: 'Create Custom global Food Success Response',
    type: CreateCustomFoodSuccessResponseDto,
    status: HttpStatus.CREATED,
  })
  @ApiOperation({
    summary: 'Creating custom global Foods',
  })
  async createGlobalFood(@Body() globalFood: CreateGlobalFoodRequestDto) {
    return await this.dietService.createGlobalFood(globalFood);
  }

  @Get('global-custom-foods')
  @ApiResponse({
    description: 'Get Global Custom Food List API',
    type: GetAllCustomFoodsSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Getting All Global Custom Foods',
  })
  async getGlobalCustomFoodList(
    @Query() condition: GetAllGlobalCustomFoodsQueryDto,
  ) {
    return await this.dietService.getAllGlobalCustomFoods(condition);
  }

  @Get('global-foods')
  @ApiResponse({
    description: 'Get All Food List API',
    type: GetAllCustomFoodsSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Getting All Foods',
  })
  async getAllFoodList(@Query() condition: GetAllGlobalFoodsQueryDto) {
    return await this.dietService.getAllGlobalFoods(condition);
  }

  @Get('global-foods/v2')
  @ApiResponse({
    description: 'Get All Food List API for V2',
    type: GetAllCustomFoodsSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Getting All Foods V2',
  })
  async getNewAllFoodList(@Query() condition: GetAllGlobalFoodsQueryDto) {
    return await this.dietService.getNewAllGlobalFoods(condition);
  }

  @Get('activities')
  @ApiResponse({
    description: 'Get Activity List API',
    type: GetAllActivitiesSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Getting All Activities',
  })
  async getActivityList() {
    return await this.dietService.getAllActivities();
  }

  @Get('weekly-goals')
  @ApiResponse({
    description: 'Get Weekly Goal List API',
    type: GetAllWeeklyGoalsSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Getting All Weekly Goals',
  })
  async getWeeklyGoalList() {
    return await this.dietService.getAllWeeklyGoals();
  }

  @Post('diet-plan')
  @ApiResponse({
    description: 'Create Diet Plan Success Response',
    type: CreateDietPlanSuccessResponseDto,
    status: HttpStatus.CREATED,
  })
  @ApiOperation({
    summary: 'Creating diet plan',
  })
  async createDietPlan(
    @UserInfo() user: User,
    @Body() dietPlan: CreateDietPlanRequestDto,
  ) {
    return await this.dietService.createDietPlan(user.id, dietPlan);
  }

  @Put('diet-plan/:dietId')
  @ApiResponse({
    description: 'Update Diet Plan Success Response',
    type: UpdateDietPlanSuccessResponseDto,
    status: HttpStatus.CREATED,
  })
  @ApiOperation({
    summary: 'Updating diet plan',
  })
  async updateDietPlan(
    @Param('dietId') dietId: string,
    @Body() dietPlan: UpdateDietPlanRequestDto,
  ) {
    return await this.dietService.updateDietPlan(dietId, dietPlan);
  }

  @Get('diet-plans')
  @ApiResponse({
    description: 'Get Diet Plan by UserId API',
    type: GetAllDietPlansSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Getting All Diet Plans',
  })
  async getDietPlansByUserId(@UserInfo() user: User) {
    return await this.dietService.getAllDietPlanListByUserId(user.id);
  }

  @Delete('diet-plan')
  @ApiResponse({
    description: 'Delete Diet Plan API',
    type: DeleteDietPlanSuccessResponseDto,
    status: HttpStatus.CREATED,
  })
  @ApiOperation({
    summary: 'Delete Diet Plan By DietPlanId',
  })
  async deleteDietPlan(@Query() data: DeleteDietPlanRequestDto) {
    return 'Delete Diet Plan API test';
  }

  @Get('diet-status/:dietId')
  @ApiResponse({
    description: 'Get Diet Plan Status by Id',
    type: GetDietStatusSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Getting Diet Plan Status by Id',
  })
  async getDietStatusById(@Param('dietId') dietId: string) {
    return await this.dietService.getDietPlanStatus(dietId);
  }

  @Get('ideal-calories/:dietId')
  @ApiResponse({
    description: 'Get Ideal Calories and Suitable Calories by DietId',
    type: GetDietStatusSuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Getting Ideal Calories and Suitable Calories by DietId',
  })
  async getIdealCalories(@Param('dietId') dietId: string) {
    return await this.dietService.getIdealCalories(dietId);
  }

  @Get('diet-history')
  @ApiResponse({
    description: 'Get Diet History by UserId API',
    type: GetAllDietHistorySuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Getting All Diet History of loggedIn user by date',
  })
  async getDietHistoryByUserId(
    @UserInfo() user: User,
    @Query() condition: GetDietHistoryQueryDto,
  ) {
    return await this.dietService.getDietHistoryListByUserId(
      user.id,
      condition,
    );
  }

  @Post('diet-history/food')
  @ApiResponse({
    description: 'Add Consumed Food to Diet History',
    type: CreateDietHistorySuccessResponseDto,
    status: HttpStatus.CREATED,
  })
  @ApiOperation({
    summary: 'Add Consumed Food to Diet History of a user for a date',
  })
  async addFoodDietHistory(
    @UserInfo() user: User,
    @Body() dietHistory: CreateDietHistoryRequestDto,
  ) {
    return await this.dietService.addFoodToDietHistory(user.id, dietHistory);
  }

  @Post('diet-history/ai/food')
  @ApiResponse({
    description: 'Add Ai Consumed Food to Diet History',
    type: CreateDietHistorySuccessResponseDto,
    status: HttpStatus.CREATED,
  })
  @ApiOperation({
    summary: 'Add Ai Consumed Food to Diet History of a user for a date',
  })
  async addAiFoodDietHistory(
    @UserInfo() user: User,
    @Body() dietHistory: CreateAIDietHistoryRequestDto,
  ) {
    return await this.dietService.addAiFoodToDietHistory(user.id, dietHistory);
  }

  @Put('diet-history/food/:dietHistoryId')
  @ApiResponse({
    description: 'Update Consumed Food of loggedin User',
    type: CreateDietHistorySuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Update Consumed Food of loggedin User',
  })
  async updateConsumedFoodInHistory(
    @UserInfo() user: User,
    @Param('dietHistoryId') dietHistoryId: string,
    @Query() dietHistory: UpdateDietHistoryRequestDto,
  ) {
    return await this.dietService.updateConsumedFoodInDietHistory(
      user.id,
      dietHistoryId,
      dietHistory,
    );
  }

  @Delete('diet-history/food')
  @ApiResponse({
    description: 'Delete Consumed Food from Diet History API',
    type: DeleteFoodFromHistorySuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Delete Consumed Food from Diet History',
  })
  async deleteFoodFromHistory(
    @Query() conditions: DeleteFoodFromHistoryQueryDto,
  ) {
    return await this.dietService.deleteFoodFromDietHistory(conditions);
  }

  @Patch('water-consumption')
  @ApiResponse({
    description: 'Modify water consumption',
    type: CreateWaterConsumptionSuccessResponseDto,
    status: HttpStatus.CREATED,
  })
  @ApiOperation({
    summary: 'Modify water consumption of a user for a date',
  })
  async addWaterConsumption(
    @UserInfo() user: User,
    @Body() waterConsumption: CreateWaterConsumptionRequestDto,
  ) {
    return await this.dietService.addWaterConsumption(
      user.id,
      waterConsumption,
    );
  }

  @Post('weight-log')
  @ApiResponse({
    description: 'Log User Weight Success Response',
    type: LogWeightSuccessResponseDto,
    status: HttpStatus.CREATED,
  })
  @ApiOperation({
    summary: 'Log user weight',
  })
  async logWeight(
    @UserInfo() user: User,
    @Body() weightData: LogWeightRequestDto,
  ) {
    return await this.weightLogService.logUserWeight(user.id, weightData);
  }

  @Get('weight-history')
  @ApiResponse({
    description: 'Get Weight History Success Response',
    type: GetWeightHistorySuccessResponseDto,
    status: HttpStatus.OK,
  })
  @ApiOperation({
    summary: 'Get user weight history',
  })
  async getWeightHistory(
    @UserInfo() user: User,
    @Query() query: GetWeightHistoryQueryDto,
  ) {
    return await this.weightLogService.getWeightHistory(user.id, query);
  }
}
