import { HttpStatus, Injectable } from '@nestjs/common';
import {
  CreateAiDietHistoryRequestBody,
  CreateCustomFoodErrorMessage,
  CreateCustomFoodRequestBody,
  CreateCustomFoodResponse,
  CreateDietHistoryErrorMessage,
  CreateDietHistoryRequestBody,
  CreateDietHistorySuccessResponse,
  // NotificationModule,
  // NotificationType,
  // NotificationCategory
  CreateDietPlanErrorMessage,
  CreateDietPlanRequestBody,
  CreateDietPlanResponse,
  CreateGlobalFoodErrorMessage,
  CreateGlobalFoodRequestBody,
  CreateWaterConsumptionRequestBody,
  CreateWaterConsumptionSuccessResponse,
  DeleteDietPlanErrorMessage,
  DeleteDietPlanSuccessResponse,
  DeleteFoodFromHistoryErrorMessages,
  DeleteFoodFromHistoryQuery,
  DeleteFoodFromHistorySuccessResponse,
  DietMessage,
  GelAllWeeklyGoalsSuccessResponse,
  GetAllActivitiesSuccessResponse,
  GetAllCustomFoodsQuery,
  GetAllCustomFoodsSuccessResponse,
  GetAllDietHistorySuccessResponse,
  GetAllDietPlansErrorMessages,
  GetAllDietPlansSuccessResponse,
  GetAllGlobalCustomFoodsQuery,
  GetAllGlobalCustomFoodsSuccessResponse,
  GetAllGlobalFoodsQuery,
  GetDietHistoryQuery,
  GetDietStatusErrorMessages,
  GetDietStatusSuccessResponse,
  GetIdealCalorieErrorMessages,
  GetIdealCalorieSuccessResponse,
  UpdateDietHistoryErrorMessage,
  UpdateDietHistoryRequestBody,
  UpdateDietHistorySuccessResponse,
  UpdateDietPlanErrorMessage,
  UpdateDietPlanRequestBody,
  UpdateDietPlanResponse,
} from 'models';
import { ConsumedFood } from 'src/entity/dietHistory';
import { Helper } from 'src/helper/helper.interface';
import { APIException } from 'src/internal/exception/api.exception';
import { DietRepository } from '../repositories';
import { DietHelperRepository } from '../repositories/diet.helper.repository';
//import { NotificationHelperService } from 'src/modules/notification/services/helper.notification.service';
import { randomUUID } from 'crypto';
import { TaskProgressService } from 'src/modules/task/task-progress.service';
import { UserRepository } from 'src/modules/user/repositories';
import { FoodRepository } from '../repositories/food.repository';
import { DietHelperService } from './diet.helper.service';

@Injectable()
export class DietService {
  constructor(
    private dietRepo: DietRepository,
    private helper: Helper,
    private dietHelperRepository: DietHelperRepository,
    //private notificationHelperService: NotificationHelperService,
    private userRepository: UserRepository,
    private dietHelperService: DietHelperService,
    private readonly taskProgressService: TaskProgressService,

    private readonly foodRepository: FoodRepository,
  ) {}

  async createCustomFood(
    userId: string,
    customFood: CreateCustomFoodRequestBody,
  ): Promise<CreateCustomFoodResponse> {
    const doesCustomFoodExist = await this.dietRepo.getCustomFood({
      name: customFood.name,
      userId: userId,
    });
    if (doesCustomFoodExist) {
      throw new APIException(
        CreateCustomFoodErrorMessage.CUSTOM_FOOD_EXISTS,
        'CUSTOM_FOOD_EXISTS',
        HttpStatus.BAD_REQUEST,
      );
    }
    customFood.userId = userId;
    const newCustomFood = await this.dietRepo.createCustomFood(customFood);
    if (!customFood) {
      throw new APIException(
        CreateCustomFoodErrorMessage.CAN_NOT_CREATE_CUSTOM_FOOD,
        'CAN_NOT_CREATE_CUSTOM_FOOD',
        HttpStatus.BAD_REQUEST,
      );
    }
    return this.helper.serviceResponse.successResponse(newCustomFood);
  }

  async createGlobalFood(
    customFood: CreateGlobalFoodRequestBody,
  ): Promise<any> {
    console.log('custom food', customFood);
    const doesCustomFoodExist = await this.dietRepo.getGlobalFood({
      name: customFood.name,
    });
    if (doesCustomFoodExist) {
      throw new APIException(
        CreateGlobalFoodErrorMessage.GLOBAL_FOOD_EXISTS,
        'GLOBAL_FOOD_EXISTS',
        HttpStatus.BAD_REQUEST,
      );
    }

    const newCustomFood = await this.dietRepo.createGlobalCustomFood(
      customFood,
    );
    if (!customFood) {
      throw new APIException(
        CreateGlobalFoodErrorMessage.CAN_NOT_CREATE_GLOBAL_FOOD,
        'CAN_NOT_CREATE_GLOBAL_FOOD',
        HttpStatus.BAD_REQUEST,
      );
    }
    return this.helper.serviceResponse.successResponse(newCustomFood);
  }

  async getAllCustomFoods(
    userId: string,
    conditions: GetAllCustomFoodsQuery,
  ): Promise<GetAllCustomFoodsSuccessResponse> {
    const { offset, limit, search } = conditions;
    const customFoods = await this.dietRepo.getAllCustomFoods(
      userId,
      offset,
      limit,
      search,
    );

    return this.helper.serviceResponse.successResponse(customFoods);
  }

  async getAllGlobalCustomFoods(
    conditions: GetAllGlobalCustomFoodsQuery,
  ): Promise<GetAllGlobalCustomFoodsSuccessResponse> {
    const { offset, limit, search } = conditions;
    const customFoods = await this.dietRepo.getAllGlobalCustomFoods(
      offset,
      limit,
      search,
    );

    return this.helper.serviceResponse.successResponse(customFoods);
  }

  async getAllGlobalFoods(conditions: GetAllGlobalFoodsQuery) {
    try {
      const [localFoods, globalFoods] = await Promise.all([
        !conditions?.session
          ? this.foodRepository.getAllFoodsBySearch(conditions?.search)
          : Promise.resolve([]),
        this.dietHelperRepository.getGlobalFoods(
          conditions?.session,
          conditions?.search,
        ),
      ]);

      const transformedLocalFoods = localFoods.map((food) => ({
        id: food.id,
        name: food.foodNameEnglish,
        knownAs: food.foodNameBengali || '',
        image: food.image || null,
        calories: parseFloat(food.calories) || 0,
        fat: parseFloat(food.fat) || 0,
        carb: parseFloat(food.carb) || 0,
        protein: parseFloat(food.protein) || 0,
        source: 'local',
      }));

      const globalFoodsArray = (globalFoods as any)?.foods || [];
      const transformedGlobalFoods = globalFoodsArray.map((food) => ({
        ...food,
        knownAs: food.knownAs || '',
        source: 'global',
      }));
      const nextSession = (globalFoods as any)?.nextSession || null;

      return {
        foods: transformedLocalFoods.concat(transformedGlobalFoods),
        nextSession: nextSession,
      };
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
  async getNewAllGlobalFoods(conditions: GetAllGlobalFoodsQuery) {
    try {
      return this.dietHelperRepository.getNewGlobalFoods(
        conditions?.session,
        conditions?.search,
      );
    } catch (error) {
      console.log(error);
    }
  }

  async getAllActivities(): Promise<GetAllActivitiesSuccessResponse> {
    const activities = await this.dietRepo.getActivityList();

    return this.helper.serviceResponse.successResponse(activities);
  }

  async getAllWeeklyGoals(): Promise<GelAllWeeklyGoalsSuccessResponse> {
    const goals = await this.dietRepo.getWeeklyGoalList();

    return this.helper.serviceResponse.successResponse(goals);
  }

  async createDietPlan(
    userId: string,
    dietPlan: CreateDietPlanRequestBody,
  ): Promise<CreateDietPlanResponse> {
    dietPlan.user.id = userId;

    const prevDay = new Date(dietPlan?.endDate);
    prevDay.setDate(prevDay.getDate() - 1);

    const user = await this.userRepository.findUser({
      id: userId,
    });

    const newDietPlan = await this.dietRepo.createDietPlan(dietPlan);

    if (!newDietPlan) {
      throw new APIException(
        CreateDietPlanErrorMessage.CAN_NOT_CREATE_DIET_PLAN,
        'CAN_NOT_CREATE_DIET_PLAN',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Notification Payload
    // const payload = {
    //   recipient: user,
    //   createdBy:{
    //     name:'admin',
    //   },
    //   title: 'Diet ending soon!' ,
    //   content: `Your diet plan is ending soon.`,
    //   module: NotificationModule.DIET,
    //   type: NotificationType.DIET_ENDING,
    //   date: prevDay,
    //   category: NotificationCategory.DIET_ENDING
    // };

    // await this.notificationHelperService.sendNotifications(payload);
    await this.taskProgressService.trackDietCreation(userId);

    return this.helper.serviceResponse.successResponse(newDietPlan);
  }

  async updateDietPlan(
    dietId: string,
    dietPlan: UpdateDietPlanRequestBody,
  ): Promise<UpdateDietPlanResponse> {
    const dietPlanExists = await this.dietRepo.getDietPlanById(dietId);

    if (!dietPlanExists) {
      throw new APIException(
        UpdateDietPlanErrorMessage.DIET_PLAN_NOT_FOUND,
        'DIET_PLAN_NOT_FOUND',
        HttpStatus.BAD_REQUEST,
      );
    }

    const updatedDietPlan = await this.dietRepo.updateDietPlan(
      dietId,
      dietPlan,
      dietPlanExists,
    );
    return this.helper.serviceResponse.successResponse(updatedDietPlan);
  }

  async deleteDietPlan(
    dietPlanId: string,
  ): Promise<DeleteDietPlanSuccessResponse> {
    const diet = await this.dietRepo.deleteDietPlan(dietPlanId);
    if (!diet) {
      throw new APIException(
        DeleteDietPlanErrorMessage.CAN_NOT_REMOVE_DIET_PLAN,
        'CAN_NOT_REMOVE_DIET_PLAN',
        HttpStatus.BAD_REQUEST,
      );
    }
    return this.helper.serviceResponse.successResponse({
      message: DietMessage.REMOVE_DIET_PLAN_SUCCESSFULLY,
    });
  }

  async getAllDietPlanListByUserId(
    userId: string,
  ): Promise<GetAllDietPlansSuccessResponse> {
    const dietPlans = await this.dietRepo.getDietPlansByUserId(userId);

    if (!dietPlans) {
      throw new APIException(
        GetAllDietPlansErrorMessages.CAN_NOT_GET_ALL_DIET_PLANS,
        'CAN_NOT_GET_ALL_DIET_PLANS',
        HttpStatus.NOT_FOUND,
      );
    }
    return this.helper.serviceResponse.successResponse(dietPlans);
  }

  async getDietPlanStatus(
    dietPlanId: string,
  ): Promise<GetDietStatusSuccessResponse> {
    const dietPlan = await this.dietRepo.getDietPlanById(dietPlanId);

    if (!dietPlan) {
      throw new APIException(
        GetDietStatusErrorMessages.CAN_NOT_GET_DIET,
        'CAN_NOT_GET_DIET',
        HttpStatus.NOT_FOUND,
      );
    }

    const difference = dietPlan?.endDate?.getTime() - new Date().getTime();
    const days = Math.ceil(difference / (1000 * 3600 * 24));

    const dietStatus = {
      id: dietPlanId,
      dietOngoing: days > 0 ? true : false,
      daysRemaining: days > 0 ? days : 0,
    };
    return this.helper.serviceResponse.successResponse(dietStatus);
  }

  async getDietPlanUserInfo(userId: string): Promise<any> {
    const dietPlan = await this.dietRepo.getDietPlanUserInfoByUserId(userId);

    if (!dietPlan) {
      throw new APIException(
        'No diet plan found for this user',
        'NO_DIET_PLAN_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    const userInfo = dietPlan.user;

    if (!userInfo) {
      throw new APIException(
        'User information not found in diet plan',
        'USER_INFO_NOT_FOUND',
        HttpStatus.NOT_FOUND,
      );
    }

    return this.helper.serviceResponse.successResponse(userInfo);
  }

  async getIdealCalories(
    dietPlanId: string,
  ): Promise<GetIdealCalorieSuccessResponse> {
    const dietPlan = await this.dietRepo.getDietPlanById(dietPlanId);

    // console.log(dietPlan);

    if (!dietPlan) {
      throw new APIException(
        GetIdealCalorieErrorMessages.CAN_NOT_GET_DIET,
        'CAN_NOT_GET_DIET',
        HttpStatus.NOT_FOUND,
      );
    }

    const activity = await this.dietRepo.getActivityById(
      dietPlan?.user?.activityId,
    );

    if (!activity) {
      throw new APIException(
        GetIdealCalorieErrorMessages.CAN_NOT_GET_ACTIVITY,
        'CAN_NOT_GET_ACTIVITY',
        HttpStatus.NOT_FOUND,
      );
    }

    let idealCalorie = 0;
    let caloriesToLossPerDay = 0;
    let caloriesToGainPerDay = 0;
    let caloriesIntakePerDay = 0;

    const user = dietPlan?.user;
    // console.log("original user",user)

    // Convert weight and target weight to pounds if in kilograms

    if (user.weightType === 'kg') {
      user.weight = user.weight * 2.20462; // 1 kg = 2.20462 lbs
      user.targetWeight = user.targetWeight * 2.20462;
    }
    // Convert height to inches
    if (user.heightType === 'cm') {
      user.height = user.height / 2.54; // 1 inch = 2.54 cm
    } else if (user.heightType === 'ft') {
      user.height = user.height * 12; // 1 ft = 12 inches
    }

    // console.log(user.weight,user.height)

    const type = dietPlan?.type;
    const difference =
      dietPlan?.endDate?.getTime() - dietPlan?.startDate?.getTime();
    const days = Math.ceil(difference / (1000 * 3600 * 24));

    //For men: BMR=66 + (6.2 x weight) + (12.7 x height) – (6.76 x age)

    //For women: BMR=655.1 + (4.35 x weight) + (4.7 x height) – (4.7 x age)

    //idealCalorie=BMR*activityLevel

    //  console.log('user', user);
    //  console.log('user weight', user?.weight);
    // console.log('user height', user?.height);

    if (user?.gender == 'male') {
      idealCalorie =
        (66 + 6.2 * user?.weight + 12.7 * user?.height - 6.76 * user?.age) *
        activity?.value;
    }
    //1800calorie
    else if (user?.gender == 'female') {
      idealCalorie =
        (655.1 + 4.35 * user?.weight + 4.7 * user?.height - 4.7 * user?.age) *
        activity?.value;
    }

    idealCalorie = idealCalorie;

    caloriesIntakePerDay = idealCalorie;

    let macros = this.dietHelperService.getMacros(
      user?.weight,
      caloriesIntakePerDay,
    );

    //it is needed to loss 500 calorie per day if you want to loss 1 pound weight in a week

    //it is needed to gain extra  500 calorie per day if you want to gain 1 pound weight in a week

    // to maintain you have to take the idealCalorie

    if (type == 'loss' && days > 0) {
      const weightLoss = user?.weight - user?.targetWeight;

      caloriesToLossPerDay = (3500 * 7 * weightLoss) / days / days;

      caloriesIntakePerDay = idealCalorie - caloriesToLossPerDay;

      macros = this.dietHelperService.getMacros(
        user?.weight,
        caloriesIntakePerDay,
      );
    } else if (type == 'gain' && days > 0) {
      const weightGain = user?.targetWeight - user?.weight;
      caloriesToGainPerDay = (3500 * 7 * weightGain) / days / days;
      caloriesIntakePerDay = idealCalorie + caloriesToGainPerDay;
      macros = this.dietHelperService.getMacros(
        user?.weight,
        caloriesIntakePerDay,
      );
    }

    const res = {
      idealCalorie: parseFloat(idealCalorie.toFixed(2)),
      caloriesToLossPerDay: parseFloat(caloriesToLossPerDay.toFixed(2)),
      caloriesToGainPerDay: parseFloat(caloriesToGainPerDay.toFixed(2)),
      caloriesIntakePerDay: parseFloat(caloriesIntakePerDay.toFixed(2)),
      type,
      macros,
    };

    return this.helper.serviceResponse.successResponse(res);
  }

  // async getIdealCalories(
  //   dietPlanId: string,
  // ): Promise<GetIdealCalorieSuccessResponse> {
  //   const dietPlan = await this.dietRepo.getDietPlanById(dietPlanId);

  //   // console.log(dietPlan);

  //   if (!dietPlan) {
  //     throw new APIException(
  //       GetIdealCalorieErrorMessages.CAN_NOT_GET_DIET,
  //       'CAN_NOT_GET_DIET',
  //       HttpStatus.NOT_FOUND,
  //     );
  //   }

  //   const activity = await this.dietRepo.getActivityById(
  //     dietPlan.user.activityId,
  //   );

  //   if (!activity) {
  //     throw new APIException(
  //       GetIdealCalorieErrorMessages.CAN_NOT_GET_ACTIVITY,
  //       'CAN_NOT_GET_ACTIVITY',
  //       HttpStatus.NOT_FOUND,
  //     );
  //   }

  //   let { weight, height, targetWeight } = dietPlan.user;

  //   // Convert weight and target weight to pounds if in kilograms

  //   if (dietPlan.user.weightType === 'kg') {
  //     weight = weight * 2.20462; // 1 kg = 2.20462 lbs
  //     targetWeight = targetWeight * 2.20462;
  //   }
  //   // Convert height to inches
  //   if (dietPlan.user.heightType === 'cm') {
  //     height = height / 2.54; // 1 inch = 2.54 cm
  //   } else if (dietPlan.user.heightType === 'ft') {
  //     height = height * 12; // 1 ft = 12 inches
  //   }

  //   // console.log(weight, targetWeight, height)

  //   let idealCalorie = 0;
  //   const { age, gender } = dietPlan.user;

  //   if (gender === 'male') {
  //     idealCalorie =
  //       (66 + 6.2 * weight + 12.7 * height - 6.76 * age) * activity.value;
  //   } else if (gender === 'female') {
  //     idealCalorie =
  //       (655.1 + 4.35 * weight + 4.7 * height - 4.7 * age) * activity.value;
  //   }

  //   let caloriesToLossPerDay = 0;
  //   let caloriesIntakePerDay = idealCalorie;

  //   const type = dietPlan.type;
  //   const difference =
  //     dietPlan.endDate.getTime() - dietPlan.startDate.getTime();
  //   const days = Math.ceil(difference / (1000 * 3600 * 24));

  //   if (type === 'loss' && days > 0) {
  //     const weightLoss = weight - targetWeight;
  //     caloriesToLossPerDay = (3500 * 7 * weightLoss) / days / days;
  //     caloriesIntakePerDay = idealCalorie - caloriesToLossPerDay;
  //   } else if (type === 'gain' && days > 0) {
  //     const weightGain = targetWeight - weight;
  //     const caloriesToGainPerDay = (3500 * 7 * weightGain) / days / days;
  //     caloriesIntakePerDay = idealCalorie + caloriesToGainPerDay;
  //   }

  //   const macros = this.dietHelperService.getMacros(
  //     weight,
  //     caloriesIntakePerDay,
  //   );

  //   const res = {
  //     idealCalorie: parseFloat(idealCalorie.toFixed(2)),
  //     caloriesToLossPerDay: parseFloat(caloriesToLossPerDay.toFixed(2)),
  //     caloriesIntakePerDay: parseFloat(caloriesIntakePerDay.toFixed(2)),
  //     type,
  //     macros,
  //   };

  //   return this.helper.serviceResponse.successResponse(res);
  // }

  async getDietHistoryListByUserId(
    userId: string,
    conditions: GetDietHistoryQuery,
  ): Promise<GetAllDietHistorySuccessResponse> {
    const { date } = conditions;
    const dietHistory = await this.dietRepo.getDietHistoryByUserId(
      userId,
      date,
    );

    if (!dietHistory) {
      return this.helper.serviceResponse.successResponse({});
    }

    return this.helper.serviceResponse.successResponse(
      this.dietHelperService.mapDietHistory(dietHistory),
    );
  }

  async addFoodToDietHistory(
    userId: string,
    dietHistory: CreateDietHistoryRequestBody,
  ): Promise<CreateDietHistorySuccessResponse> {
    const dietHistoryExists = await this.dietRepo.getDietHistoryByUserId(
      userId,
      dietHistory.date,
    );

    const food = await this.dietRepo.checkFoodSrcExists(
      dietHistory.source,
      dietHistory.foodId,
    );

    if (!food) {
      throw new APIException(
        CreateDietHistoryErrorMessage.FOOD_NOT_EXISTS,
        'FOOD_NOT_EXISTS',
        HttpStatus.BAD_REQUEST,
      );
    }

    if (!dietHistoryExists) {
      await this.dietRepo.createDietHistory(userId, dietHistory.date);
    }

    const consumedFood: ConsumedFood =
      this.dietHelperService.formatFoodForAdding(food, dietHistory);

    const newDietHistory = await this.dietRepo.addFoodToDietHistory(
      userId,
      dietHistory,
      dietHistoryExists,
      consumedFood,
    );

    if (!newDietHistory) {
      throw new APIException(
        CreateDietHistoryErrorMessage.CAN_NOT_ADD_CONSUMED_FOOD,
        'CAN_NOT_ADD_CONSUMED_FOOD',
        HttpStatus.BAD_REQUEST,
      );
    }
    await this.taskProgressService.trackMealAdded(
      userId,
      dietHistory.mealType,
      dietHistory.date,
    );
    return this.helper.serviceResponse.successResponse(
      this.dietHelperService.mapDietHistory(newDietHistory),
    );
  }

  async addAiFoodToDietHistory(
    userId: string,
    dietHistory: CreateAiDietHistoryRequestBody,
  ): Promise<CreateDietHistorySuccessResponse> {
    const dietHistoryExists = await this.dietRepo.getDietHistoryByUserId(
      userId,
      dietHistory.date,
    );

    const food = {
      id: randomUUID(),
      name: dietHistory.name,
      calories: parseFloat(dietHistory.calories),
      carb: parseFloat(dietHistory.carb),
      protein: parseFloat(dietHistory.protein),
      fat: parseFloat(dietHistory.fat),
    };

    if (!dietHistoryExists) {
      await this.dietRepo.createDietHistory(userId, dietHistory.date);
    }

    const consumedFood: ConsumedFood =
      this.dietHelperService.formatFoodForAdding(food, dietHistory);

    const newDietHistory = await this.dietRepo.addFoodToDietHistory(
      userId,
      dietHistory,
      dietHistoryExists,
      consumedFood,
    );

    if (!newDietHistory) {
      throw new APIException(
        CreateDietHistoryErrorMessage.CAN_NOT_ADD_CONSUMED_FOOD,
        'CAN_NOT_ADD_CONSUMED_FOOD',
        HttpStatus.BAD_REQUEST,
      );
    }
    await this.taskProgressService.trackMealAdded(
      userId,
      dietHistory.mealType,
      dietHistory.date,
    );
    return this.helper.serviceResponse.successResponse(
      this.dietHelperService.mapDietHistory(newDietHistory),
    );
  }

  async updateConsumedFoodInDietHistory(
    userId: string,
    dietHistoryId: string,
    consumedFood: UpdateDietHistoryRequestBody,
  ): Promise<UpdateDietHistorySuccessResponse> {
    //fetch diet hsitory
    const dietHistory = await this.dietRepo.dietHistoryWithConsumedFoodId(
      dietHistoryId,
      consumedFood?.consumedFoodId,
    );
    if (!dietHistory) {
      throw new APIException(
        UpdateDietHistoryErrorMessage.CAN_NOT_FIND_CONSUMED_FOOD,
        'CAN_NOT_FIND_CONSUMED_FOOD',
        HttpStatus.BAD_REQUEST,
      );
    }

    const { foodId, source, consumedFoodId } = consumedFood;

    if ((foodId && !source) || (!foodId && source)) {
      throw new APIException(
        UpdateDietHistoryErrorMessage.CAN_NOT_UPDATE_FOODID,
        'CAN_NOT_FIND_CONSUMED_FOOD',
        HttpStatus.BAD_REQUEST,
      );
    }

    const existedConsumedFood = dietHistory.consumedFoods.find(
      (x) => x.id === consumedFoodId,
    );

    const food = await this.dietRepo.checkFoodSrcExists(source, foodId);

    if (!food) {
      throw new APIException(
        CreateDietHistoryErrorMessage.FOOD_NOT_EXISTS,
        'FOOD_NOT_EXISTS',
        HttpStatus.BAD_REQUEST,
      );
    }

    const updatedConsumedFood: ConsumedFood =
      this.dietHelperService.formatFoodForUpdating(
        food,
        consumedFood,
        existedConsumedFood,
      );

    //Update consumed food in diet history

    const newDietHistory = await this.dietRepo.updateFoodToDietHistory(
      userId,
      updatedConsumedFood,
      dietHistory,
    );

    if (!newDietHistory) {
      throw new APIException(
        UpdateDietHistoryErrorMessage.CAN_NOT_UPDATE_CONSUMED_FOOD,
        'CAN_NOT_UPDATE_CONSUMED_FOOD',
        HttpStatus.BAD_REQUEST,
      );
    }
    return this.helper.serviceResponse.successResponse(
      this.dietHelperService.mapDietHistory(newDietHistory),
    );
  }

  async deleteFoodFromDietHistory(
    conditions: DeleteFoodFromHistoryQuery,
  ): Promise<DeleteFoodFromHistorySuccessResponse> {
    const { dietHistoryId, consumedFoodId } = conditions;

    const dietHistory = await this.dietRepo.getDietHistoryById(dietHistoryId);

    const consumedFood = dietHistory.consumedFoods.find(
      (x) => x.id == consumedFoodId,
    );

    if (!consumedFood) {
      throw new APIException(
        DeleteFoodFromHistoryErrorMessages.CAN_NOT_FIND_CONSUMED_FOOD,
        'CAN_NOT_DELETE_CONSUMED_FOOD',
        HttpStatus.NOT_FOUND,
      );
    }

    const newDietHistory = await this.dietRepo.deleteFoodFromDietHistory(
      dietHistoryId,
      dietHistory,
      consumedFood,
    );

    if (!newDietHistory) {
      throw new APIException(
        DeleteFoodFromHistoryErrorMessages.CAN_NOT_DELETE_CONSUMED_FOOD,
        'CAN_NOT_DELETE_CONSUMED_FOOD',
        HttpStatus.BAD_REQUEST,
      );
    }

    return this.helper.serviceResponse.successResponse(
      this.dietHelperService.mapDietHistory(newDietHistory),
    );
  }

  async addWaterConsumption(
    userId: string,
    conditions: CreateWaterConsumptionRequestBody,
  ): Promise<CreateWaterConsumptionSuccessResponse> {
    const { date, waterAmount } = conditions;
    let dietHistory = await this.dietRepo.getDietHistoryByUserId(userId, date);
    if (!dietHistory) {
      dietHistory = await this.dietRepo.createDietHistory(userId, date);
    }

    const updatedDietHistory = await this.dietRepo.addWaterConsumption(
      dietHistory.id,
      waterAmount,
      dietHistory.waterConsumption,
    );
    await this.taskProgressService.trackWaterConsumption(userId);
    return this.helper.serviceResponse.successResponse(
      this.dietHelperService.mapDietHistory(updatedDietHistory),
    );
  }
}
