// Add these imports
import { HttpStatus, Injectable } from '@nestjs/common';
import { Helper } from 'src/helper/helper.interface';
import { APIException } from 'src/internal/exception/api.exception';
import { DietRepository } from '../repositories';
import { WeightLogRepository } from '../repositories/weightLog.repository';
import {
  GetWeightHistoryQueryDto,
  GetWeightHistorySuccessResponseDto,
} from '../rest/dto/getWeightHistory.dto';
import {
  LogWeightRequestDto,
  LogWeightSuccessResponseDto,
} from '../rest/dto/logWeight.dto';

@Injectable()
export class WeightLogService {
  constructor(
    private readonly weightLogRepo: WeightLogRepository,
    private readonly dietRepo: DietRepository,
    private readonly helper: Helper,
  ) {}

  async logUserWeight(
    userId: string,
    weightData: LogWeightRequestDto,
  ): Promise<LogWeightSuccessResponseDto> {
    try {
      const weightLog = await this.weightLogRepo.createWeightLog({
        userId,
        weight: weightData.weight,
        weightType: weightData.weightType || 'kg',
        date: weightData.date ? new Date(weightData.date) : new Date(),
        notes: weightData.notes || '',
      });

      if (!weightLog) {
        throw new APIException(
          'Failed to log weight',
          'WEIGHT_LOG_FAILED',
          HttpStatus.INTERNAL_SERVER_ERROR,
        );
      }

      // Update current diet plan with new weight if it's the latest entry
      const currentDietPlan = await this.dietRepo.getDietPlanUserInfoByUserId(
        userId,
      );
      if (currentDietPlan) {
        const latestLog = await this.weightLogRepo.getLatestWeightLog(userId);
        if (latestLog && latestLog.id === weightLog.id) {
          await this.dietRepo.updateDietPlan(
            currentDietPlan.id,
            {
              user: {
                ...currentDietPlan.user,
                weight: weightData.weight,
                weightType: weightData.weightType || 'kg',
              },
            },
            currentDietPlan,
          );
        }
      }

      return this.helper.serviceResponse.successResponse(weightLog);
    } catch (error) {
      if (error instanceof APIException) {
        throw error;
      }
      console.error('Error logging user weight:', error);
      throw new APIException(
        'Failed to log weight',
        'WEIGHT_LOG_ERROR',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getWeightHistory(
    userId: string,
    query: GetWeightHistoryQueryDto,
  ): Promise<GetWeightHistorySuccessResponseDto> {
    try {
      let startDate: Date | undefined;
      let endDate: Date | undefined;

      if (query.startDate && query.endDate) {
        startDate = new Date(query.startDate);
        endDate = new Date(query.endDate);
      } else if (query.period) {
        endDate = new Date();
        const now = new Date();

        switch (query.period) {
          case 'week':
            startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
          case 'month':
            startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
          case '3months':
            startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
          case '6months':
            startDate = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000);
            break;
          case 'year':
            startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
            break;
        }
      }

      const weightLogs = await this.weightLogRepo.getWeightLogsByUserId(
        userId,
        startDate,
        endDate,
      );

      if (!weightLogs) {
        return this.helper.serviceResponse.successResponse({
          weightLogs: [],
          summary: {
            totalEntries: 0,
            latestWeight: 0,
            startingWeight: 0,
            weightChange: 0,
            averageWeight: 0,
          },
        });
      }

      const totalEntries = weightLogs.length;
      const latestWeight = totalEntries > 0 ? weightLogs[0].weight : 0;
      const startingWeight =
        totalEntries > 0 ? weightLogs[totalEntries - 1].weight : 0;
      const weightChange = latestWeight - startingWeight;
      const averageWeight =
        totalEntries > 0
          ? weightLogs.reduce((sum, log) => sum + log.weight, 0) / totalEntries
          : 0;

      const responseData = {
        weightLogs,
        summary: {
          totalEntries,
          latestWeight: parseFloat(latestWeight.toFixed(2)),
          startingWeight: parseFloat(startingWeight.toFixed(2)),
          weightChange: parseFloat(weightChange.toFixed(2)),
          averageWeight: parseFloat(averageWeight.toFixed(2)),
        },
      };

      return this.helper.serviceResponse.successResponse(responseData);
    } catch (error) {
      console.error('Error getting weight history:', error);
      throw new APIException(
        'Failed to get weight history',
        'WEIGHT_HISTORY_ERROR',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
