import { Injectable } from '@nestjs/common';
import { randomUUID } from 'crypto';
import {
  CreateAiDietHistoryRequestBody,
  CreateDietHistoryRequestBody,
  UpdateDietHistoryRequestBody,
} from 'models';
import { CustomFood } from 'src/entity/customFood';
import {
  CalorieSummary,
  ConsumedFood,
  DietHistory,
  DietHistoryResponseBody,
} from 'src/entity/dietHistory';
@Injectable()
export class DietHelperService {
  constructor() {}

  mapDietHistory(dietHistory: DietHistory): DietHistoryResponseBody {
    // 1 g protein = 4 Calories
    // 1 g Carbohydrate = 4 Calories
    // 1 g fat = 9 Calories

    let totalCarbIntakeInCalorie = 0;
    let totalProteinIntakeInCalorie = 0;
    let totalFatIntakeInCalorie = 0;

    let totalCarbIntakeInGram = 0;
    let totalProteinIntakeInGram = 0;
    let totalFatIntakeInGram = 0;

    const meal = {
      breakfast: [],
      lunch: [],
      dinner: [],
      snacks: [],
    };

    if (dietHistory?.consumedFoods?.length > 0) {
      const consumedFoods: ConsumedFood[] = dietHistory?.consumedFoods;
      consumedFoods.forEach((food) => {
        totalCarbIntakeInGram =
          totalCarbIntakeInGram + food?.carb * food?.servingSize;
        totalProteinIntakeInGram =
          totalProteinIntakeInGram + food?.protein * food?.servingSize;
        totalFatIntakeInGram =
          totalFatIntakeInGram + food?.fat * food?.servingSize;

        totalCarbIntakeInCalorie = totalCarbIntakeInGram * 4;
        totalProteinIntakeInCalorie = totalProteinIntakeInGram * 4;
        totalFatIntakeInCalorie = totalFatIntakeInGram * 9;
        food.calories = food.calories * food.servingSize;
        meal[food.mealType].push(food);
      });
    }

    const summary: CalorieSummary = {
      totalCarbIntakeInCalorie: parseFloat(totalCarbIntakeInCalorie.toFixed(2)),
      totalProteinIntakeInCalorie: parseFloat(
        totalProteinIntakeInCalorie.toFixed(2),
      ),
      totalFatIntakeInCalorie: parseFloat(totalFatIntakeInCalorie.toFixed(2)),
      totalCarbIntakeInGram: parseFloat(totalCarbIntakeInGram.toFixed(2)),
      totalProteinIntakeInGram: parseFloat(totalProteinIntakeInGram.toFixed(2)),
      totalFatIntakeInGram: parseFloat(totalFatIntakeInGram.toFixed(2)),
      totalCalorieConsumed: dietHistory?.totalCalorieConsumed || 0,
    };

    return {
      id: dietHistory?.id,
      userId: dietHistory?.userId,
      date: dietHistory?.date,
      waterConsumption: dietHistory?.waterConsumption,
      summary,
      breakfast: meal.breakfast,
      lunch: meal.lunch,
      dinner: meal.dinner,
      snacks: meal.snacks,
    };
  }

  getMacros(weight: number, caloriesIntakePerDay: number) {
    // 1 g protein = 4 Calories
    // 1 g Carbohydrate = 4 Calories
    // 1 g fat = 9 Calories
    //protein intake in gram = weignt in pound*0.825
    //fat is 25% of daily calorie intake
    //rest calories will go to carb

    const proteinIntake = weight * 0.825; //gram
    const fatIntake = (caloriesIntakePerDay * 0.25) / 9; //gram
    const carbIntakeInCalories =
      caloriesIntakePerDay - (fatIntake * 9 + proteinIntake * 4);
    const carbIntake = carbIntakeInCalories / 4;
    const macros = {
      consumePeriod: 'Daily',
      suggestedProteinInGram: parseFloat(proteinIntake.toFixed(2)),
      suggestedProteinInCalorie: parseFloat((proteinIntake * 4).toFixed(2)),
      suggestedFatInGram: parseFloat(fatIntake.toFixed(2)),
      suggestedFatInCalorie: parseFloat((fatIntake * 9).toFixed(2)),
      suggestedCarbInGram: parseFloat(carbIntake.toFixed(2)),
      suggestedCarbInCalorie: parseFloat(carbIntakeInCalories.toFixed(2)),
    };

    return macros;
  }

  formatFoodForAdding(
    food: CustomFood,
    dietHistory: CreateDietHistoryRequestBody | CreateAiDietHistoryRequestBody,
  ): ConsumedFood {
    return {
      name: food?.name,
      mealType: dietHistory?.mealType,
      calories: food?.calories,
      carb: food?.carb,
      protein: food?.protein,
      fat: food?.fat,
      source: dietHistory?.source,
      foodId: food?.id,
      servingSize: dietHistory.servingSize,
      id: randomUUID(),
    };
  }

  formatFoodForUpdating(
    food: CustomFood,
    consumedFood: UpdateDietHistoryRequestBody,
    existedConsumedFood: ConsumedFood,
  ): ConsumedFood {
    return {
      id: consumedFood?.consumedFoodId,
      mealType: consumedFood?.mealType || existedConsumedFood?.mealType,
      name: food?.name || existedConsumedFood?.name,
      calories: food?.calories || existedConsumedFood?.calories,
      carb: food?.carb || existedConsumedFood?.calories,
      protein: food?.protein || existedConsumedFood?.protein,
      fat: food?.fat || existedConsumedFood?.fat,
      source: consumedFood?.source || existedConsumedFood?.source,
      foodId: consumedFood?.foodId || existedConsumedFood?.foodId,
      servingSize:
        consumedFood?.servingSize || existedConsumedFood?.servingSize,
    };
  }
}
