import { Module } from '@nestjs/common';
import { NotificationHelperService } from '../notification/services/helper.notification.service';
import { TaskModule } from '../task/task.module';
import { UserRepository } from '../user/repositories';
import { FoodScannerModule } from './food-scanner-ai/food-scanner.module';
import { DietUserInfoProvider } from './providers';
import { DietRepository } from './repositories';
import { DietHelperRepository } from './repositories/diet.helper.repository';
import { FoodRepository } from './repositories/food.repository';
import { WeightLogRepository } from './repositories/weightLog.repository';
import { DietController } from './rest/diet.controller';
import { FoodController } from './rest/food.controller';
import { DietService } from './services';
import { DietHelperService } from './services/diet.helper.service';
import { FoodService } from './services/food.service';
import { WeightLogService } from './services/weightLog.service';

@Module({
  imports: [TaskModule, FoodScannerModule],
  controllers: [DietController, FoodController],
  providers: [
    DietService,
    DietRepository,
    DietHelperRepository,
    NotificationHelperService,
    UserRepository,
    DietHelperService,
    FoodService,
    FoodRepository,
    DietUserInfoProvider,
    WeightLogService,
    WeightLogRepository,
  ],
  exports: [DietUserInfoProvider, DietService],
})
export class DietModule {}
