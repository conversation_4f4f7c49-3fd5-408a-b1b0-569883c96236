import { Injectable } from '@nestjs/common';
import { ReminderRepository } from '../repositories/reminder.repository';
import { CreateReminderDto, UpdateReminderDto } from '../rest/dto/reminder.dto';
import { Reminder } from 'src/entity/reminder';
import { NotificationHelperService } from 'src/modules/notification/services/helper.notification.service';
import { QueueInstance } from 'src/queue-system';
import { NotificationQueueName, QueuePayloadType } from 'src/queue-system/predefined-data';
import { NotificationModule, NotificationType, NotificationCategory } from 'models';

@Injectable()
export class ReminderService {
  private readonly REMINDER_TOPIC = 'fitsomnia-reminders';

  constructor(
    private readonly reminderRepository: ReminderRepository,
    private readonly notificationHelperService: NotificationHelperService,
  ) {}

  async createReminder(
    adminId: string,
    createReminderDto: CreateReminderDto,
  ): Promise<Reminder> {
    try {
      const reminder = await this.reminderRepository.createReminder(adminId, createReminderDto);
      console.log(`✅ Reminder created successfully: "${reminder.title}" scheduled for ${reminder.time}`);
      return reminder;
    } catch (error) {
      console.error(`Failed to create reminder: ${error.message}`);
      throw error;
    }
  }

  async getAllReminders(): Promise<Reminder[]> {
    return this.reminderRepository.findAllReminders();
  }

  async getReminderById(id: string): Promise<Reminder> {
    return this.reminderRepository.findReminderById(id);
  }

  async updateReminder(
    id: string,
    updateReminderDto: UpdateReminderDto,
  ): Promise<Reminder> {
    try {
      const updatedReminder = await this.reminderRepository.updateReminder(id, updateReminderDto);
      console.log(`✅ Reminder updated successfully: "${updatedReminder.title}" scheduled for ${updatedReminder.time}`);
      return updatedReminder;
    } catch (error) {
      console.error(`Failed to update reminder with ID ${id}: ${error.message}`);
      throw error;
    }
  }

  async deleteReminder(id: string): Promise<boolean> {
    return this.reminderRepository.deleteReminder(id);
  }

  async sendReminderNotifications(): Promise<void> {
    try {
      // Get current time in HH:MM format
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now
        .getMinutes()
        .toString()
        .padStart(2, '0')}`;

      // Find all active reminders for the current time
      const activeReminders =
        await this.reminderRepository.findActiveRemindersForTime(currentTime);

      if (activeReminders.length === 0) {
        return;
      }



      // Get all active users for in-app notifications
      const activeUsers = await this.notificationHelperService.getAllActiveUsers();
      const allUserIds = activeUsers.map(user => user.id);

      // Send each reminder through the proper notification flow (generator → sender)
      for (const reminder of activeReminders) {
        try {
          // Create payload for the notification generator (this will save to database for in-app notifications)
          const generatorPayload = {
            targetUsers: allUserIds, // Send to all active users for in-app notifications
            title: reminder.title,
            content: reminder.message,
            module: NotificationModule.REMINDER,
            type: NotificationType.DAILY_REMINDER,
            category: NotificationCategory.DAILY_REMINDER,
            documentId: null, // Not needed for diet dashboard navigation
            saveDatabase: true, // This ensures notifications are saved for in-app notification panel
            body: reminder.message,
            topic: this.REMINDER_TOPIC,
            data: {
              module: 'REMINDER',
              type: NotificationType.DAILY_REMINDER, // Correct NotificationType enum value
              reminderType: reminder.reminderType, // Feature-specific reminder type (diet, leaderboard, etc.)
              title: reminder.title,
              body: reminder.message,
              documentId: null,
            },
            task: 'generator',
            payloadType: QueuePayloadType.DAILY_REMINDER_GENERATOR,
          };

          // Send to generator queue first (this will save to database and then forward to sender)
          const queueInstance = await QueueInstance;
          if (!queueInstance) {
            throw new Error('Queue instance is not available');
          }

          await queueInstance.sendPayload(
            NotificationQueueName.DAILY_REMINDER_GENERATOR_QUEUE,
            Buffer.from(JSON.stringify(generatorPayload)),
          );


        } catch (error) {
          console.error(`Failed to queue reminder "${reminder.title}":`, error.message);
        }
      }
    } catch (error) {
      console.error('Error sending reminder notifications:', error.message);
    }
  }

  /**
   * Subscribe all active users to reminder topic
   * This should be called during app initialization or periodically
   */
  async subscribeAllUsersToReminderTopic(): Promise<void> {
    try {
      await this.notificationHelperService.subscribeAllActiveUsersToReminderTopic();

    } catch (error) {
      console.error('Error subscribing users to reminder topic:', error.message);
    }
  }


}
