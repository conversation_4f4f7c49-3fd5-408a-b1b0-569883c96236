export interface Nutrients {
  ENERC_KCAL: number;
  PROCNT: number;
  FAT: number;
  CHOCDF: number;
  FIBTG?: number;
}

export interface Measure {
  label: string;
  weight: number;
  nutrients: Nutrients;
}

export interface GlobalFood {
  id: string;
  name: string;
  brand?: string;
  nutrients: Nutrients;
  measures: Measure[];
}

export interface WeightLog {
  id: string;
  userId: string;
  weight: number;
  weightType: string;
  date: Date;
  notes?: string;
  createdAt: Date;
}
