import { Injectable } from '@nestjs/common';
import {
  FcmSubscriptionResponse,
  FcmTopicResponse,
  IFcmService,
  IndividualFcmRequest,
  PushPriority,
  TopicFcmRequest,
} from './fcm.service.interface';
import * as admin from 'firebase-admin';
import { fireBaseConfig, getTopicName } from 'config/fcm';
import { v4 as uuidv4 } from 'uuid';
import { NotificationType } from 'models';

@Injectable()
export class FcmService implements IFcmService {
  constructor() {
    !admin.apps.length &&
      admin.initializeApp({
        credential: admin.credential.cert(
          JSON.parse(JSON.stringify(fireBaseConfig)),
        ),
      });
  }

  async sendToIndividual({
    token,
    title,
    body,
    documentId,
    data,
    isHighPriority,
    ttl,
  }: IndividualFcmRequest): Promise<boolean> {
    try {
      const payload = {
        notification: {
          title,
          body,
        },
        data: {
          ...data,
          documentId,
          title,
          body,
        },
        android: {
          // notification: {
          //   title,
          //   body,
          // },
          // Priority of the message. Must be either `normal` or `high`.
          priority: isHighPriority ? PushPriority.HIGH : PushPriority.NORMAL,
          collapseKey: '',
          ttl, // Time-to-live duration of the message in seconds.
        },
        apns: {
          headers: {
            'apns-expiration': ((new Date().getTime() + ttl) / 1000).toFixed(),
            'apns-priority': isHighPriority ? '10' : '5',
          },
        },
        token,
      };
      await admin.messaging().send(payload);
      return true;
    } catch (error) {
      return false;
    }
  }

  async sendToMany(
    tokens: string[],
    title: string,
    body: string,
    documentId: string,
    data: object,
    isHighPriority: boolean,
    ttl: number,
  ): Promise<boolean> {
    try {
      const payload = {
        notification: {
          title,
          body,
        },
        data: {
          ...data,
          documentId,
        },
        android: {
          notification: {
            title,
            body,
          },
          // Priority of the message. Must be either `normal` or `high`.
          priority: isHighPriority ? PushPriority.HIGH : PushPriority.NORMAL,
          collapseKey: '',
          ttl, // Time-to-live duration of the message in seconds.
        },
        apns: {
          headers: {
            'apns-expiration': ((new Date().getTime() + ttl) / 1000).toFixed(),
            'apns-priority': isHighPriority ? '10' : '5',
          },
        },
        tokens,
      };
      await admin.messaging().sendMulticast(payload);
      return true;
    } catch (error) {
      console.error('FCM sendToMany error:', error?.message || error);
      return false;
    }
  }

  async sendToTopic(fcmRequest: TopicFcmRequest): Promise<FcmTopicResponse> {
    const { title, body, documentId, data, topic, ttl = 600 } = fcmRequest;
    const environmentTopic = getTopicName(topic);

    try {
      // Validate topic
      if (
        !environmentTopic ||
        typeof environmentTopic !== 'string' ||
        !/^[a-zA-Z0-9-_.~%]+$/.test(environmentTopic)
      ) {
        throw new Error('Invalid topic name');
      }

      const stringifiedData = Object.fromEntries(
        Object.entries(data || {}).map(([key, value]) => [
          key,
          value === null || value === undefined ? '' : String(value),
        ]),
      );

      const notificationId = uuidv4();
      let notificationType = stringifiedData?.type as NotificationType;
      if (!Object.values(NotificationType).includes(notificationType)) {
        console.warn(
          `Invalid notification type: ${notificationType}, falling back to DAILY_REMINDER`,
        );
        notificationType = NotificationType.DAILY_REMINDER;
      }

      const payload = {
        notification: { title, body },
        data: {
          ...stringifiedData,
          documentId: documentId ?? '',
          notificationId,
          type: notificationType,
          title: title ?? '',
          body: body ?? '',
        },
        android: {
          notification: {
            title,
            body,
            sound: 'default',
            channelId: 'default',
            defaultSound: true,
            defaultVibrateTimings: true,
          },
          priority: PushPriority.HIGH,
          collapseKey: 'notification_collapse_key',
          ttl, // Configurable TTL
        },
        apns: {
          headers: {
            'apns-expiration': ((Date.now() + ttl * 1000) / 1000).toFixed(),
            'apns-priority': '10',
            'apns-push-type': 'alert',
          },
          payload: {
            aps: {
              alert: { title, body },
              sound: 'default',
              badge: 1,
              mutableContent: true,
              interruptionLevel: 'active',
              relevanceScore: 1.0,
            },
            ...stringifiedData,
          },
        },
        topic: environmentTopic,
      };

      const response = await admin.messaging().send(payload);
      return { messageId: response, topic: environmentTopic, success: true };
    } catch (error) {
      console.error('FCM sendToTopic error:', error?.message || error);
      const errorMessage = error?.message || 'Unknown error';
      return { success: false, error: errorMessage };
    }
  }

  async subscribeNotificationTopic(
    tokens: string | string[],
    topic: string,
  ): Promise<FcmSubscriptionResponse> {
    const environmentTopic = getTopicName(topic);
    try {
      const response = await admin
        .messaging()
        .subscribeToTopic(tokens, environmentTopic);
      const tokenCount = Array.isArray(tokens) ? tokens.length : 1;

      return {
        success: true,
        topic: environmentTopic,
        tokenCount,
        successCount: response.successCount,
        failureCount: response.failureCount,
        errors: response.errors,
      };
    } catch (error) {
      console.error(
        'FCM subscribeNotificationTopic error:',
        error?.message || error,
      );
      return { success: false, error: error?.message || error };
    }
  }

  async unsubscribeNotificationTopic(
    tokens: string | string[],
    topic: string,
  ): Promise<FcmSubscriptionResponse> {
    const environmentTopic = getTopicName(topic);
    try {
      const response = await admin
        .messaging()
        .unsubscribeFromTopic(tokens, environmentTopic);
      const tokenCount = Array.isArray(tokens) ? tokens.length : 1;

      return {
        success: true,
        topic: environmentTopic,
        tokenCount,
        successCount: response.successCount,
        failureCount: response.failureCount,
        errors: response.errors,
      };
    } catch (error) {
      console.error(
        'FCM unsubscribeNotificationTopic error:',
        error?.message || error,
      );
      return { success: false, error: error?.message || error };
    }
  }
}
