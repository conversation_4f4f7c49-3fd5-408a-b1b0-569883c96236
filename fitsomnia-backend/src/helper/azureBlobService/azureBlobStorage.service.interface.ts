/**
 * Interface for Azure Blob Storage Service
 * Extends the base IS3FileUploadService interface for backward compatibility
 * while adding Azure-specific functionality
 */
export abstract class IAzureBlobStorageService {

  abstract generateFileKey: (
    userId: string,
    fileName: string,
    featureName?: string,
  ) => string;
  
  abstract generateFileKeyWithRoot: (
    userId: string,
    fileName: string,
    root: string,
    featureName?: string,
  ) => string;
  
  abstract getFileKey: (url: string, featureName: string) => string;


  abstract generateCdnPresignedUrl(
    url: string,
    featureName: string,
    fileName?: string,
  ): Promise<string | null>;

  abstract generatePreSignedUrl: (
    url: string,
    featureName: string,
    fileName?: string,
    type?: string,
  ) => Promise<string | null>;

  abstract uploadToAzureBlob: (
    file: Express.Multer.File,
    key: string,
    isPublic?: boolean,
  ) => Promise<string | null>;

  /**
   * Gets a BlockBlobClient for operations on a specific blob
   * @param containerName Name of the container
   * @param blobName Name/key of the blob
   * @returns BlockBlobClient instance
   */
  abstract getBlockBlobClient(containerName: string, blobName: string): any;
}
