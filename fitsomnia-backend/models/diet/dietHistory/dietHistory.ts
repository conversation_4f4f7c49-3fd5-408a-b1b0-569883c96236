export enum MealType {
  LUNCH = 'lunch',
  BREAKFAST = 'breakfast',
  DINNER = 'dinner',
  SNACKS = 'snacks',
}

export enum FoodSource {
  GLOBAL = 'global',
  CUSTOM = 'custom',
  LOCAL = 'local',
  AI = 'ai',
}

export interface ConsumedFood {
  id: string;
  foodId?: string;
  name: string;
  servingSize: number;
  calories: number;
  fat: number;
  carb: number;
  protein: number;
  source: FoodSource;
  mealType: MealType;
}

export interface DietHistory {
  id: string;
  userId: string;
  date: Date;
  consumedFoods?: ConsumedFood[];
  totalCalorieConsumed?: number;
  waterConsumption?: number;
}

export interface CalorieSummary {
  totalCalorieConsumed: number;
  totalCarbIntakeInCalorie: number;
  totalProteinIntakeInCalorie: number;
  totalFatIntakeInCalorie: number;
  totalCarbIntakeInGram: number;
  totalProteinIntakeInGram: number;
  totalFatIntakeInGram: number;
}
export interface DietHistoryResponseBody {
  id: string;
  userId: string;
  date: Date;
  breakfast: ConsumedFood[];
  lunch: ConsumedFood[];
  dinner: ConsumedFood[];
  snacks: ConsumedFood[];
  waterConsumption?: number;
  summary: CalorieSummary;
}
